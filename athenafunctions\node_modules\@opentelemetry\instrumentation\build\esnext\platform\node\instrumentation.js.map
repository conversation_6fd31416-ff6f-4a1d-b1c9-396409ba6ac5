{"version": 3, "file": "instrumentation.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/instrumentation.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAGH,OAAO,KAAK,IAAI,MAAM,MAAM,CAAC;AAC7B,OAAO,EAAE,KAAK,IAAI,SAAS,EAAE,MAAM,MAAM,CAAC;AAC1C,OAAO,EAAE,SAAS,EAAE,MAAM,QAAQ,CAAC;AACnC,OAAO,EAAE,IAAI,EAAE,MAAM,EAAwB,MAAM,SAAS,CAAC;AAC7D,OAAO,EAAE,uBAAuB,EAAE,MAAM,uBAAuB,CAAC;AAChE,OAAO,EACL,2BAA2B,GAE5B,MAAM,+BAA+B,CAAC;AAEvC,OAAO,EAAE,IAAI,IAAI,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAK1D,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAE1C,OAAO,EAAE,IAAI,IAAI,WAAW,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,YAAY,EAAE,MAAM,IAAI,CAAC;AAClC,OAAO,EAAE,SAAS,EAAE,MAAM,aAAa,CAAC;AAExC;;GAEG;AACH,MAAM,OAAgB,mBAGpB,SAAQ,uBAAmC;IAS3C,YACE,mBAA2B,EAC3B,sBAA8B,EAC9B,MAAkB;QAElB,KAAK,CAAC,mBAAmB,EAAE,sBAAsB,EAAE,MAAM,CAAC,CAAC;QAVrD,WAAM,GAA6B,EAAE,CAAC;QACtC,iCAA4B,GAClC,2BAA2B,CAAC,WAAW,EAAE,CAAC;QACpC,aAAQ,GAAG,KAAK,CAAC;QAsBN,UAAK,GAAgB,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;YACvE,IAAI,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC,EAAE;gBAClC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;aACnC;YACD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBACrC,OAAO,IAAI,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;aAC3C;iBAAM;gBACL,MAAM,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBACtE,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,IAAI,EAAE;oBACzC,KAAK,EAAE,OAAO;iBACf,CAAC,CAAC;gBACH,OAAO,OAAO,CAAC;aAChB;QACH,CAAC,CAAC;QAEiB,YAAO,GAAkB,CAAC,aAAa,EAAE,IAAI,EAAE,EAAE;YAClE,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBACrC,OAAO,MAAM,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;aACpC;iBAAM;gBACL,OAAO,MAAM,CAAC,cAAc,CAAC,aAAa,EAAE,IAAI,EAAE;oBAChD,KAAK,EAAE,aAAa,CAAC,IAAI,CAAC;iBAC3B,CAAC,CAAC;aACJ;QACH,CAAC,CAAC;QAEiB,cAAS,GAAoB,CAC9C,kBAAkB,EAClB,KAAK,EACL,OAAO,EACP,EAAE;YACF,IAAI,CAAC,kBAAkB,EAAE;gBACvB,IAAI,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;gBACxD,OAAO;aACR;iBAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC7C,kBAAkB,GAAG,CAAC,kBAAkB,CAAC,CAAC;aAC3C;YAED,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpC,IAAI,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;gBACpE,OAAO;aACR;YAED,kBAAkB,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBACzC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,CAAC,KAAK,CAAC,aAAa,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAC3C,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAEiB,gBAAW,GAAsB,CAClD,kBAAkB,EAClB,KAAK,EACL,EAAE;YACF,IAAI,CAAC,kBAAkB,EAAE;gBACvB,IAAI,CAAC,KAAK,CAAC,2CAA2C,CAAC,CAAC;gBACxD,OAAO;aACR;iBAAM,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,kBAAkB,CAAC,EAAE;gBAC7C,kBAAkB,GAAG,CAAC,kBAAkB,CAAC,CAAC;aAC3C;YAED,IAAI,CAAC,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,EAAE;gBACpC,IAAI,CAAC,KAAK,CAAC,uDAAuD,CAAC,CAAC;gBACpE,OAAO;aACR;YAED,kBAAkB,CAAC,OAAO,CAAC,aAAa,CAAC,EAAE;gBACzC,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACnB,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,CAAC;gBACpC,CAAC,CAAC,CAAC;YACL,CAAC,CAAC,CAAC;QACL,CAAC,CAAC;QAnFA,IAAI,OAAO,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC;QAE1B,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;YACtC,OAAO,GAAG,CAAC,OAAO,CAAC,CAAC;SACrB;QAED,IAAI,CAAC,QAAQ,GAAI,OAA6C,IAAI,EAAE,CAAC;QAErE,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;YACxB,IAAI,CAAC,MAAM,EAAE,CAAC;SACf;IACH,CAAC;IA0EO,uBAAuB;QAC7B,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,MAAuC,EAAE,EAAE;YAChE,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,CAAC;YACxB,IAAI;gBACF,MAAM,cAAc,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;gBAC7C,IAAI,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,EAAE;oBACjC,gFAAgF;oBAChF,IAAI,CAAC,KAAK,CAAC,IAAI,CACb,UAAU,IAAI,2BAA2B,IAAI,CAAC,mBAAmB,gEAAgE,IAAI,EAAE,CACxI,CAAC;iBACH;aACF;YAAC,WAAM;gBACN,6CAA6C;aAC9C;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,sBAAsB,CAAC,OAAe;QAC5C,IAAI;YACF,MAAM,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE;gBAC5D,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;YACH,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;YACzC,OAAO,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS,CAAC;SAC1D;QAAC,OAAO,KAAK,EAAE;YACd,IAAI,CAAC,IAAI,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC;SACjD;QAED,OAAO,SAAS,CAAC;IACnB,CAAC;IAEO,UAAU,CAChB,MAAuC,EACvC,OAAU,EACV,IAAY,EACZ,OAAuB;;QAEvB,IAAI,CAAC,OAAO,EAAE;YACZ,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;gBACtC,MAAM,CAAC,aAAa,GAAG,OAAO,CAAC;gBAC/B,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACjB,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,uEAAuE,EACvE;wBACE,MAAM,EAAE,MAAM,CAAC,IAAI;qBACpB,CACF,CAAC;oBACF,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;iBAC9B;aACF;YACD,OAAO,OAAO,CAAC;SAChB;QAED,MAAM,OAAO,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;QACrD,MAAM,CAAC,aAAa,GAAG,OAAO,CAAC;QAC/B,IAAI,MAAM,CAAC,IAAI,KAAK,IAAI,EAAE;YACxB,cAAc;YACd,IACE,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,OAAO,EAAE,MAAM,CAAC,iBAAiB,CAAC,EACxE;gBACA,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU,EAAE;oBACtC,MAAM,CAAC,aAAa,GAAG,OAAO,CAAC;oBAC/B,IAAI,IAAI,CAAC,QAAQ,EAAE;wBACjB,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,2DAA2D,EAC3D;4BACE,MAAM,EAAE,MAAM,CAAC,IAAI;4BACnB,OAAO,EAAE,MAAM,CAAC,aAAa;4BAC7B,OAAO;yBACR,CACF,CAAC;wBACF,OAAO,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;qBACpD;iBACF;aACF;YACD,OAAO,OAAO,CAAC;SAChB;QACD,gBAAgB;QAChB,MAAM,KAAK,GAAG,MAAA,MAAM,CAAC,KAAK,mCAAI,EAAE,CAAC;QACjC,MAAM,cAAc,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;QAC5C,MAAM,6BAA6B,GAAG,KAAK;aACxC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,cAAc,CAAC;aACtC,MAAM,CAAC,CAAC,CAAC,EAAE,CACV,WAAW,CAAC,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,MAAM,CAAC,iBAAiB,CAAC,CACpE,CAAC;QACJ,OAAO,6BAA6B,CAAC,MAAM,CAAI,CAAC,cAAc,EAAE,IAAI,EAAE,EAAE;YACtE,IAAI,CAAC,aAAa,GAAG,cAAc,CAAC;YACpC,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACjB,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,uEAAuE,EACvE;oBACE,MAAM,EAAE,MAAM,CAAC,IAAI;oBACnB,OAAO,EAAE,MAAM,CAAC,aAAa;oBAC7B,QAAQ,EAAE,IAAI,CAAC,IAAI;oBACnB,OAAO;iBACR,CACF,CAAC;gBAEF,oEAAoE;gBACpE,OAAO,IAAI,CAAC,KAAK,CAAC,cAAc,EAAE,MAAM,CAAC,aAAa,CAAM,CAAC;aAC9D;YACD,OAAO,cAAc,CAAC;QACxB,CAAC,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAEM,MAAM;QACX,IAAI,IAAI,CAAC,QAAQ,EAAE;YACjB,OAAO;SACR;QACD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,wCAAwC;QACxC,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE;YAC1B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;gBAClC,IAAI,OAAO,MAAM,CAAC,KAAK,KAAK,UAAU,IAAI,MAAM,CAAC,aAAa,EAAE;oBAC9D,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,6EAA6E,EAC7E;wBACE,MAAM,EAAE,MAAM,CAAC,IAAI;wBACnB,OAAO,EAAE,MAAM,CAAC,aAAa;qBAC9B,CACF,CAAC;oBACF,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;iBAC1D;gBACD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;oBAC/B,IAAI,IAAI,CAAC,aAAa,EAAE;wBACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,kFAAkF,EAClF;4BACE,MAAM,EAAE,MAAM,CAAC,IAAI;4BACnB,OAAO,EAAE,MAAM,CAAC,aAAa;4BAC7B,QAAQ,EAAE,IAAI,CAAC,IAAI;yBACpB,CACF,CAAC;wBACF,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;qBACtD;iBACF;aACF;YACD,OAAO;SACR;QAED,IAAI,CAAC,uBAAuB,EAAE,CAAC;QAC/B,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC,MAAM,MAAM,GAAW,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;gBAChD,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;oBACrC,MAAM,UAAU,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;oBACpC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;oBACvB,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC;iBAC1B;gBACD,OAAO,IAAI,CAAC,UAAU,CAAiB,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACzE,CAAC,CAAC;YACF,MAAM,SAAS,GAAgB,CAAC,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,EAAE;gBACxD,OAAO,IAAI,CAAC,UAAU,CAAiB,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;YACzE,CAAC,CAAC;YAEF,iEAAiE;YACjE,mEAAmE;YACnE,gCAAgC;YAChC,MAAM,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC;gBACvC,CAAC,CAAC,IAAI,WAAW,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,EAAE,SAAS,CAAC;gBAChE,CAAC,CAAC,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;YAEvE,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YACvB,MAAM,OAAO,GAAG,IAAI,UAAU,CAC5B,CAAC,MAAM,CAAC,IAAI,CAAC,EACb,EAAE,SAAS,EAAE,KAAK,EAAE,EACZ,MAAM,CACf,CAAC;YACF,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;SAC3B;IACH,CAAC;IAEM,OAAO;QACZ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAClB,OAAO;SACR;QACD,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,KAAK,MAAM,MAAM,IAAI,IAAI,CAAC,QAAQ,EAAE;YAClC,IAAI,OAAO,MAAM,CAAC,OAAO,KAAK,UAAU,IAAI,MAAM,CAAC,aAAa,EAAE;gBAChE,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,8EAA8E,EAC9E;oBACE,MAAM,EAAE,MAAM,CAAC,IAAI;oBACnB,OAAO,EAAE,MAAM,CAAC,aAAa;iBAC9B,CACF,CAAC;gBACF,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;aAC5D;YACD,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE;gBAC/B,IAAI,IAAI,CAAC,aAAa,EAAE;oBACtB,IAAI,CAAC,KAAK,CAAC,KAAK,CACd,mFAAmF,EACnF;wBACE,MAAM,EAAE,MAAM,CAAC,IAAI;wBACnB,OAAO,EAAE,MAAM,CAAC,aAAa;wBAC7B,QAAQ,EAAE,IAAI,CAAC,IAAI;qBACpB,CACF,CAAC;oBACF,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,CAAC;iBACxD;aACF;SACF;IACH,CAAC;IAEM,SAAS;QACd,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;CACF;AAED,SAAS,WAAW,CAClB,iBAA2B,EAC3B,OAAgB,EAChB,iBAA2B;IAE3B,IAAI,OAAO,OAAO,KAAK,WAAW,EAAE;QAClC,8DAA8D;QAC9D,OAAO,iBAAiB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;KACxC;IAED,OAAO,iBAAiB,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE;QAC/C,OAAO,SAAS,CAAC,OAAO,EAAE,gBAAgB,EAAE,EAAE,iBAAiB,EAAE,CAAC,CAAC;IACrE,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as types from '../../types';\nimport * as path from 'path';\nimport { types as utilTypes } from 'util';\nimport { satisfies } from 'semver';\nimport { wrap, unwrap, massWrap, massUnwrap } from 'shimmer';\nimport { InstrumentationAbstract } from '../../instrumentation';\nimport {\n  RequireInTheMiddleSingleton,\n  Hooked,\n} from './RequireInTheMiddleSingleton';\nimport type { HookFn } from 'import-in-the-middle';\nimport { Hook as HookImport } from 'import-in-the-middle';\nimport {\n  InstrumentationConfig,\n  InstrumentationModuleDefinition,\n} from '../../types';\nimport { diag } from '@opentelemetry/api';\nimport type { OnRequireFn } from 'require-in-the-middle';\nimport { Hook as HookRequire } from 'require-in-the-middle';\nimport { readFileSync } from 'fs';\nimport { isWrapped } from '../../utils';\n\n/**\n * Base abstract class for instrumenting node plugins\n */\nexport abstract class InstrumentationBase<\n    ConfigType extends InstrumentationConfig = InstrumentationConfig,\n  >\n  extends InstrumentationAbstract<ConfigType>\n  implements types.Instrumentation<ConfigType>\n{\n  private _modules: InstrumentationModuleDefinition[];\n  private _hooks: (Hooked | HookRequire)[] = [];\n  private _requireInTheMiddleSingleton: RequireInTheMiddleSingleton =\n    RequireInTheMiddleSingleton.getInstance();\n  private _enabled = false;\n\n  constructor(\n    instrumentationName: string,\n    instrumentationVersion: string,\n    config: ConfigType\n  ) {\n    super(instrumentationName, instrumentationVersion, config);\n\n    let modules = this.init();\n\n    if (modules && !Array.isArray(modules)) {\n      modules = [modules];\n    }\n\n    this._modules = (modules as InstrumentationModuleDefinition[]) || [];\n\n    if (this._config.enabled) {\n      this.enable();\n    }\n  }\n\n  protected override _wrap: typeof wrap = (moduleExports, name, wrapper) => {\n    if (isWrapped(moduleExports[name])) {\n      this._unwrap(moduleExports, name);\n    }\n    if (!utilTypes.isProxy(moduleExports)) {\n      return wrap(moduleExports, name, wrapper);\n    } else {\n      const wrapped = wrap(Object.assign({}, moduleExports), name, wrapper);\n      Object.defineProperty(moduleExports, name, {\n        value: wrapped,\n      });\n      return wrapped;\n    }\n  };\n\n  protected override _unwrap: typeof unwrap = (moduleExports, name) => {\n    if (!utilTypes.isProxy(moduleExports)) {\n      return unwrap(moduleExports, name);\n    } else {\n      return Object.defineProperty(moduleExports, name, {\n        value: moduleExports[name],\n      });\n    }\n  };\n\n  protected override _massWrap: typeof massWrap = (\n    moduleExportsArray,\n    names,\n    wrapper\n  ) => {\n    if (!moduleExportsArray) {\n      diag.error('must provide one or more modules to patch');\n      return;\n    } else if (!Array.isArray(moduleExportsArray)) {\n      moduleExportsArray = [moduleExportsArray];\n    }\n\n    if (!(names && Array.isArray(names))) {\n      diag.error('must provide one or more functions to wrap on modules');\n      return;\n    }\n\n    moduleExportsArray.forEach(moduleExports => {\n      names.forEach(name => {\n        this._wrap(moduleExports, name, wrapper);\n      });\n    });\n  };\n\n  protected override _massUnwrap: typeof massUnwrap = (\n    moduleExportsArray,\n    names\n  ) => {\n    if (!moduleExportsArray) {\n      diag.error('must provide one or more modules to patch');\n      return;\n    } else if (!Array.isArray(moduleExportsArray)) {\n      moduleExportsArray = [moduleExportsArray];\n    }\n\n    if (!(names && Array.isArray(names))) {\n      diag.error('must provide one or more functions to wrap on modules');\n      return;\n    }\n\n    moduleExportsArray.forEach(moduleExports => {\n      names.forEach(name => {\n        this._unwrap(moduleExports, name);\n      });\n    });\n  };\n\n  private _warnOnPreloadedModules(): void {\n    this._modules.forEach((module: InstrumentationModuleDefinition) => {\n      const { name } = module;\n      try {\n        const resolvedModule = require.resolve(name);\n        if (require.cache[resolvedModule]) {\n          // Module is already cached, which means the instrumentation hook might not work\n          this._diag.warn(\n            `Module ${name} has been loaded before ${this.instrumentationName} so it might not work, please initialize it before requiring ${name}`\n          );\n        }\n      } catch {\n        // Module isn't available, we can simply skip\n      }\n    });\n  }\n\n  private _extractPackageVersion(baseDir: string): string | undefined {\n    try {\n      const json = readFileSync(path.join(baseDir, 'package.json'), {\n        encoding: 'utf8',\n      });\n      const version = JSON.parse(json).version;\n      return typeof version === 'string' ? version : undefined;\n    } catch (error) {\n      diag.warn('Failed extracting version', baseDir);\n    }\n\n    return undefined;\n  }\n\n  private _onRequire<T>(\n    module: InstrumentationModuleDefinition,\n    exports: T,\n    name: string,\n    baseDir?: string | void\n  ): T {\n    if (!baseDir) {\n      if (typeof module.patch === 'function') {\n        module.moduleExports = exports;\n        if (this._enabled) {\n          this._diag.debug(\n            'Applying instrumentation patch for nodejs core module on require hook',\n            {\n              module: module.name,\n            }\n          );\n          return module.patch(exports);\n        }\n      }\n      return exports;\n    }\n\n    const version = this._extractPackageVersion(baseDir);\n    module.moduleVersion = version;\n    if (module.name === name) {\n      // main module\n      if (\n        isSupported(module.supportedVersions, version, module.includePrerelease)\n      ) {\n        if (typeof module.patch === 'function') {\n          module.moduleExports = exports;\n          if (this._enabled) {\n            this._diag.debug(\n              'Applying instrumentation patch for module on require hook',\n              {\n                module: module.name,\n                version: module.moduleVersion,\n                baseDir,\n              }\n            );\n            return module.patch(exports, module.moduleVersion);\n          }\n        }\n      }\n      return exports;\n    }\n    // internal file\n    const files = module.files ?? [];\n    const normalizedName = path.normalize(name);\n    const supportedFileInstrumentations = files\n      .filter(f => f.name === normalizedName)\n      .filter(f =>\n        isSupported(f.supportedVersions, version, module.includePrerelease)\n      );\n    return supportedFileInstrumentations.reduce<T>((patchedExports, file) => {\n      file.moduleExports = patchedExports;\n      if (this._enabled) {\n        this._diag.debug(\n          'Applying instrumentation patch for nodejs module file on require hook',\n          {\n            module: module.name,\n            version: module.moduleVersion,\n            fileName: file.name,\n            baseDir,\n          }\n        );\n\n        // patch signature is not typed, so we cast it assuming it's correct\n        return file.patch(patchedExports, module.moduleVersion) as T;\n      }\n      return patchedExports;\n    }, exports);\n  }\n\n  public enable(): void {\n    if (this._enabled) {\n      return;\n    }\n    this._enabled = true;\n\n    // already hooked, just call patch again\n    if (this._hooks.length > 0) {\n      for (const module of this._modules) {\n        if (typeof module.patch === 'function' && module.moduleExports) {\n          this._diag.debug(\n            'Applying instrumentation patch for nodejs module on instrumentation enabled',\n            {\n              module: module.name,\n              version: module.moduleVersion,\n            }\n          );\n          module.patch(module.moduleExports, module.moduleVersion);\n        }\n        for (const file of module.files) {\n          if (file.moduleExports) {\n            this._diag.debug(\n              'Applying instrumentation patch for nodejs module file on instrumentation enabled',\n              {\n                module: module.name,\n                version: module.moduleVersion,\n                fileName: file.name,\n              }\n            );\n            file.patch(file.moduleExports, module.moduleVersion);\n          }\n        }\n      }\n      return;\n    }\n\n    this._warnOnPreloadedModules();\n    for (const module of this._modules) {\n      const hookFn: HookFn = (exports, name, baseDir) => {\n        if (!baseDir && path.isAbsolute(name)) {\n          const parsedPath = path.parse(name);\n          name = parsedPath.name;\n          baseDir = parsedPath.dir;\n        }\n        return this._onRequire<typeof exports>(module, exports, name, baseDir);\n      };\n      const onRequire: OnRequireFn = (exports, name, baseDir) => {\n        return this._onRequire<typeof exports>(module, exports, name, baseDir);\n      };\n\n      // `RequireInTheMiddleSingleton` does not support absolute paths.\n      // For an absolute paths, we must create a separate instance of the\n      // require-in-the-middle `Hook`.\n      const hook = path.isAbsolute(module.name)\n        ? new HookRequire([module.name], { internals: true }, onRequire)\n        : this._requireInTheMiddleSingleton.register(module.name, onRequire);\n\n      this._hooks.push(hook);\n      const esmHook = new HookImport(\n        [module.name],\n        { internals: false },\n        <HookFn>hookFn\n      );\n      this._hooks.push(esmHook);\n    }\n  }\n\n  public disable(): void {\n    if (!this._enabled) {\n      return;\n    }\n    this._enabled = false;\n\n    for (const module of this._modules) {\n      if (typeof module.unpatch === 'function' && module.moduleExports) {\n        this._diag.debug(\n          'Removing instrumentation patch for nodejs module on instrumentation disabled',\n          {\n            module: module.name,\n            version: module.moduleVersion,\n          }\n        );\n        module.unpatch(module.moduleExports, module.moduleVersion);\n      }\n      for (const file of module.files) {\n        if (file.moduleExports) {\n          this._diag.debug(\n            'Removing instrumentation patch for nodejs module file on instrumentation disabled',\n            {\n              module: module.name,\n              version: module.moduleVersion,\n              fileName: file.name,\n            }\n          );\n          file.unpatch(file.moduleExports, module.moduleVersion);\n        }\n      }\n    }\n  }\n\n  public isEnabled(): boolean {\n    return this._enabled;\n  }\n}\n\nfunction isSupported(\n  supportedVersions: string[],\n  version?: string,\n  includePrerelease?: boolean\n): boolean {\n  if (typeof version === 'undefined') {\n    // If we don't have the version, accept the wildcard case only\n    return supportedVersions.includes('*');\n  }\n\n  return supportedVersions.some(supportedVersion => {\n    return satisfies(version, supportedVersion, { includePrerelease });\n  });\n}\n"]}