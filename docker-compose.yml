version: "3.8"

services:
  redis:
    image: redis:7
    container_name: inngest_redis
    ports:
      - "6379:6379"

  inngest:
    build:
      context: .
      dockerfile: ./inngest-runtime/Dockerfile
    container_name: inngest_athenafunctions_runtime
    ports:
      - "3000:3000"  # Next.js app (athenafunctions)
      - "8288:8288"  # Inngest runtime
    depends_on:
      - redis
    environment:
      REDIS_URL: redis://redis:6379
      DEBUG: "*"
      # Next.js environment variables
      NODE_ENV: development
    env_file:
      - .env
      
