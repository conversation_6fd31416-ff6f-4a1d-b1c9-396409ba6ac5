version: "3.8"

services:
  redis:
    image: redis:7
    container_name: inngest_redis
    ports:
      - "6379:6379"

  inngest:
    build:
      context: ./inngest-runtime
    container_name: inngest_runtime
    ports:
      - "8288:8288"
    depends_on:
      - redis
    environment:
      REDIS_URL: redis://redis:6379
      DEBUG: "*"
      INNGEST_FUNCTION_SERVER_URL: "http://host.docker.internal:3000/api/inngest" # Use host.docker.internal to access the host machine from the container as https://localhost:3000 ius not accessible from the container. This is a separate project from this server. It could be a Next.js app that hosts the inngest functions.
    env_file:
      - .env
      
