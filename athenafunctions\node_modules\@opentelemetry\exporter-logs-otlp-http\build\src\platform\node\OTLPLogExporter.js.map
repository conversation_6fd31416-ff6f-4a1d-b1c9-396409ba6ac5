{"version": 3, "file": "OTLPLogExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPLogExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAOH,0EAAqE;AACrE,sEAAqE;AACrE,2CAAwC;AACxC,2EAGqD;AAErD;;GAEG;AACH,MAAa,eACX,SAAQ,qCAAqC;IAG7C,YAAY,SAAqC,EAAE;QACjD,KAAK,CACH,IAAA,wCAA4B,EAC1B,IAAA,oCAAwB,EAAC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE;YAClD,YAAY,EAAE,iCAAiC,iBAAO,EAAE;YACxD,cAAc,EAAE,kBAAkB;SACnC,CAAC,EACF,qCAAkB,CACnB,CACF,CAAC;IACJ,CAAC;CACF;AAfD,0CAeC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type {\n  ReadableLogRecord,\n  LogRecordExporter,\n} from '@opentelemetry/sdk-logs';\nimport type { OTLPExporterNodeConfigBase } from '@opentelemetry/otlp-exporter-base';\nimport { OTLPExporterBase } from '@opentelemetry/otlp-exporter-base';\nimport { JsonLogsSerializer } from '@opentelemetry/otlp-transformer';\nimport { VERSION } from '../../version';\nimport {\n  convertLegacyHttpOptions,\n  createOtlpHttpExportDelegate,\n} from '@opentelemetry/otlp-exporter-base/node-http';\n\n/**\n * Collector Logs Exporter for Node\n */\nexport class OTLPLogExporter\n  extends OTLPExporterBase<ReadableLogRecord[]>\n  implements LogRecordExporter\n{\n  constructor(config: OTLPExporterNodeConfigBase = {}) {\n    super(\n      createOtlpHttpExportDelegate(\n        convertLegacyHttpOptions(config, 'LOGS', 'v1/logs', {\n          'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n          'Content-Type': 'application/json',\n        }),\n        JsonLogsSerializer\n      )\n    );\n  }\n}\n"]}