{"version": 3, "file": "logs.js", "sourceRoot": "", "sources": ["../../../../src/logs/protobuf/logs.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AACH,OAAO,KAAK,IAAI,MAAM,sBAAsB,CAAC;AAK7C,OAAO,EAAE,8BAA8B,EAAE,MAAM,aAAa,CAAC;AAK7D,IAAM,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;KAChE,yBAAmE,CAAC;AAEvE,IAAM,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE;KAC/D,wBAAiE,CAAC;AAErE;;GAEG;AACH,MAAM,CAAC,IAAM,sBAAsB,GAG/B;IACF,gBAAgB,EAAE,UAAC,GAAwB;QACzC,IAAM,OAAO,GAAG,8BAA8B,CAAC,GAAG,CAAC,CAAC;QACpD,OAAO,eAAe,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,CAAC;IAClD,CAAC;IACD,mBAAmB,EAAE,UAAC,GAAe;QACnC,OAAO,gBAAgB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;IACtC,CAAC;CACF,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\nimport * as root from '../../generated/root';\n\nimport { IExportLogsServiceRequest } from '../internal-types';\nimport { IExportLogsServiceResponse } from '../export-response';\n\nimport { createExportLogsServiceRequest } from '../internal';\nimport { ReadableLogRecord } from '@opentelemetry/sdk-logs';\nimport { ExportType } from '../../common/protobuf/protobuf-export-type';\nimport { ISerializer } from '../../i-serializer';\n\nconst logsResponseType = root.opentelemetry.proto.collector.logs.v1\n  .ExportLogsServiceResponse as ExportType<IExportLogsServiceResponse>;\n\nconst logsRequestType = root.opentelemetry.proto.collector.logs.v1\n  .ExportLogsServiceRequest as ExportType<IExportLogsServiceRequest>;\n\n/*\n * @experimental this serializer may receive breaking changes in minor versions, pin this package's version when using this constant\n */\nexport const ProtobufLogsSerializer: ISerializer<\n  ReadableLogRecord[],\n  IExportLogsServiceResponse\n> = {\n  serializeRequest: (arg: ReadableLogRecord[]) => {\n    const request = createExportLogsServiceRequest(arg);\n    return logsRequestType.encode(request).finish();\n  },\n  deserializeResponse: (arg: Uint8Array) => {\n    return logsResponseType.decode(arg);\n  },\n};\n"]}