{"version": 3, "file": "http-exporter-transport.js", "sourceRoot": "", "sources": ["../../../src/transport/http-exporter-transport.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAcH,MAAM,qBAAqB;IAIzB,YAAoB,WAAkC;QAAlC,gBAAW,GAAX,WAAW,CAAuB;QAH9C,UAAK,GAAwB,IAAI,CAAC;QAClC,WAAM,GAAoC,IAAI,CAAC;IAEE,CAAC;IAE1D,KAAK,CAAC,IAAI,CAAC,IAAgB,EAAE,aAAqB;QAChD,IAAI,IAAI,CAAC,KAAK,IAAI,IAAI,EAAE;YACtB,8FAA8F;YAC9F,MAAM,EACJ,YAAY,EACZ,eAAe;YACf,8DAA8D;cAC/D,GAAG,OAAO,CAAC,wBAAwB,CAAC,CAAC;YACtC,IAAI,CAAC,MAAM,GAAG,eAAe,CAC3B,IAAI,CAAC,WAAW,CAAC,GAAG,EACpB,IAAI,CAAC,WAAW,CAAC,YAAY,CAC9B,CAAC;YACF,IAAI,CAAC,KAAK,GAAG,YAAY,CAAC;SAC3B;QAED,OAAO,IAAI,OAAO,CAAiB,OAAO,CAAC,EAAE;;YAC3C,8BAA8B;YAC9B,oEAAoE;YACpE,MAAA,IAAI,CAAC,KAAK,+CAAV,IAAI,EACF,IAAI,CAAC,WAAW,EAChB,IAAI,CAAC,MAAO,EACZ,IAAI,EACJ,MAAM,CAAC,EAAE;gBACP,OAAO,CAAC,MAAM,CAAC,CAAC;YAClB,CAAC,EACD,aAAa,CACd,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IACD,QAAQ;QACN,2CAA2C;IAC7C,CAAC;CACF;AAED,MAAM,UAAU,2BAA2B,CACzC,UAAiC;IAEjC,OAAO,IAAI,qBAAqB,CAAC,UAAU,CAAC,CAAC;AAC/C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type {\n  HttpRequestParameters,\n  sendWithHttp,\n} from './http-transport-types';\n\n// NOTE: do not change these type imports to actual imports. Doing so WILL break `@opentelemetry/instrumentation-http`,\n// as they'd be imported before the http/https modules can be wrapped.\nimport type * as https from 'https';\nimport type * as http from 'http';\nimport { ExportResponse } from '../export-response';\nimport { IExporterTransport } from '../exporter-transport';\n\nclass HttpExporterTransport implements IExporterTransport {\n  private _send: sendWithHttp | null = null;\n  private _agent: http.Agent | https.Agent | null = null;\n\n  constructor(private _parameters: HttpRequestParameters) {}\n\n  async send(data: Uint8Array, timeoutMillis: number): Promise<ExportResponse> {\n    if (this._send == null) {\n      // Lazy require to ensure that http/https is not required before instrumentations can wrap it.\n      const {\n        sendWithHttp,\n        createHttpAgent,\n        // eslint-disable-next-line @typescript-eslint/no-var-requires\n      } = require('./http-transport-utils');\n      this._agent = createHttpAgent(\n        this._parameters.url,\n        this._parameters.agentOptions\n      );\n      this._send = sendWithHttp;\n    }\n\n    return new Promise<ExportResponse>(resolve => {\n      // this will always be defined\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      this._send?.(\n        this._parameters,\n        this._agent!,\n        data,\n        result => {\n          resolve(result);\n        },\n        timeoutMillis\n      );\n    });\n  }\n  shutdown() {\n    // intentionally left empty, nothing to do.\n  }\n}\n\nexport function createHttpExporterTransport(\n  parameters: HttpRequestParameters\n): IExporterTransport {\n  return new HttpExporterTransport(parameters);\n}\n"]}