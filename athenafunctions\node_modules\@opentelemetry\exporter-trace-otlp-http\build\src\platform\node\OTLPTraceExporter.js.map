{"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/node/OTLPTraceExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,0EAG2C;AAC3C,2CAAwC;AACxC,sEAAsE;AACtE,2EAGqD;AAErD;;GAEG;AACH,MAAa,iBACX,SAAQ,qCAAgC;IAGxC,YAAY,SAAqC,EAAE;QACjD,KAAK,CACH,IAAA,wCAA4B,EAC1B,IAAA,oCAAwB,EAAC,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE;YACtD,YAAY,EAAE,iCAAiC,iBAAO,EAAE;YACxD,cAAc,EAAE,kBAAkB;SACnC,CAAC,EACF,sCAAmB,CACpB,CACF,CAAC;IACJ,CAAC;CACF;AAfD,8CAeC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport {\n  OTLPExporterNodeConfigBase,\n  OTLPExporterBase,\n} from '@opentelemetry/otlp-exporter-base';\nimport { VERSION } from '../../version';\nimport { JsonTraceSerializer } from '@opentelemetry/otlp-transformer';\nimport {\n  convertLegacyHttpOptions,\n  createOtlpHttpExportDelegate,\n} from '@opentelemetry/otlp-exporter-base/node-http';\n\n/**\n * Collector Trace Exporter for Node\n */\nexport class OTLPTraceExporter\n  extends OTLPExporterBase<ReadableSpan[]>\n  implements SpanExporter\n{\n  constructor(config: OTLPExporterNodeConfigBase = {}) {\n    super(\n      createOtlpHttpExportDelegate(\n        convertLegacyHttpOptions(config, 'TRACES', 'v1/traces', {\n          'User-Agent': `OTel-OTLP-Exporter-JavaScript/${VERSION}`,\n          'Content-Type': 'application/json',\n        }),\n        JsonTraceSerializer\n      )\n    );\n  }\n}\n"]}