{"version": 3, "file": "OTLPMetricExporterOptions.js", "sourceRoot": "", "sources": ["../../src/OTLPMetricExporterOptions.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAeH,IAAY,gCAIX;AAJD,WAAY,gCAAgC;IAC1C,yFAAK,CAAA;IACL,mGAAU,CAAA;IACV,iGAAS,CAAA;AACX,CAAC,EAJW,gCAAgC,GAAhC,wCAAgC,KAAhC,wCAAgC,QAI3C", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPExporterConfigBase } from '@opentelemetry/otlp-exporter-base';\nimport {\n  AggregationTemporality,\n  AggregationSelector,\n} from '@opentelemetry/sdk-metrics';\n\nexport interface OTLPMetricExporterOptions extends OTLPExporterConfigBase {\n  temporalityPreference?:\n    | AggregationTemporalityPreference\n    | AggregationTemporality;\n  aggregationPreference?: AggregationSelector;\n}\n\nexport enum AggregationTemporalityPreference {\n  DELTA,\n  CUMULATIVE,\n  LOWMEMORY,\n}\n"]}