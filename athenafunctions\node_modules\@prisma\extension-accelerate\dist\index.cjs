"use strict";var I=Object.create;var y=Object.defineProperty;var $=Object.getOwnPropertyDescriptor;var R=Object.getOwnPropertyNames;var U=Object.getPrototypeOf,C=Object.prototype.hasOwnProperty;var _=(s,n)=>{for(var i in n)y(s,i,{get:n[i],enumerable:!0})},x=(s,n,i,l)=>{if(n&&typeof n=="object"||typeof n=="function")for(let c of R(n))!C.call(s,c)&&c!==i&&y(s,c,{get:()=>n[c],enumerable:!(l=$(n,c))||l.enumerable});return s};var T=(s,n,i)=>(i=s!=null?I(U(s)):{},x(n||!s||!s.__esModule?y(i,"default",{value:s,enumerable:!0}):i,s)),E=s=>x(y({},"__esModule",{value:!0}),s);var j={};_(j,{FETCH_FAILURE_MESSAGE:()=>f,makeAccelerateExtension:()=>p,withAccelerate:()=>v});module.exports=E(j);var m=T(require("@prisma/client/scripts/default-index.js"),1);function w(s,n){let[i=0,l=0,c=0]=s.split(".").map(Number),[u=0,h=0,o=0]=n.split(".").map(Number),r=u-i,e=h-l,t=o-c;return r||e||t}var F=T(require("@prisma/client/scripts/default-index.js"),1);function b(){let s=F.default.Prisma.prismaVersion;return[O(),`PrismaEngine/${s.engine}`,`PrismaClient/${s.client}`].join(" ")}function O(){return typeof navigator<"u"?navigator.userAgent:typeof process<"u"&&typeof process.versions<"u"?`Node/${process.versions.node} (${process.platform}; ${process.arch})`:"EdgeRuntime"in globalThis?"Vercel-Edge-Runtime":"UnknownRuntime"}var S="@prisma/extension-accelerate",f="Unable to connect to the Accelerate API. This may be due to a network or DNS issue. Please check your connection and the Accelerate connection string. For details, visit https://www.prisma.io/docs/accelerate/troubleshoot.";function k(s){let n=b(),i;return async l=>{let{args:c}=l,{cacheStrategy:u,__accelerateInfo:h=!1,...o}=c,r=null,{__internalParams:e,query:t}=l;return e.customDataProxyFetch=()=>async(a,d)=>{let A=new Array;typeof u?.ttl=="number"&&A.push(`max-age=${u.ttl}`),typeof u?.swr=="number"&&A.push(`stale-while-revalidate=${u.swr}`);let P=u?.tags?.join(",")??"";d.headers={...d.headers,"cache-control":A.length>0?A.join(","):"no-cache","user-agent":n,...P.length>0?{"accelerate-cache-tags":P}:{}},i&&(d.headers["accelerate-query-engine-jwt"]=i);try{let g=await s(a,d);return r={cacheStatus:g.headers.get("accelerate-cache-status"),lastModified:new Date(g.headers.get("last-modified")??""),region:g.headers.get("cf-ray")?.split("-")[1]??"unspecified",requestId:g.headers.get("cf-ray")??"unspecified",signature:g.headers.get("accelerate-signature")??"unspecified"},i=g.headers.get("accelerate-query-engine-jwt")??void 0,g}catch{throw new Error(f)}},h?{data:await t(o,e),info:r}:t(o,e)}}function p(s){let n=w("5.1.0",m.default.Prisma.prismaVersion.client)>=0;return m.default.Prisma.defineExtension(i=>{let{apiKeyPromise:l,baseURL:c}=q(i),u=k(s);async function h(r){let e=await l;if(!e)return{requestId:"unspecified"};let t;try{t=await s(new URL("/invalidate",c).href,{method:"POST",headers:{authorization:`Bearer ${e}`,"content-type":"application/json"},body:JSON.stringify(r)})}catch{throw new Error(f)}if(!t?.ok){let a=await t.text();throw new Error(`Failed to invalidate Accelerate cache. Response was ${t.status} ${t.statusText}. ${a}`)}return t.body?.cancel(),{requestId:t.headers.get("cf-ray")??"unspecified"}}let o=i.$extends({name:S,query:{$allModels:{$allOperations:u}}});return o.$extends({name:S,client:{$accelerate:{invalidate:r=>h(r),invalidateAll:()=>h({tags:"all"})}},model:{$allModels:{aggregate(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.aggregate(r);return Object.assign(a,{withAccelerateInfo(){return t.aggregate({...r,__accelerateInfo:!0})}})},count(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.count(r);return Object.assign(a,{withAccelerateInfo(){return t.count({...r,__accelerateInfo:!0})}})},findFirst(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.findFirst(r);return Object.assign(a,{withAccelerateInfo(){return t.findFirst({...r,__accelerateInfo:!0})}})},findFirstOrThrow(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.findFirstOrThrow(r);return Object.assign(a,{withAccelerateInfo(){return t.findFirstOrThrow({...r,__accelerateInfo:!0})}})},findMany(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.findMany(r);return Object.assign(a,{withAccelerateInfo(){return t.findMany({...r,__accelerateInfo:!0})}})},findUnique(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.findUnique(r);return Object.assign(a,{withAccelerateInfo(){return t.findUnique({...r,__accelerateInfo:!0})}})},findUniqueOrThrow(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.findUniqueOrThrow(r);return Object.assign(a,{withAccelerateInfo(){return t.findUniqueOrThrow({...r,__accelerateInfo:!0})}})},groupBy(r){let e=m.default.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:o[e.name],a=t.groupBy(r);return Object.assign(a,{withAccelerateInfo(){return t.groupBy({...r,__accelerateInfo:!0})}})}}}})})}function q(s){let n=Reflect.get(s,"_accelerateEngineConfig");try{let{host:i,hostname:l,protocol:c,searchParams:u}=new URL(n?.accelerateUtils?.resolveDatasourceUrl?.(n));if(c==="prisma+postgres:"&&(l==="localhost"||l==="127.0.0.1"))return{apiKeyPromise:Promise.resolve(u.get("api_key")),baseURL:new URL(`http://${i}`)}}catch{}return{apiKeyPromise:s._engine.start().then(()=>s._engine.apiKey?.()??null),baseURL:new URL("https://accelerate.prisma-data.net")}}function v(s){let n=s?.fetch??fetch;return p(n)}0&&(module.exports={FETCH_FAILURE_MESSAGE,makeAccelerateExtension,withAccelerate});
