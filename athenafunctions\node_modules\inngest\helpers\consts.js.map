{"version": 3, "file": "consts.js", "sourceRoot": "", "sources": ["../../src/helpers/consts.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAE1B;;;;;;;;GAQG;AACH,IAAY,SAKX;AALD,WAAY,SAAS;IACnB,kCAAqB,CAAA;IACrB,0BAAa,CAAA;IACb,4BAAe,CAAA;IACf,8BAAiB,CAAA;AACnB,CAAC,EALW,SAAS,yBAAT,SAAS,QAKpB;AAED,IAAY,KAEX;AAFD,WAAY,KAAK;IACf,wBAAe,CAAA;AACjB,CAAC,EAFW,KAAK,qBAAL,KAAK,QAEhB;AAED,IAAY,OA+FX;AA/FD,WAAY,OAAO;IACjB,oDAAyC,CAAA;IACzC,qEAA0D,CAAA;IAC1D,gDAAqC,CAAA;IAErC;;OAEG;IACH,wDAA6C,CAAA;IAC7C,6CAAkC,CAAA;IAClC,8CAAmC,CAAA;IACnC,gEAAqD,CAAA;IACrD,qDAA0C,CAAA;IAC1C,kDAAuC,CAAA;IACvC,kDAAuC,CAAA;IACvC,gDAAqC,CAAA;IACrC,iDAAsC,CAAA;IACtC,yCAA8B,CAAA;IAC9B,gEAAqD,CAAA;IAErD;;;OAGG;IACH,qCAA0B,CAAA;IAE1B;;;;;OAKG;IACH,iDAAsC,CAAA;IAEtC;;OAEG;IACH,8BAAmB,CAAA;IAEnB;;;;;OAKG;IACH,oDAAyC,CAAA;IAEzC;;OAEG;IACH,yCAA8B,CAAA;IAE9B;;;;OAIG;IACH,mCAAwB,CAAA;IAExB;;OAEG;IACH,gCAAqB,CAAA;IAErB;;;;OAIG;IACH,6CAAkC,CAAA;IAElC;;OAEG;IACH,8BAAmB,CAAA;IAEnB;;;;OAIG;IACH,+CAAoC,CAAA;IAEpC;;;;OAIG;IACH,qDAA0C,CAAA;IAE1C,sCAA2B,CAAA;IAE3B,0CAA+B,CAAA;IAC/B,0CAA+B,CAAA;IAC/B,gDAAqC,CAAA;AACvC,CAAC,EA/FW,OAAO,uBAAP,OAAO,QA+FlB;AAED;;;;;;;;GAQG;AACH,IAAY,UAgBX;AAhBD,WAAY,UAAU;IACpB,8CAAgC,CAAA;IAChC,+CAAiC,CAAA;IACjC,0CAA4B,CAAA;IAC5B,2CAA6B,CAAA;IAC7B,6CAA+B,CAAA;IAC/B,+CAAiC,CAAA;IACjC,4CAA8B,CAAA;IAC9B,sDAAwC,CAAA;IACxC,wCAA0B,CAAA;IAC1B,yDAA2C,CAAA;IAC3C,0EAA4D,CAAA;IAC5D,qDAAuC,CAAA;IACvC,qDAAuC,CAAA;IACvC,yCAA2B,CAAA;IAC3B,uCAAyB,CAAA;AAC3B,CAAC,EAhBW,UAAU,0BAAV,UAAU,QAgBrB;AAEY,QAAA,wBAAwB,GAAG,0BAA0B,CAAC;AACtD,QAAA,0BAA0B,GAAG,iBAAiB,CAAC;AAC/C,QAAA,oBAAoB,GAAG,wBAAwB,CAAC;AAE7D;;;;;GAKG;AACH,IAAY,cAUX;AAVD,WAAY,cAAc;IACxB;;;OAGG;IACH,4DAA0C,CAAA;IAC1C,8DAA4C,CAAA;IAC5C,gEAA8C,CAAA;IAC9C,kEAAgD,CAAA;IAChD,4DAA0C,CAAA;AAC5C,CAAC,EAVW,cAAc,8BAAd,cAAc,QAUzB;AAEY,QAAA,SAAS,GAAW,eAAK,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;AAEpD,QAAA,WAAW,GAAG,SAAS,CAAC;AAExB,QAAA,aAAa,GAAG,kBAAkB,CAAC;AAEhD,IAAY,UAGX;AAHD,WAAY,UAAU;IACpB,yBAAW,CAAA;IACX,6BAAe,CAAA;AACjB,CAAC,EAHW,UAAU,0BAAV,UAAU,QAGrB;AAED,IAAY,QAGX;AAHD,WAAY,QAAQ;IAClB,8BAAkB,CAAA;IAClB,qCAAyB,CAAA;AAC3B,CAAC,EAHW,QAAQ,wBAAR,QAAQ,QAGnB"}