{"version": 3, "file": "errors.js", "sourceRoot": "", "sources": ["../../src/helpers/errors.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAA0B;AAC1B,8EAA4C;AAC5C,6DAK6B;AAC7B,4DAAmC;AACnC,6BAAwB;AAExB,6EAAuE;AAGvE,MAAM,cAAc,GAAG,cAAc,CAAC;AACtC,MAAM,gBAAgB,GAAG,IAAI,CAAC;AAE9B;;;;;;;;;;GAUG;AACH,uCAAiB,CAAC,GAAG,CACnB,mBAAmB,EACnB,wCAAqC,CACtC,CAAC;AAMF;;;;;;;;;;;;;;GAcG;AACI,MAAM,cAAc,GAAG;AAM5B;;GAEG;AACH,OAAgB;AAEhB;;;GAGG;AACH,eAA8B,KAAsB,EAC3C,EAAE;IACX,IAAI,CAAC;QACH,6CAA6C;QAC7C,kCAAkC;QAClC,MAAM,uBAAuB,GAAG,IAAA,yBAAiB,EAAC,OAAO,CAAC,CAAC;QAE3D,IAAI,uBAAuB,EAAE,CAAC;YAC5B,OAAO,uBAAkC,CAAC;QAC5C,CAAC;QAED,IAAI,OAAO,OAAO,KAAK,QAAQ,IAAI,OAAO,KAAK,IAAI,EAAE,CAAC;YACpD,+CAA+C;YAC/C,MAAM,aAAa,GAAG,IAAA,oCAAiB,EAAC,OAAgB,CAAC,CAAC;YAE1D,0EAA0E;YAC1E,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,YAAY,EAAE,CAAC;gBACxC,OAAO,OAAkB,CAAC;YAC5B,CAAC;YAED,oEAAoE;YACpE,yDAAyD;YACzD,sEAAsE;YACtE,4CAA4C;YAC5C,MAAM,GAAG,GAAG,gCAGP,aAAa,KAEhB,IAAI,EAAE,aAAa,CAAC,IAAI,IAAI,OAAO,EACnC,OAAO,EACL,aAAa,CAAC,OAAO;oBACrB,IAAA,6BAAS,EAAC,OAAO,CAAC;oBAClB,8DAA8D,EAChE,KAAK,EAAE,aAAa,CAAC,KAAK,IAAI,EAAE,EAChC,CAAC,cAAc,CAAC,EAAE,gBAAgB,GAC1B,CAAC;YAEX,0EAA0E;YAC1E,yEAAyE;YACzE,yEAAyE;YACzE,IAAI,MAAM,GAAY,GAAG,CAAC;YAC1B,MAAM,QAAQ,GAAG,CAAC,CAAC;YACnB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC;gBAClC,IACE,OAAO,MAAM,KAAK,QAAQ;oBAC1B,MAAM,KAAK,IAAI;oBACf,OAAO,IAAI,MAAM;oBACjB,MAAM,CAAC,KAAK,EACZ,CAAC;oBACD,MAAM,GAAG,MAAM,CAAC,KAAK,GAAG,IAAA,sBAAc,EAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;oBAC3D,SAAS;gBACX,CAAC;gBAED,MAAM;YACR,CAAC;YAED,OAAO,GAAc,CAAC;QACxB,CAAC;QAED,4EAA4E;QAC5E,8DAA8D;QAC9D,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAC;IAClE,CAAC;IAAC,OAAO,GAAG,EAAE,CAAC;QACb,IAAI,YAAY,EAAE,CAAC;YACjB,sEAAsE;YACtE,SAAS;YACT,OAAO,OAAkB,CAAC;QAC5B,CAAC;QAED,IAAI,CAAC;YACH,mEAAmE;YACnE,uEAAuE;YACvE,2DAA2D;YAC3D,OAAO,gCACF,IAAA,sBAAc,EACf,IAAI,KAAK,CAAC,OAAO,OAAO,KAAK,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAA,6BAAS,EAAC,OAAO,CAAC,CAAC,EACrE,KAAK,CACN;gBACD,2CAA2C;gBAC3C,KAAK,EAAE,EAAE,EACT,CAAC,cAAc,CAAC,EAAE,gBAAgB,GACxB,CAAC;QACf,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACb,0EAA0E;YAC1E,qCAAqC;YACrC,oDAAoD;YACpD,OAAO;gBACL,IAAI,EAAE,kCAAkC;gBACxC,OAAO,EAAE,sCAAsC;gBAC/C,KAAK,EAAE,EAAE;gBACT,CAAC,cAAc,CAAC,EAAE,gBAAgB;aACxB,CAAC;QACf,CAAC;IACH,CAAC;AACH,CAAC,CAAC;AA9GW,QAAA,cAAc,kBA8GzB;AAEF;;;GAGG;AACI,MAAM,iBAAiB,GAAG,CAC/B,KAAc,EACe,EAAE;IAC/B,IAAI,CAAC;QACH,IAAI,OAAO,KAAK,KAAK,QAAQ,EAAE,CAAC;YAC9B,MAAM,MAAM,GAAG,OAAC;iBACb,MAAM,CAAC;gBACN,CAAC,cAAc,CAAC,EAAE,OAAC,CAAC,OAAO,CAAC,gBAAgB,CAAC;gBAC7C,IAAI,EAAE,OAAC,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,CAAC,IAAI,CAAC,uCAAiB,CAAC,IAAI,EAAE,CAAC,CAGpD,CAAC;gBACF,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE;gBACnB,KAAK,EAAE,OAAC,CAAC,MAAM,EAAE;aAClB,CAAC;iBACD,WAAW,EAAE;iBACb,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC;YAEhC,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;gBACnB,OAAO,MAAM,CAAC,IAAuB,CAAC;YACxC,CAAC;QACH,CAAC;QAED,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE,CAAC;YAChD,MAAM,kBAAkB,GACtB,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,EAAE,cAAc,CAAC;gBAC3D,sEAAsE;gBACrE,KAAuC,CAAC,cAAc,CAAC;oBACtD,gBAAgB,CAAC;YAErB,IAAI,kBAAkB,EAAE,CAAC;gBACvB,OAAO,KAAwB,CAAC;YAClC,CAAC;QACH,CAAC;IACH,CAAC;IAAC,WAAM,CAAC;QACP,4EAA4E;QAC5E,QAAQ;IACV,CAAC;AACH,CAAC,CAAC;AAtCW,QAAA,iBAAiB,qBAsC5B;AAEF;;;;;;GAMG;AACI,MAAM,gBAAgB,GAAG,CAM9B,OAAiC,EACjC,eAA8B,KAAsB,EAC3C,EAAE;IACX,MAAM,cAAc,GAA8B,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAEtE,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YACvD,OAAO,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC;QAC9D,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,EAAE,CAAC;QACpB,CAAC;QAED,MAAM,eAAe,GAAG,IAAA,sCAAmB,EAAC,OAA0B,CAAC,CAAC;QAExE,IAAI,OAAO,IAAI,eAAe,EAAE,CAAC;YAC/B,eAAe,CAAC,KAAK,GAAG,IAAA,wBAAgB,EACtC,eAAe,CAAC,KAAiC,EACjD,IAAI,CACL,CAAC;QACJ,CAAC;QAED,OAAO,eAA0B,CAAC;IACpC,CAAC;IAAC,WAAM,CAAC;QACP,IAAI,YAAY,EAAE,CAAC;YACjB,sEAAsE;YACtE,SAAS;YACT,OAAO,OAAkB,CAAC;QAC5B,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAC;QAE9D;;;WAGG;QACH,GAAG,CAAC,KAAK,GAAG,SAAS,CAAC;QAEtB,OAAO,GAAc,CAAC;IACxB,CAAC;AACH,CAAC,CAAC;AA/CW,QAAA,gBAAgB,oBA+C3B;AAEF,IAAY,OA4BX;AA5BD,WAAY,OAAO;IACjB,0CAA+B,CAAA;IAE/B;;;;;OAKG;IACH,oEAAyD,CAAA;IAEzD;;;;;OAKG;IACH,gFAAqE,CAAA;IAErE;;;;;OAKG;IACH,0DAA+C,CAAA;IAE/C,sEAA2D,CAAA;AAC7D,CAAC,EA5BW,OAAO,uBAAP,OAAO,QA4BlB;AAkEY,QAAA,mBAAmB,GAC9B,mDAAmD,CAAC;AAEtD;;;GAGG;AACI,MAAM,iBAAiB,GAAG,CAAI,GAAM,EAAK,EAAE;;IAChD,IAAI,CAAC;QACH,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC;YAClB,OAAO,GAAG,CAAC;QACb,CAAC;QAED,MAAM,aAAa,GAAG,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,2BAAmB,CAAC,CAAC;QAChE,IAAI,CAAC,aAAa,EAAE,CAAC;YACnB,OAAO,GAAG,CAAC;QACb,CAAC;QAED,MAAM,gBAAgB,GAAG,IAAA,oBAAS,EAAC,GAAG,CAAC,OAAO,CAAC,CAAC;QAEhD,MAAM,OAAO,GACX,CAAA,MAAA,MAAA,gBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,0CAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC,0CAAE,IAAI,EAAE,KAAI,GAAG,CAAC,OAAO,CAAC;QACzE,MAAM,IAAI,GACR,CAAA,MAAA,MAAA,gBAAgB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,0CAAE,KAAK,CAAC,MAAM,EAAE,CAAC,CAAC,0CAAE,IAAI,EAAE;YACjE,SAAS,CAAC;QAEZ,GAAG,CAAC,OAAO,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAE1D,IAAI,GAAG,CAAC,KAAK,EAAE,CAAC;YACd,MAAM,cAAc,GAAG,IAAA,oBAAS,EAAC,GAAG,CAAC,KAAK,CAAC,CAAC;YAC5C,MAAM,SAAS,GAAG,cAAc;iBAC7B,KAAK,CAAC,GAAG,2BAAmB,IAAI,CAAC;iBACjC,KAAK,CAAC,CAAC,CAAC;iBACR,IAAI,CAAC,IAAI,CAAC,CAAC;YAEd,GAAG,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,KAAK,SAAS,EAAE,CAAC;QAC1D,CAAC;QAED,OAAO,GAAG,CAAC;IACb,CAAC;IAAC,OAAO,OAAO,EAAE,CAAC;QACjB,OAAO,GAAG,CAAC;IACb,CAAC;AACH,CAAC,CAAC;AAnCW,QAAA,iBAAiB,qBAmC5B;AAEF;;;GAGG;AACH,MAAM,OAAO,GAAG,CAAC,GAAY,EAAgB,EAAE;IAC7C,IAAI,CAAC;QACH,IAAI,GAAG,YAAY,KAAK,EAAE,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,MAAM,OAAO,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;QAClE,MAAM,UAAU,GAAG,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC,CAAC;QAExE,OAAO,OAAO,IAAI,UAAU,CAAC;IAC/B,CAAC;IAAC,OAAO,OAAO,EAAE,CAAC;QACjB,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC,CAAC;AAEF;;;GAGG;AACI,MAAM,eAAe,GAAG,CAAC,GAAY,EAAE,QAAgB,EAAU,EAAE;IACxE,MAAM,EAAE,OAAO,EAAE,GAAG,OAAC;SAClB,MAAM,CAAC,EAAE,OAAO,EAAE,OAAC,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;SACtC,KAAK,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;SAC5B,KAAK,CAAC,GAAG,CAAC,CAAC;IAEd,OAAO,OAAO,CAAC;AACjB,CAAC,CAAC;AAPW,QAAA,eAAe,mBAO1B;AAEF;;;;;;GAMG;AACI,MAAM,WAAW,GAAG,CAAC,EAC1B,IAAI,GAAG,OAAO,EACd,YAAY,EACZ,SAAS,EACT,WAAW,EACX,QAAQ,EACR,GAAG,EACH,YAAY,EACZ,KAAK,EACL,IAAI,GACQ,EAAU,EAAE;;IACxB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GACrB;QACE,KAAK,EAAE,EAAE,IAAI,EAAE,GAAG,EAAE,OAAO,EAAE,eAAK,CAAC,GAAG,EAAE;QACxC,IAAI,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,eAAK,CAAC,MAAM,EAAE;KAK9C,CAAC,IAAI,CAAC,CAAC;IAER,IAAI,MAAM,GAAG,GAAG,IAAI,KAAK,eAAK,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;IACrE,IAAI,KAAK,EAAE,CAAC;QACV,MAAM;YACJ,IAAI;gBACJ,CAAC,GAAG,CAAC,CAAA,MAAA,IAAI,KAAK,EAAE,CAAC,KAAK,0CAAE,KAAK,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,CAAC,KAAI,EAAE,CAAC,CAAC,CAAC,IAAI,CACvE,IAAI,CACL,CAAC;IACN,CAAC;IAED,IAAI,WAAW,GACb,MAAA,CAAC,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC;QACtB,CAAC,CAAC,QAAQ;aACL,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;aACpB,MAAM,CAAC,OAAO,CAAC;aACf,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC;aACjC,IAAI,CAAC,IAAI,CAAC;QACf,CAAC,CAAC,QAAQ,aAAR,QAAQ,uBAAR,QAAQ,CAAE,IAAI,EAAE,CAAC,mCAAI,EAAE,CAAC;IAE9B,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,WAAW,EAAE,CAAC;QAC3C,WAAW,GAAG,wEAAwE,WAAW,EAAE,CAAC;IACtG,CAAC;IAED,IAAI,IAAI,GAAG,CAAC,WAAW,aAAX,WAAW,uBAAX,WAAW,CAAE,IAAI,EAAE,EAAE,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,IAAI,EAAE,EAAE,YAAY,aAAZ,YAAY,uBAAZ,YAAY,CAAE,IAAI,EAAE,CAAC;SAChE,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,GAAG,CAAC,CAAC;IACb,IAAI,IAAI,IAAI,CAAC,CAAC,CAAC,OAAO,WAAW,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;IAElD,MAAM,OAAO,GAAG,CAAC,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,IAAI,EAAE,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;IAE9D,MAAM,OAAO,GAAG;QACd,2BAAmB;QACnB,MAAM;QACN,IAAI;QACJ,OAAO;QACP,IAAI,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE;QAC3B,2BAAmB;KACpB;SACE,MAAM,CAAC,OAAO,CAAC;SACf,IAAI,CAAC,MAAM,CAAC,CAAC;IAEhB,OAAO,OAAO,CAAC,OAAO,CAAC,CAAC;AAC1B,CAAC,CAAC;AA9DW,QAAA,WAAW,eA8DtB;AAEW,QAAA,uBAAuB,GAAG;IACrC,kDAAkD;IAClD,+DACE,UACF,WAAW;IACX,iBAAiB,aAAyC,iBAAiB;CAC5E,CAAC;AAEF;;;;;;;;GAQG;AACH,MAAa,mBAAoB,SAAQ,KAAK;IAG5C,YAAY,MAA0C;QACpD,KAAK,CAAC,iBAAiB,CAAC,CAAC;QACzB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;CACF;AAPD,kDAOC;AAED;;;;;;;;;;GAUG;AACH,8DAA8D;AACvD,MAAM,YAAY,GAAG,CAAC,MAAc,EAAyB,EAAE;IACpE,OAAO,CAAC,GAAG,EAAE,EAAE;QACb,IAAI,CAAC;YACH,wHAAwH;YACxH,GAAG,CAAC,OAAO,KAAX,GAAG,CAAC,OAAO,GAAK,GAAG,MAAM,KAAK,GAAG,CAAC,OAAO,EAAE,EAAC;QAC9C,CAAC;QAAC,OAAO,OAAO,EAAE,CAAC;YACjB,QAAQ;QACV,CAAC;gBAAS,CAAC;YACT,6CAA6C;YAC7C,MAAM,GAAG,CAAC;QACZ,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,YAAY,gBAYvB;AAEF;;;GAGG;AACI,MAAM,yBAAyB,GAAG,CAAC,IAAa,EAAE,EAAE;IACzD,OAAO,IAAA,mBAAW,EAAC;QACjB,YAAY,EAAE,wCAAwC;QACtD,GAAG,EAAE,gFAAgF;QACrF,YAAY,EACV,kLAAkL;QACpL,KAAK,EAAE,IAAI;QACX,QAAQ,EACN,0LAA0L;QAC5L,SAAS,EACP,+HAA+H;QACjI,IAAI;KACL,CAAC,CAAC;AACL,CAAC,CAAC;AAbW,QAAA,yBAAyB,6BAapC"}