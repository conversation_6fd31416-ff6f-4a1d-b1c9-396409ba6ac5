{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,uCAAgD;AAAvC,8GAAA,kBAAkB,OAAA;AAC3B,yEAGqC;AADnC,6IAAA,gCAAgC,OAAA;AAElC,mEAKkC;AAJhC,uIAAA,6BAA6B,OAAA;AAC7B,kIAAA,wBAAwB,OAAA;AACxB,sIAAA,4BAA4B,OAAA;AAC5B,gIAAA,sBAAsB,OAAA", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { OTLPMetricExporter } from './platform';\nexport {\n  OTLPMetricExporterOptions,\n  AggregationTemporalityPreference,\n} from './OTLPMetricExporterOptions';\nexport {\n  CumulativeTemporalitySelector,\n  DeltaTemporalitySelector,\n  LowMemoryTemporalitySelector,\n  OTLPMetricExporterBase,\n} from './OTLPMetricExporterBase';\n"]}