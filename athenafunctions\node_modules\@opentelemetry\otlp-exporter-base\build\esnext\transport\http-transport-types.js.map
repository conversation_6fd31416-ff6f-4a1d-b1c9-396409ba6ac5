{"version": 3, "file": "http-transport-types.js", "sourceRoot": "", "sources": ["../../../src/transport/http-transport-types.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type * as http from 'http';\nimport type * as https from 'https';\nimport { ExportResponse } from '../export-response';\n\nexport type sendWithHttp = (\n  params: HttpRequestParameters,\n  agent: http.Agent | https.Agent,\n  data: Uint8Array,\n  onDone: (response: ExportResponse) => void,\n  timeoutMillis: number\n) => void;\n\nexport interface HttpRequestParameters {\n  url: string;\n  headers: () => Record<string, string>;\n  compression: 'gzip' | 'none';\n  agentOptions: http.AgentOptions | https.AgentOptions;\n}\n"]}