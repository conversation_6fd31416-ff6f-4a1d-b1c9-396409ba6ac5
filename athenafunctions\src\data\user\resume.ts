"use server";


import { safeDecrypt } from "@/utils/security/safeDecrypt";
import prisma from "@/lib/prisma/prisma";

export const GetAllResumeRawTextData = async () => {
  try {
    // Fetch all resumes
    const resumes = await prisma.userResume.findMany({
      select: {
        id: true,
        userId: true,
        fileContent: true
      }
    });

    // Decrypt the file content for each resume
    if (resumes && resumes.length > 0) {
        const resumesWithDecryptedContent = resumes.map(resume => ({
        id: resume.id,
        userId: resume.userId,
        raw_text: safeDecrypt(resume.fileContent) || ""
        })).filter(resume => resume.raw_text); // Filter out resumes with no content

        return resumesWithDecryptedContent;
    }
    return null;
  } catch (error) {
    console.error("Error fetching resume raw text:", error);
    return null;
  }
};
