import { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';
import { OTLPExporterNodeConfigBase, OTLPExporterBase } from '@opentelemetry/otlp-exporter-base';
/**
 * Collector Trace Exporter for Node
 */
export declare class OTLPTraceExporter extends OTLPExporterBase<ReadableSpan[]> implements SpanExporter {
    constructor(config?: OTLPExporterNodeConfigBase);
}
//# sourceMappingURL=OTLPTraceExporter.d.ts.map