{"version": 3, "file": "OTLPTraceExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPTraceExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,0EAG2C;AAC3C,sEAAsE;AACtE,iFAAuG;AAEvG;;GAEG;AACH,MAAa,iBACX,SAAQ,qCAAgC;IAGxC,YAAY,SAAiC,EAAE;QAC7C,KAAK,CACH,IAAA,oDAAqC,EACnC,MAAM,EACN,sCAAmB,EACnB,WAAW,EACX,EAAE,cAAc,EAAE,kBAAkB,EAAE,CACvC,CACF,CAAC;IACJ,CAAC;CACF;AAdD,8CAcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';\nimport {\n  OTLPExporterConfigBase,\n  OTLPExporterBase,\n} from '@opentelemetry/otlp-exporter-base';\nimport { JsonTraceSerializer } from '@opentelemetry/otlp-transformer';\nimport { createLegacyOtlpBrowserExportDelegate } from '@opentelemetry/otlp-exporter-base/browser-http';\n\n/**\n * Collector Trace Exporter for Web\n */\nexport class OTLPTraceExporter\n  extends OTLPExporterBase<ReadableSpan[]>\n  implements SpanExporter\n{\n  constructor(config: OTLPExporterConfigBase = {}) {\n    super(\n      createLegacyOtlpBrowserExportDelegate(\n        config,\n        JsonTraceSerializer,\n        'v1/traces',\n        { 'Content-Type': 'application/json' }\n      )\n    );\n  }\n}\n"]}