# Accelerate Prisma Client extension

This is the package for the [Prisma Client extension](https://www.prisma.io/docs/concepts/components/prisma-client/client-extensions?utm_source=github&utm_medium=accelerate-readme) that enables usage of Prisma Accelerate.

[Prisma Accelerate](https://www.prisma.io/data-platform/accelerate?utm_source=github&utm_medium=accelerate-readme) provides an external connection pool and global caching layer that you can use to speed up your database queries.

It is part of the [Prisma](https://www.prisma.io?utm_source=github&utm_medium=accelerate-readme) ecosystem, alongside other tools such as:

- [Prisma ORM](https://github.com/prisma/prisma): Next-generation Node.js and TypeScript ORM, supporting PostgreSQL, MySQL, MariaDB, SQL Server, SQLite, CockroachDB, and MongoDB.

Prisma is leading Data DX, a philosophy that promotes simplicity in data-driven application development. Learn more on the [Data DX manifesto](https://www.datadx.io/?utm_source=github&utm_medium=accelerate-readme).

## Getting started with Accelerate

### Resources

You can explore Accelerate with the following resources:

- [Get started](https://www.prisma.io/docs/data-platform/accelerate/getting-started?utm_source=github&utm_medium=accelerate-readme)
- [Documentation](https://www.prisma.io/docs/data-platform/accelerate/what-is-accelerate?utm_source=github&utm_medium=accelerate-readme)
- [Prisma Data Platform](https://console.prisma.io/login?utm_source=github&utm_medium=accelerate-readme)
- [Accelerate Speed Test](https://accelerate-speed-test.prisma.io/?utm_source=github&utm_medium=accelerate-readme)

### Using Accelerate

#### 1. Enable Accelerate

Log into [Prisma Data Platform](https://console.prisma.io/login?utm_source=github&utm_medium=accelerate-readme) and enable Accelerate for your project.

#### 2. Add Accelerate to your application

Replace the database connection string with the Accelerate connection string you generated in Prisma Data Platform (assuming you store your database connection string in the `DATABASE_URL` in the `.env` file):

```bash
DATABASE_URL="prisma://accelerate.prisma-data.net/?api_key=__API_KEY__"
```

To be able to seamlessly continue to use Prisma Migrate, you can set the `directUrl` property in your `datasource`:

```prisma
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_DATABASE_URL")
}
```

And then add the URL that connects directly to your database as the value for `DIRECT_DATABASE_URL` :

```prisma
DATABASE_URL="prisma://accelerate.prisma-data.net/?api_key=__API_KEY__"
DIRECT_DATABASE_URL="postgresql://USER:PASSWORD@HOST:PORT/DATABASE"
```

Finally, extend your Prisma Client instance with the Accelerate extension to enable Accelerate’s connection pool:

```ts
import { PrismaClient } from "@prisma/client/edge";
import { withAccelerate } from "@prisma/extension-accelerate";

const prisma = new PrismaClient().$extends(withAccelerate());
```

#### 3. Add caching to your Accelerate queries

You can optionally configure caching on a per-query level using the `ttl` (Time-To-Live) and `swl` (Stale-While-Revalidate) options:

```ts
await prisma.user.findMany({
  cacheStrategy: {
    ttl: 3_600,
    swr: 500,
  },
});
```
