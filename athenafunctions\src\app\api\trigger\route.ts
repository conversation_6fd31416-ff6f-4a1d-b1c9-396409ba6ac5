import { NextRequest, NextResponse } from 'next/server';
import { inngest } from '@/inngest/client';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { eventName, data } = body;

    if (!eventName) {
      return NextResponse.json(
        { error: 'Event name is required' },
        { status: 400 }
      );
    }

    // Send event to Inngest
    const result = await inngest.send({
      name: eventName,
      data: data || {},
    });

    return NextResponse.json({
      success: true,
      message: `Event '${eventName}' triggered successfully`,
      result,
    });
  } catch (error) {
    console.error('Error triggering event:', error);
    return NextResponse.json(
      { error: 'Failed to trigger event' },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Inngest Event Trigger API',
    usage: {
      method: 'POST',
      body: {
        eventName: 'string (required)',
        data: 'object (optional)',
      },
      examples: [
        {
          eventName: 'test/hello.world',
          data: { email: '<EMAIL>' },
        },
      ],
    },
  });
}
