{"name": "@opentelemetry/propagator-jaeger", "version": "1.30.1", "description": "OpenTelemetry Jaeger propagator provides HTTP header propagation for systems that are using Jaeger HTTP header format.", "main": "build/src/index.js", "module": "build/esm/index.js", "types": "build/src/index.d.ts", "repository": "open-telemetry/opentelemetry-js", "scripts": {"prepublishOnly": "npm run compile", "compile": "tsc --build tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "test": "nyc mocha 'test/**/*.test.ts' --exclude 'test/index-webpack.ts'", "test:browser": "karma start --single-run", "tdd": "npm run tdd:node", "tdd:node": "npm run test -- --watch-extensions ts --watch", "tdd:browser": "karma start", "clean": "tsc --build --clean tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix", "version": "node ../../scripts/version-update.js", "watch": "tsc --build --watch tsconfig.json tsconfig.esm.json tsconfig.esnext.json", "precompile": "cross-var lerna run version --scope $npm_package_name --include-dependencies", "prewatch": "npm run precompile", "peer-api-check": "node ../../scripts/peer-api-check.js", "align-api-deps": "node ../../scripts/align-api-deps.js"}, "keywords": ["opentelemetry", "nodejs", "tracing", "profiling", "jaeger"], "author": "OpenTelemetry Authors", "license": "Apache-2.0", "engines": {"node": ">=14"}, "files": ["build/esm/**/*.js", "build/esm/**/*.js.map", "build/esm/**/*.d.ts", "build/esnext/**/*.js", "build/esnext/**/*.js.map", "build/esnext/**/*.d.ts", "build/src/**/*.js", "build/src/**/*.js.map", "build/src/**/*.d.ts", "doc", "LICENSE", "README.md"], "publishConfig": {"access": "public"}, "devDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0", "@types/mocha": "10.0.10", "@types/node": "18.6.5", "@types/sinon": "17.0.3", "@types/webpack-env": "1.16.3", "babel-plugin-istanbul": "7.0.0", "cross-var": "1.1.0", "karma": "6.4.4", "karma-chrome-launcher": "3.1.0", "karma-coverage": "2.2.1", "karma-mocha": "2.0.1", "karma-spec-reporter": "0.0.36", "karma-webpack": "5.0.1", "lerna": "6.6.2", "mocha": "10.8.2", "nyc": "15.1.0", "sinon": "15.1.2", "ts-loader": "9.5.1", "typescript": "4.4.4", "webpack": "5.96.1"}, "peerDependencies": {"@opentelemetry/api": ">=1.0.0 <1.10.0"}, "dependencies": {"@opentelemetry/core": "1.30.1"}, "homepage": "https://github.com/open-telemetry/opentelemetry-js/tree/main/packages/opentelemetry-propagator-jaeger", "sideEffects": false, "gitHead": "cbc912d67bda462ca00449d7ce7b80052c20a4fc"}