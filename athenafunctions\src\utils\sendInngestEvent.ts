// utils/sendInngestEvent.ts
import { inngest } from "@/lib/inngest/client";

export async function sendInngestEvent({
  name,
  data,
}: {
  name: string;
  data: Record<string, unknown>;
}) {
  try {
    console.log(`[Inngest] 🚀 Attempting to send event: ${name}`, { data });

    /**
     * By using the Inngest client's `send` method, we no longer need to manually
     * manage URLs or headers for different environments. The SDK handles it
     * automatically.
     */
    const result = await inngest.send({
      name,
      data,
    });

    console.log(`[Inngest] ✅ Event sent successfully: ${name}`, { result });
    return result;
  } catch (err) {
    console.error(`[Inngest] ❌ Error sending event: ${name}`, err);
    throw err; // Re-throw to help with debugging
  }
}
