module.exports = {

"[project]/node_modules/inngest/helpers/promises.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __await = this && this.__await || function(v) {
    return this instanceof __await ? (this.v = v, this) : new __await(v);
};
var __asyncGenerator = this && this.__asyncGenerator || function(thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function awaitReturn(f) {
        return function(v) {
            return Promise.resolve(v).then(f, reject);
        };
    }
    function verb(n, f) {
        if (g[n]) {
            i[n] = function(v) {
                return new Promise(function(a, b) {
                    q.push([
                        n,
                        v,
                        a,
                        b
                    ]) > 1 || resume(n, v);
                });
            };
            if (f) i[n] = f(i[n]);
        }
    }
    function resume(n, v) {
        try {
            step(g[n](v));
        } catch (e) {
            settle(q[0][3], e);
        }
    }
    function step(r) {
        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
    }
    function fulfill(value) {
        resume("next", value);
    }
    function reject(value) {
        resume("throw", value);
    }
    function settle(f, v) {
        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
    }
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.retryWithBackoff = exports.resolveNextTick = exports.runAsPromise = exports.createTimeoutPromise = exports.createDeferredPromiseWithStack = exports.createDeferredPromise = exports.resolveAfterPending = exports.createFrozenPromise = void 0;
/**
 * Some environments don't allow access to the global queueMicrotask(). While we
 * had assumed this was only true for those powered by earlier versions of Node
 * (<14) that we don't officially support, Vercel's Edge Functions also obscure
 * the function in dev, even though the platform it's based on (Cloudflare
 * Workers) appropriately exposes it. Even worse, production Vercel Edge
 * Functions can see the function, but it immediately blows up the function when
 * used.
 *
 * Therefore, we can fall back to a reasonable alternative of
 * `Promise.resolve().then(fn)` instead. This _may_ be slightly slower in modern
 * environments, but at least we can still work in these environments.
 */ const shimQueueMicrotask = (callback)=>{
    void Promise.resolve().then(callback);
};
/**
 * A helper function to create a `Promise` that will never settle.
 *
 * It purposefully creates no references to `resolve` or `reject` so that the
 * returned `Promise` will remain unsettled until it falls out of scope and is
 * garbage collected.
 *
 * This should be used within transient closures to fake asynchronous action, so
 * long as it's guaranteed that they will fall out of scope.
 */ const createFrozenPromise = ()=>{
    return new Promise(()=>undefined);
};
exports.createFrozenPromise = createFrozenPromise;
/**
 * Returns a Promise that resolves after the current event loop's microtasks
 * have finished, but before the next event loop tick.
 */ const resolveAfterPending = (count = 100)=>{
    /**
     * This uses a brute force implementation that will continue to enqueue
     * microtasks 10 times before resolving. This is to ensure that the microtask
     * queue is drained, even if the microtask queue is being manipulated by other
     * code.
     *
     * While this still doesn't guarantee that the microtask queue is drained,
     * it's our best bet for giving other non-controlled promises a chance to
     * resolve before we continue without resorting to falling in to the next
     * tick.
     */ return new Promise((resolve)=>{
        let i = 0;
        const iterate = ()=>{
            shimQueueMicrotask(()=>{
                if (i++ > count) {
                    return resolve();
                }
                iterate();
            });
        };
        iterate();
    });
};
exports.resolveAfterPending = resolveAfterPending;
/**
 * Creates and returns Promise that can be resolved or rejected with the
 * returned `resolve` and `reject` functions.
 *
 * Resolving or rejecting the function will return a new set of Promise control
 * functions. These can be ignored if the original Promise is all that's needed.
 */ const createDeferredPromise = ()=>{
    let resolve;
    let reject;
    const promise = new Promise((_resolve, _reject)=>{
        resolve = (value)=>{
            _resolve(value);
            return (0, exports.createDeferredPromise)();
        };
        reject = (reason)=>{
            _reject(reason);
            return (0, exports.createDeferredPromise)();
        };
    });
    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
    return {
        promise,
        resolve: resolve,
        reject: reject
    };
};
exports.createDeferredPromise = createDeferredPromise;
/**
 * Creates and returns a deferred Promise that can be resolved or rejected with
 * the returned `resolve` and `reject` functions.
 *
 * For each Promise resolved or rejected this way, this will also keep a stack
 * of all unhandled Promises, resolved or rejected.
 *
 * Once a Promise is read, it is removed from the stack.
 */ const createDeferredPromiseWithStack = ()=>{
    const settledPromises = [];
    let rotateQueue = ()=>{};
    const results = function() {
        return __asyncGenerator(this, arguments, function*() {
            while(true){
                const next = settledPromises.shift();
                if (next) {
                    yield yield __await(next);
                } else {
                    yield __await(new Promise((resolve)=>{
                        rotateQueue = resolve;
                    }));
                }
            }
        });
    }();
    const shimDeferredPromise = (deferred)=>{
        const originalResolve = deferred.resolve;
        const originalReject = deferred.reject;
        deferred.resolve = (value)=>{
            settledPromises.push(deferred.promise);
            rotateQueue();
            return shimDeferredPromise(originalResolve(value));
        };
        deferred.reject = (reason)=>{
            settledPromises.push(deferred.promise);
            rotateQueue();
            return shimDeferredPromise(originalReject(reason));
        };
        return deferred;
    };
    const deferred = shimDeferredPromise((0, exports.createDeferredPromise)());
    return {
        deferred,
        results
    };
};
exports.createDeferredPromiseWithStack = createDeferredPromiseWithStack;
/**
 * Creates a Promise that will resolve after the given duration, along with
 * methods to start, clear, and reset the timeout.
 */ const createTimeoutPromise = (duration)=>{
    const { promise, resolve } = (0, exports.createDeferredPromise)();
    let timeout;
    // eslint-disable-next-line prefer-const
    let ret;
    const start = ()=>{
        if (timeout) return ret;
        timeout = setTimeout(()=>{
            resolve();
        }, duration);
        return ret;
    };
    const clear = ()=>{
        clearTimeout(timeout);
        timeout = undefined;
    };
    const reset = ()=>{
        clear();
        return start();
    };
    ret = Object.assign(promise, {
        start,
        clear,
        reset
    });
    return ret;
};
exports.createTimeoutPromise = createTimeoutPromise;
/**
 * Take any function and safely promisify such that both synchronous and
 * asynchronous errors are caught and returned as a rejected Promise.
 *
 * The passed `fn` can be undefined to support functions that may conditionally
 * be defined.
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const runAsPromise = (fn)=>{
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
    return Promise.resolve().then(fn);
};
exports.runAsPromise = runAsPromise;
/**
 * Returns a Promise that resolve after the current event loop tick.
 */ const resolveNextTick = ()=>{
    return new Promise((resolve)=>setTimeout(resolve));
};
exports.resolveNextTick = resolveNextTick;
const retryWithBackoff = async (fn, opts)=>{
    var _a;
    const maxAttempts = (opts === null || opts === void 0 ? void 0 : opts.maxAttempts) || 5;
    const baseDelay = (_a = opts === null || opts === void 0 ? void 0 : opts.baseDelay) !== null && _a !== void 0 ? _a : 100;
    for(let attempt = 1; attempt <= maxAttempts; attempt++){
        try {
            return await fn();
        } catch (err) {
            if (attempt >= maxAttempts) {
                throw err;
            }
            const jitter = Math.random() * baseDelay;
            const delay = baseDelay * Math.pow(2, attempt - 1) + jitter;
            await new Promise((resolve)=>setTimeout(resolve, delay));
        }
    }
    throw new Error("Max retries reached; this should be unreachable.");
};
exports.retryWithBackoff = retryWithBackoff; //# sourceMappingURL=promises.js.map
}}),
"[project]/node_modules/inngest/helpers/ServerTiming.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ServerTiming = void 0;
const promises_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/promises.js [app-route] (ecmascript)");
/**
 * A class to manage timing functions and arbitrary periods of time before
 * generating a `Server-Timing` header for use in HTTP responses.
 *
 * This is a very simple implementation that does not support nested timings or
 * fractions of a millisecond.
 */ class ServerTiming {
    constructor(){
        this.timings = {};
    }
    /**
     * Start a timing. Returns a function that, when called, will stop the timing
     * and add it to the header.
     */ start(name, description) {
        if (!this.timings[name]) {
            this.timings[name] = {
                description: description !== null && description !== void 0 ? description : "",
                timers: []
            };
        }
        const index = this.timings[name].timers.push({
            start: Date.now()
        }) - 1;
        return ()=>{
            const target = this.timings[name];
            if (!target) {
                return console.warn(`Timing "${name}" does not exist`);
            }
            const timer = target.timers[index];
            if (!timer) {
                return console.warn(`Timer ${index} for timing "${name}" does not exist`);
            }
            timer.end = Date.now();
        };
    }
    /**
     * Add a piece of arbitrary, untimed information to the header. Common use
     * cases would be cache misses.
     *
     * @example
     * ```
     * timer.append("cache", "miss");
     * ```
     */ append(key, value) {
        this.timings[key] = {
            description: value,
            timers: []
        };
    }
    /**
     * Wrap a function in a timing. The timing will be stopped and added to the
     * header when the function resolves or rejects.
     *
     * The return value of the function will be returned from this function.
     */ async wrap(name, fn, description) {
        const stop = this.start(name, description);
        try {
            return await (0, promises_js_1.runAsPromise)(fn);
        } finally{
            stop();
        }
    }
    /**
     * Generate the `Server-Timing` header.
     */ getHeader() {
        const entries = Object.entries(this.timings).reduce((acc, [name, { description, timers }])=>{
            /**
             * Ignore timers that had no end.
             */ const hasTimersWithEnd = timers.some((timer)=>timer.end);
            if (!hasTimersWithEnd) {
                return acc;
            }
            const dur = timers.reduce((acc, { start, end })=>{
                if (!start || !end) return acc;
                return acc + (end - start);
            }, 0);
            const entry = [
                name,
                description ? `desc="${description}"` : "",
                dur ? `dur=${dur}` : ""
            ].filter(Boolean).join(";");
            return [
                ...acc,
                entry
            ];
        }, []);
        return entries.join(", ");
    }
}
exports.ServerTiming = ServerTiming; //# sourceMappingURL=ServerTiming.js.map
}}),
"[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.syncKind = exports.serverKind = exports.dummyEventKey = exports.debugPrefix = exports.logPrefix = exports.internalEvents = exports.defaultDevServerHost = exports.defaultInngestEventBaseUrl = exports.defaultInngestApiBaseUrl = exports.headerKeys = exports.envKeys = exports.probe = exports.queryKeys = void 0;
const chalk_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/chalk/source/index.js [app-route] (ecmascript)"));
/**
 * Keys for accessing query parameters included in requests from Inngest to run
 * functions.
 *
 * Used internally to create handlers using `InngestCommHandler`, but can be
 * imported to be used if creating a custom handler outside of the package.
 *
 * @public
 */ var queryKeys;
(function(queryKeys) {
    queryKeys["DeployId"] = "deployId";
    queryKeys["FnId"] = "fnId";
    queryKeys["Probe"] = "probe";
    queryKeys["StepId"] = "stepId";
})(queryKeys || (exports.queryKeys = queryKeys = {}));
var probe;
(function(probe) {
    probe["Trust"] = "trust";
})(probe || (exports.probe = probe = {}));
var envKeys;
(function(envKeys) {
    envKeys["InngestSigningKey"] = "INNGEST_SIGNING_KEY";
    envKeys["InngestSigningKeyFallback"] = "INNGEST_SIGNING_KEY_FALLBACK";
    envKeys["InngestEventKey"] = "INNGEST_EVENT_KEY";
    /**
     * @deprecated Removed in v3. Use {@link InngestBaseUrl} instead.
     */ envKeys["InngestDevServerUrl"] = "INNGEST_DEVSERVER_URL";
    envKeys["InngestEnvironment"] = "INNGEST_ENV";
    envKeys["InngestBaseUrl"] = "INNGEST_BASE_URL";
    envKeys["InngestEventApiBaseUrl"] = "INNGEST_EVENT_API_BASE_URL";
    envKeys["InngestApiBaseUrl"] = "INNGEST_API_BASE_URL";
    envKeys["InngestServeHost"] = "INNGEST_SERVE_HOST";
    envKeys["InngestServePath"] = "INNGEST_SERVE_PATH";
    envKeys["InngestLogLevel"] = "INNGEST_LOG_LEVEL";
    envKeys["InngestStreaming"] = "INNGEST_STREAMING";
    envKeys["InngestDevMode"] = "INNGEST_DEV";
    envKeys["InngestAllowInBandSync"] = "INNGEST_ALLOW_IN_BAND_SYNC";
    /**
     * @deprecated It's unknown what this env var was used for, but we do not
     * provide explicit support for it. Prefer using `INNGEST_ENV` instead.
     */ envKeys["BranchName"] = "BRANCH_NAME";
    /**
     * The git branch of the commit the deployment was triggered by. Example:
     * `improve-about-page`.
     *
     * {@link https://vercel.com/docs/concepts/projects/environment-variables/system-environment-variables#system-environment-variables}
     */ envKeys["VercelBranch"] = "VERCEL_GIT_COMMIT_REF";
    /**
     * Expected to be `"1"` if defined.
     */ envKeys["IsVercel"] = "VERCEL";
    /**
     * The branch name of the current deployment. May only be accessible at build
     * time, but included here just in case.
     *
     * {@link https://developers.cloudflare.com/pages/platform/build-configuration/#environment-variables}
     */ envKeys["CloudflarePagesBranch"] = "CF_PAGES_BRANCH";
    /**
     * Expected to be `"1"` if defined.
     */ envKeys["IsCloudflarePages"] = "CF_PAGES";
    /**
     * The branch name of the deployment from Git to Netlify, if available.
     *
     * {@link https://docs.netlify.com/configure-builds/environment-variables/#git-metadata}
     */ envKeys["NetlifyBranch"] = "BRANCH";
    /**
     * Expected to be `"true"` if defined.
     */ envKeys["IsNetlify"] = "NETLIFY";
    /**
     * The Git branch for a service or deploy.
     *
     * {@link https://render.com/docs/environment-variables#all-services}
     */ envKeys["RenderBranch"] = "RENDER_GIT_BRANCH";
    /**
     * Expected to be `"true"` if defined.
     */ envKeys["IsRender"] = "RENDER";
    /**
     * The branch that triggered the deployment. Example: `main`
     *
     * {@link https://docs.railway.app/develop/variables#railway-provided-variables}
     */ envKeys["RailwayBranch"] = "RAILWAY_GIT_BRANCH";
    /**
     * The railway environment for the deployment. Example: `production`
     *
     * {@link https://docs.railway.app/develop/variables#railway-provided-variables}
     */ envKeys["RailwayEnvironment"] = "RAILWAY_ENVIRONMENT";
    envKeys["VercelEnvKey"] = "VERCEL_ENV";
    envKeys["OpenAiApiKey"] = "OPENAI_API_KEY";
    envKeys["GeminiApiKey"] = "GEMINI_API_KEY";
    envKeys["AnthropicApiKey"] = "ANTHROPIC_API_KEY";
})(envKeys || (exports.envKeys = envKeys = {}));
/**
 * Keys for accessing headers included in requests from Inngest to run
 * functions.
 *
 * Used internally to create handlers using `InngestCommHandler`, but can be
 * imported to be used if creating a custom handler outside of the package.
 *
 * @public
 */ var headerKeys;
(function(headerKeys) {
    headerKeys["ContentLength"] = "content-length";
    headerKeys["Signature"] = "x-inngest-signature";
    headerKeys["SdkVersion"] = "x-inngest-sdk";
    headerKeys["Environment"] = "x-inngest-env";
    headerKeys["Platform"] = "x-inngest-platform";
    headerKeys["Framework"] = "x-inngest-framework";
    headerKeys["NoRetry"] = "x-inngest-no-retry";
    headerKeys["RequestVersion"] = "x-inngest-req-version";
    headerKeys["RetryAfter"] = "retry-after";
    headerKeys["InngestServerKind"] = "x-inngest-server-kind";
    headerKeys["InngestExpectedServerKind"] = "x-inngest-expected-server-kind";
    headerKeys["InngestSyncKind"] = "x-inngest-sync-kind";
    headerKeys["EventIdSeed"] = "x-inngest-event-id-seed";
    headerKeys["TraceParent"] = "traceparent";
    headerKeys["TraceState"] = "tracestate";
})(headerKeys || (exports.headerKeys = headerKeys = {}));
exports.defaultInngestApiBaseUrl = "https://api.inngest.com/";
exports.defaultInngestEventBaseUrl = "https://inn.gs/";
exports.defaultDevServerHost = "http://localhost:8288/";
/**
 * Events that Inngest may send internally that can be used to trigger
 * functions.
 *
 * @public
 */ var internalEvents;
(function(internalEvents) {
    /**
     * A function has failed after exhausting all available retries. This event
     * will contain the original event and the error that caused the failure.
     */ internalEvents["FunctionFailed"] = "inngest/function.failed";
    internalEvents["FunctionInvoked"] = "inngest/function.invoked";
    internalEvents["FunctionFinished"] = "inngest/function.finished";
    internalEvents["FunctionCancelled"] = "inngest/function.cancelled";
    internalEvents["ScheduledTimer"] = "inngest/scheduled.timer";
})(internalEvents || (exports.internalEvents = internalEvents = {}));
exports.logPrefix = chalk_1.default.magenta.bold("[Inngest]");
exports.debugPrefix = "inngest";
exports.dummyEventKey = "NO_EVENT_KEY_SET";
var serverKind;
(function(serverKind) {
    serverKind["Dev"] = "dev";
    serverKind["Cloud"] = "cloud";
})(serverKind || (exports.serverKind = serverKind = {}));
var syncKind;
(function(syncKind) {
    syncKind["InBand"] = "in_band";
    syncKind["OutOfBand"] = "out_of_band";
})(syncKind || (exports.syncKind = syncKind = {})); //# sourceMappingURL=consts.js.map
}}),
"[project]/node_modules/inngest/version.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.version = void 0;
// Generated by genversion.
exports.version = "3.39.2"; //# sourceMappingURL=version.js.map
}}),
"[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.hashSigningKey = exports.hashEventKey = exports.stringifyUnknown = exports.timeStr = exports.slugify = exports.stringify = void 0;
const hash_js_1 = __turbopack_context__.r("[project]/node_modules/hash.js/lib/hash.js [app-route] (ecmascript)");
const json_stringify_safe_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/json-stringify-safe/stringify.js [app-route] (ecmascript)"));
const ms_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/ms/index.js [app-route] (ecmascript)"));
/**
 * Safely `JSON.stringify()` an `input`, handling circular refernences and
 * removing `BigInt` values.
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const stringify = (input)=>{
    return (0, json_stringify_safe_1.default)(input, (key, value)=>{
        if (typeof value !== "bigint") {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-return
            return value;
        }
    });
};
exports.stringify = stringify;
/**
 * Returns a slugified string used to generate consistent IDs.
 *
 * This can be used to generate a consistent ID for a function when migrating
 * from v2 to v3 of the SDK.
 *
 * @public
 */ const slugify = (str)=>{
    const join = "-";
    return str.toLowerCase().replace(/[^a-z0-9-]+/g, join).replace(/-+/g, join).split(join).filter(Boolean).join(join);
};
exports.slugify = slugify;
const millisecond = 1;
const second = millisecond * 1000;
const minute = second * 60;
const hour = minute * 60;
const day = hour * 24;
const week = day * 7;
/**
 * A collection of periods in milliseconds and their suffixes used when creating
 * time strings.
 */ const periods = [
    [
        "w",
        week
    ],
    [
        "d",
        day
    ],
    [
        "h",
        hour
    ],
    [
        "m",
        minute
    ],
    [
        "s",
        second
    ]
];
/**
 * Convert a given `Date`, `number`, or `ms`-compatible `string` to a
 * Inngest sleep-compatible time string (e.g. `"1d"` or `"2h3010s"`).
 *
 * Can optionally provide a `now` date to use as the base for the calculation,
 * otherwise a new date will be created on invocation.
 */ const timeStr = (/**
 * The future date to use to convert to a time string.
 */ input)=>{
    if (input instanceof Date) {
        return input.toISOString();
    }
    const milliseconds = typeof input === "string" ? (0, ms_1.default)(input) : input;
    const [, timeStr] = periods.reduce(([num, str], [suffix, period])=>{
        const numPeriods = Math.floor(num / period);
        if (numPeriods > 0) {
            return [
                num % period,
                `${str}${numPeriods}${suffix}`
            ];
        }
        return [
            num,
            str
        ];
    }, [
        milliseconds,
        ""
    ]);
    return timeStr;
};
exports.timeStr = timeStr;
/**
 * Given an unknown input, stringify it if it's a boolean, a number, or a
 * string, else return `undefined`.
 */ const stringifyUnknown = (input)=>{
    if (typeof input === "boolean" || typeof input === "number" || typeof input === "string") {
        return input.toString();
    }
};
exports.stringifyUnknown = stringifyUnknown;
const hashEventKey = (eventKey)=>{
    return (0, hash_js_1.sha256)().update(eventKey).digest("hex");
};
exports.hashEventKey = hashEventKey;
const hashSigningKey = (signingKey)=>{
    var _a;
    if (!signingKey) {
        return "";
    }
    const prefix = ((_a = signingKey.match(/^signkey-[\w]+-/)) === null || _a === void 0 ? void 0 : _a.shift()) || "";
    const key = signingKey.replace(/^signkey-[\w]+-/, "");
    // Decode the key from its hex representation into a bytestream
    return `${prefix}${(0, hash_js_1.sha256)().update(key, "hex").digest("hex")}`;
};
exports.hashSigningKey = hashSigningKey; //# sourceMappingURL=strings.js.map
}}),
"[project]/node_modules/inngest/helpers/env.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
// This file exists to help normalize process.env amongst the backend
// and frontend.  Many frontends (eg. Next, CRA) utilize webpack's DefinePlugin
// along with prefixes, meaning we have to explicitly use the full `process.env.FOO`
// string in order to read variables.
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.parseAsBoolean = exports.getResponse = exports.getFetch = exports.platformSupportsStreaming = exports.getPlatformName = exports.inngestHeaders = exports.allProcessEnv = exports.processEnv = exports.getEnvironmentName = exports.getMode = exports.Mode = exports.devServerHost = void 0;
const version_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/version.js [app-route] (ecmascript)");
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
/**
 * devServerHost returns the dev server host by searching for the INNGEST_DEVSERVER_URL
 * environment variable (plus project prefixces for eg. react, such as REACT_APP_INNGEST_DEVSERVER_URL).
 *
 * If not found this returns undefined, indicating that the env var has not been set.
 *
 * @example devServerHost()
 */ const devServerHost = (env = (0, exports.allProcessEnv)())=>{
    // devServerKeys are the env keys we search for to discover the dev server
    // URL.  This includes the standard key first, then includes prefixed keys
    // for use within common frameworks (eg. CRA, next).
    //
    // We have to fully write these using process.env as they're typically
    // processed using webpack's DefinePlugin, which is dumb and does a straight
    // text replacement instead of actually understanding the AST, despite webpack
    // being fully capable of understanding the AST.
    const prefixes = [
        "REACT_APP_",
        "NEXT_PUBLIC_"
    ];
    const keys = [
        consts_js_1.envKeys.InngestBaseUrl,
        consts_js_1.envKeys.InngestDevMode
    ];
    const values = keys.flatMap((key)=>{
        return prefixes.map((prefix)=>{
            return env[prefix + key];
        });
    });
    return values.find((v)=>{
        if (!v) {
            return;
        }
        try {
            return Boolean(new URL(v));
        } catch (_a) {
        // no-op
        }
    });
};
exports.devServerHost = devServerHost;
const checkFns = ((checks)=>checks)({
    equals: (actual, expected)=>actual === expected,
    "starts with": (actual, expected)=>{
        var _a;
        return expected ? (_a = actual === null || actual === void 0 ? void 0 : actual.startsWith(expected)) !== null && _a !== void 0 ? _a : false : false;
    },
    "is truthy": (actual)=>Boolean(actual),
    "is truthy but not": (actual, expected)=>Boolean(actual) && actual !== expected
});
const prodChecks = [
    [
        "CF_PAGES",
        "equals",
        "1"
    ],
    [
        "CONTEXT",
        "starts with",
        "prod"
    ],
    [
        "ENVIRONMENT",
        "starts with",
        "prod"
    ],
    [
        "NODE_ENV",
        "starts with",
        "prod"
    ],
    [
        "VERCEL_ENV",
        "starts with",
        "prod"
    ],
    [
        "DENO_DEPLOYMENT_ID",
        "is truthy"
    ],
    [
        consts_js_1.envKeys.VercelEnvKey,
        "is truthy but not",
        "development"
    ],
    [
        consts_js_1.envKeys.IsNetlify,
        "is truthy"
    ],
    [
        consts_js_1.envKeys.IsRender,
        "is truthy"
    ],
    [
        consts_js_1.envKeys.RailwayBranch,
        "is truthy"
    ],
    [
        consts_js_1.envKeys.IsCloudflarePages,
        "is truthy"
    ]
];
class Mode {
    constructor({ type, isExplicit, explicitDevUrl, env = (0, exports.allProcessEnv)() }){
        this.env = env;
        this.type = type;
        this.isExplicit = isExplicit || Boolean(explicitDevUrl);
        this.explicitDevUrl = explicitDevUrl;
    }
    get isDev() {
        return this.type === "dev";
    }
    get isCloud() {
        return this.type === "cloud";
    }
    get isInferred() {
        return !this.isExplicit;
    }
    /**
     * If we are explicitly in a particular mode, retrieve the URL that we are
     * sure we should be using, not considering any environment variables or other
     * influences.
     */ getExplicitUrl(defaultCloudUrl) {
        if (!this.isExplicit) {
            return undefined;
        }
        if (this.explicitDevUrl) {
            return this.explicitDevUrl.href;
        }
        if (this.isCloud) {
            return defaultCloudUrl;
        }
        if (this.isDev) {
            return consts_js_1.defaultDevServerHost;
        }
        return undefined;
    }
}
exports.Mode = Mode;
/**
 * Returns the mode of the current environment, based off of either passed
 * environment variables or `process.env`, or explicit settings.
 */ const getMode = ({ env = (0, exports.allProcessEnv)(), client, explicitMode } = {})=>{
    if (explicitMode) {
        return new Mode({
            type: explicitMode,
            isExplicit: true,
            env
        });
    }
    if (client === null || client === void 0 ? void 0 : client["mode"].isExplicit) {
        return client["mode"];
    }
    if (consts_js_1.envKeys.InngestDevMode in env) {
        if (typeof env[consts_js_1.envKeys.InngestDevMode] === "string") {
            try {
                const explicitDevUrl = new URL(env[consts_js_1.envKeys.InngestDevMode]);
                return new Mode({
                    type: "dev",
                    isExplicit: true,
                    explicitDevUrl,
                    env
                });
            } catch (_a) {
            // no-op
            }
        }
        const envIsDev = (0, exports.parseAsBoolean)(env[consts_js_1.envKeys.InngestDevMode]);
        if (typeof envIsDev === "boolean") {
            return new Mode({
                type: envIsDev ? "dev" : "cloud",
                isExplicit: true,
                env
            });
        }
    }
    const isProd = prodChecks.some(([key, checkKey, expected])=>{
        return checkFns[checkKey]((0, strings_js_1.stringifyUnknown)(env[key]), expected);
    });
    return new Mode({
        type: isProd ? "cloud" : "dev",
        isExplicit: false,
        env
    });
};
exports.getMode = getMode;
/**
 * getEnvironmentName returns the suspected branch name for this environment by
 * searching through a set of common environment variables.
 *
 * This could be used to determine if we're on a branch deploy or not, though it
 * should be noted that we don't know if this is the default branch or not.
 */ const getEnvironmentName = (env = (0, exports.allProcessEnv)())=>{
    /**
     * Order is important; more than one of these env vars may be set, so ensure
     * that we check the most specific, most reliable env vars first.
     */ return env[consts_js_1.envKeys.InngestEnvironment] || env[consts_js_1.envKeys.BranchName] || env[consts_js_1.envKeys.VercelBranch] || env[consts_js_1.envKeys.NetlifyBranch] || env[consts_js_1.envKeys.CloudflarePagesBranch] || env[consts_js_1.envKeys.RenderBranch] || env[consts_js_1.envKeys.RailwayBranch];
};
exports.getEnvironmentName = getEnvironmentName;
const processEnv = (key)=>{
    return (0, exports.allProcessEnv)()[key];
};
exports.processEnv = processEnv;
/**
 * allProcessEnv returns the current process environment variables, or an empty
 * object if they cannot be read, making sure we support environments other than
 * Node such as Deno, too.
 *
 * Using this ensures we don't dangerously access `process.env` in environments
 * where it may not be defined, such as Deno or the browser.
 */ const allProcessEnv = ()=>{
    // Node, or Node-like environments
    try {
        // eslint-disable-next-line @inngest/internal/process-warn
        if ("TURBOPACK compile-time truthy", 1) {
            // eslint-disable-next-line @inngest/internal/process-warn
            return process.env;
        }
    } catch (_err) {
    // noop
    }
    // Deno
    try {
        const env = Deno.env.toObject();
        if (env) {
            return env;
        }
    } catch (_err) {
    // noop
    }
    // Netlify
    try {
        const env = Netlify.env.toObject();
        if (env) {
            return env;
        }
    } catch (_err) {
    // noop
    }
    return {};
};
exports.allProcessEnv = allProcessEnv;
/**
 * Generate a standardised set of headers based on input and environment
 * variables.
 *
 *
 */ const inngestHeaders = (opts)=>{
    var _a;
    const sdkVersion = `inngest-js:v${version_js_1.version}`;
    const headers = {
        "Content-Type": "application/json",
        "User-Agent": sdkVersion,
        [consts_js_1.headerKeys.SdkVersion]: sdkVersion
    };
    if (opts === null || opts === void 0 ? void 0 : opts.framework) {
        headers[consts_js_1.headerKeys.Framework] = opts.framework;
    }
    if (opts === null || opts === void 0 ? void 0 : opts.expectedServerKind) {
        headers[consts_js_1.headerKeys.InngestExpectedServerKind] = opts.expectedServerKind;
    }
    const env = Object.assign(Object.assign({}, (0, exports.allProcessEnv)()), opts === null || opts === void 0 ? void 0 : opts.env);
    const inngestEnv = (opts === null || opts === void 0 ? void 0 : opts.inngestEnv) || (0, exports.getEnvironmentName)(env);
    if (inngestEnv) {
        headers[consts_js_1.headerKeys.Environment] = inngestEnv;
    }
    const platform = (0, exports.getPlatformName)(env);
    if (platform) {
        headers[consts_js_1.headerKeys.Platform] = platform;
    }
    return Object.assign(Object.assign(Object.assign({}, headers), (_a = opts === null || opts === void 0 ? void 0 : opts.client) === null || _a === void 0 ? void 0 : _a["headers"]), opts === null || opts === void 0 ? void 0 : opts.extras);
};
exports.inngestHeaders = inngestHeaders;
/**
 * A set of checks that, given an environment, will return `true` if the current
 * environment is running on the platform with the given name.
 */ const platformChecks = {
    /**
     * Vercel Edge Functions don't have access to environment variables unless
     * they are explicitly referenced in the top level code, but they do have a
     * global `EdgeRuntime` variable set that we can use to detect this.
     */ vercel: (env)=>env[consts_js_1.envKeys.IsVercel] === "1" || typeof EdgeRuntime === "string",
    netlify: (env)=>env[consts_js_1.envKeys.IsNetlify] === "true",
    "cloudflare-pages": (env)=>env[consts_js_1.envKeys.IsCloudflarePages] === "1",
    render: (env)=>env[consts_js_1.envKeys.IsRender] === "true",
    railway: (env)=>Boolean(env[consts_js_1.envKeys.RailwayEnvironment])
};
/**
 * A set of checks that, given an environment, will return `true` if the current
 * environment and platform supports streaming responses back to Inngest.
 *
 * Streaming capability is both framework and platform-based. Frameworks are
 * supported in serve handlers, and platforms are checked here.
 *
 * As such, this record declares which platforms we explicitly support for
 * streaming and is used by {@link platformSupportsStreaming}.
 */ const streamingChecks = {
    /**
     * "Vercel supports streaming for Serverless Functions, Edge Functions, and
     * React Server Components in Next.js projects."
     *
     * In practice, however, there are many reports of streaming not working as
     * expected on Serverless Functions, so we resort to only allowing streaming
     * for Edge Functions here.
     *
     * See {@link https://vercel.com/docs/frameworks/nextjs#streaming}
     */ vercel: (_framework, _env)=>typeof EdgeRuntime === "string",
    "cloudflare-pages": ()=>true
};
const getPlatformName = (env)=>{
    return Object.keys(platformChecks).find((key)=>{
        return platformChecks[key](env);
    });
};
exports.getPlatformName = getPlatformName;
/**
 * Returns `true` if we believe the current environment supports streaming
 * responses back to Inngest.
 *
 * We run a check directly related to the platform we believe we're running on,
 * usually based on environment variables.
 */ const platformSupportsStreaming = (framework, env = (0, exports.allProcessEnv)())=>{
    var _a, _b;
    return (_b = (_a = streamingChecks[(0, exports.getPlatformName)(env)]) === null || _a === void 0 ? void 0 : _a.call(streamingChecks, framework, env)) !== null && _b !== void 0 ? _b : false;
};
exports.platformSupportsStreaming = platformSupportsStreaming;
/**
 * A unique symbol used to mark a custom fetch implementation. We wrap the
 * implementations to provide some extra control when handling errors.
 */ const CUSTOM_FETCH_MARKER = Symbol("Custom fetch implementation");
/**
 * Given a potential fetch function, return the fetch function to use based on
 * this and the environment.
 */ const getFetch = (givenFetch)=>{
    /**
     * If we've explicitly been given a fetch function, use that.
     */ if (givenFetch) {
        if (CUSTOM_FETCH_MARKER in givenFetch) {
            return givenFetch;
        }
        /**
         * We wrap the given fetch function to provide some extra control when
         * handling errors.
         */ const customFetch = async (...args)=>{
            var _a;
            try {
                return await givenFetch(...args);
            } catch (err) {
                /**
                 * Capture warnings that are not simple fetch failures and highlight
                 * them for the user.
                 *
                 * We also use this opportunity to log the causing error, as code higher
                 * up the stack will likely abstract this.
                 */ if (!(err instanceof Error) || !((_a = err.message) === null || _a === void 0 ? void 0 : _a.startsWith("fetch failed"))) {
                    console.warn("A request failed when using a custom fetch implementation; this may be a misconfiguration. Make sure that your fetch client is correctly bound to the global scope.");
                    console.error(err);
                }
                throw err;
            }
        };
        /**
         * Mark the custom fetch implementation so that we can identify it later, in
         * addition to adding some runtime properties to it to make it seem as much
         * like the original fetch as possible.
         */ Object.defineProperties(customFetch, {
            [CUSTOM_FETCH_MARKER]: {},
            name: {
                value: givenFetch.name
            },
            length: {
                value: givenFetch.length
            }
        });
        return customFetch;
    }
    /**
     * Browser or Node 18+
     */ try {
        if (typeof globalThis !== "undefined" && "fetch" in globalThis) {
            return fetch.bind(globalThis);
        }
    } catch (err) {
    // no-op
    }
    /**
     * Existing polyfilled fetch
     */ if (typeof fetch !== "undefined") {
        return fetch;
    }
    /**
     * Environments where fetch cannot be found and must be polyfilled
     */ // eslint-disable-next-line @typescript-eslint/no-var-requires
    return __turbopack_context__.r("[project]/node_modules/cross-fetch/dist/node-ponyfill.js [app-route] (ecmascript)");
};
exports.getFetch = getFetch;
/**
 * If `Response` isn't included in this environment, it's probably an earlier
 * Node env that isn't already polyfilling. This function returns either the
 * native `Response` or a polyfilled one.
 */ const getResponse = ()=>{
    if (typeof Response !== "undefined") {
        return Response;
    }
    // eslint-disable-next-line @typescript-eslint/no-unsafe-return, @typescript-eslint/no-unsafe-member-access, @typescript-eslint/no-var-requires
    return __turbopack_context__.r("[project]/node_modules/cross-fetch/dist/node-ponyfill.js [app-route] (ecmascript)").Response;
};
exports.getResponse = getResponse;
/**
 * Given an unknown value, try to parse it as a `boolean`. Useful for parsing
 * environment variables that could be a selection of different values such as
 * `"true"`, `"1"`.
 *
 * If the value could not be confidently parsed as a `boolean` or was seen to be
 * `undefined`, this function returns `undefined`.
 */ const parseAsBoolean = (value)=>{
    if (typeof value === "boolean") {
        return value;
    }
    if (typeof value === "number") {
        return Boolean(value);
    }
    if (typeof value === "string") {
        const trimmed = value.trim().toLowerCase();
        if (trimmed === "undefined") {
            return undefined;
        }
        if ([
            "true",
            "1"
        ].includes(trimmed)) {
            return true;
        }
        return false;
    }
    return undefined;
};
exports.parseAsBoolean = parseAsBoolean; //# sourceMappingURL=env.js.map
}}),
"[project]/node_modules/inngest/helpers/devserver.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.devServerHost = exports.devServerUrl = exports.devServerAvailable = void 0;
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const env_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/env.js [app-route] (ecmascript)");
/**
 * Attempts to contact the dev server, returning a boolean indicating whether or
 * not it was successful.
 *
 * @example devServerUrl(process.env[envKeys.DevServerUrl], "/your-path")
 */ const devServerAvailable = async (/**
 * The host of the dev server. You should pass in an environment variable as
 * this parameter.
 */ host = consts_js_1.defaultDevServerHost, /**
 * The fetch implementation to use to communicate with the dev server.
 */ fetch)=>{
    try {
        const url = (0, exports.devServerUrl)(host, "/dev");
        const result = await fetch(url.toString());
        await result.json();
        return true;
    } catch (e) {
        return false;
    }
};
exports.devServerAvailable = devServerAvailable;
/**
 * devServerUrl returns a full URL for the given path name.
 *
 * Because Cloudflare/V8 platforms don't allow process.env, you are expected
 * to pass in the host from the dev server env key:
 *
 * @example devServerUrl(processEnv(envKeys.DevServerUrl), "/your-path")
 * @example devServerUrl("http://localhost:8288/", "/your-path")
 */ const devServerUrl = (host = (0, exports.devServerHost)(), pathname = "")=>{
    return new URL(pathname, host.includes("://") ? host : `http://${host}`);
};
exports.devServerUrl = devServerUrl;
/**
 * devServerHost exports the development server's domain by inspecting env
 * variables, or returns the default development server URL.
 *
 * This guarantees a specific URL as a string, as opposed to the env export
 * which only returns a value of the env var is set.
 */ const devServerHost = ()=>(0, env_js_1.devServerHost)() || consts_js_1.defaultDevServerHost;
exports.devServerHost = devServerHost; //# sourceMappingURL=devserver.js.map
}}),
"[project]/node_modules/inngest/helpers/enum.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.enumFromValue = void 0;
/**
 * Returns the value of an enum from a string value.
 *
 * If the value given is not a value from the enum, `undefined` is returned.
 */ const enumFromValue = (enumType, value)=>{
    if (Object.values(enumType).includes(value)) {
        return value;
    }
};
exports.enumFromValue = enumFromValue; //# sourceMappingURL=enum.js.map
}}),
"[project]/node_modules/inngest/components/NonRetriableError.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.NonRetriableError = void 0;
/**
 * An error that, when thrown, indicates to Inngest that the function should
 * cease all execution and not retry.
 *
 * A `message` must be provided, and an optional `cause` can be provided to
 * provide more context to the error.
 *
 * @public
 */ class NonRetriableError extends Error {
    constructor(message, options){
        super(message);
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        this.cause = options === null || options === void 0 ? void 0 : options.cause;
        this.name = "NonRetriableError";
    }
}
exports.NonRetriableError = NonRetriableError; //# sourceMappingURL=NonRetriableError.js.map
}}),
"[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.functionStoppedRunningErr = exports.rethrowError = exports.OutgoingResultError = exports.fixEventKeyMissingSteps = exports.prettyError = exports.getErrorMessage = exports.minifyPrettyError = exports.prettyErrorSplitter = exports.ErrCode = exports.deserializeError = exports.isSerializedError = exports.serializeError = void 0;
const chalk_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/chalk/source/index.js [app-route] (ecmascript)"));
const json_stringify_safe_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/json-stringify-safe/stringify.js [app-route] (ecmascript)"));
const serialize_error_cjs_1 = __turbopack_context__.r("[project]/node_modules/serialize-error-cjs/dist/index.js [app-route] (ecmascript)");
const strip_ansi_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/strip-ansi/index.js [app-route] (ecmascript)"));
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const NonRetriableError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/NonRetriableError.js [app-route] (ecmascript)");
const SERIALIZED_KEY = "__serialized";
const SERIALIZED_VALUE = true;
/**
 * Add first-class support for certain errors that we control, in addition to
 * built-in errors such as `TypeError`.
 *
 * Adding these allows these non-standard errors to be correctly serialized,
 * sent to Inngest, then deserialized back into the correct error type for users
 * to react to correctly.
 *
 * Note that these errors only support `message?: string | undefined` as the
 * input; more custom errors are not supported with this current strategy.
 */ serialize_error_cjs_1.errorConstructors.set("NonRetriableError", NonRetriableError_js_1.NonRetriableError);
/**
 * Serialise an error to a serialized JSON string.
 *
 * Errors do not serialise nicely to JSON, so we use this function to convert
 * them to a serialized JSON string. Doing this is also non-trivial for some
 * errors, so we use the `serialize-error` package to do it for us.
 *
 * See {@link https://www.npmjs.com/package/serialize-error}
 *
 * This function is a small wrapper around that package to also add a `type`
 * property to the serialised error, so that we can distinguish between
 * serialised errors and other objects.
 *
 * Will not reserialise existing serialised errors.
 */ const serializeError = (/**
 * The suspected error to serialize.
 */ subject, /**
 * If `true` and the error is not serializable, will return the original value
 * as `unknown` instead of coercing it to a serialized error.
 */ allowUnknown = false)=>{
    try {
        // Try to understand if this is already done.
        // Will handle stringified errors.
        const existingSerializedError = (0, exports.isSerializedError)(subject);
        if (existingSerializedError) {
            return existingSerializedError;
        }
        if (typeof subject === "object" && subject !== null) {
            // Is an object, so let's try and serialize it.
            const serializedErr = (0, serialize_error_cjs_1.serializeError)(subject);
            // Not a proper error was caught, so give us a chance to return `unknown`.
            if (!serializedErr.name && allowUnknown) {
                return subject;
            }
            // Serialization can succeed but assign no name or message, so we'll
            // map over the result here to ensure we have everything.
            // We'll just stringify the entire subject for the message, as this at
            // least provides some context for the user.
            const ret = Object.assign(Object.assign({}, serializedErr), {
                name: serializedErr.name || "Error",
                message: serializedErr.message || (0, json_stringify_safe_1.default)(subject) || "Unknown error; error serialization could not find a message.",
                stack: serializedErr.stack || "",
                [SERIALIZED_KEY]: SERIALIZED_VALUE
            });
            // If we have a cause, make sure we recursively serialize them too. We are
            // lighter with causes though; attempt to recursively serialize them, but
            // stop if we find something that doesn't work and just return `unknown`.
            let target = ret;
            const maxDepth = 5;
            for(let i = 0; i < maxDepth; i++){
                if (typeof target === "object" && target !== null && "cause" in target && target.cause) {
                    target = target.cause = (0, exports.serializeError)(target.cause, true);
                    continue;
                }
                break;
            }
            return ret;
        }
        // If it's not an object, it's hard to parse this as an Error. In this case,
        // we'll throw an error to start attempting backup strategies.
        throw new Error("Error is not an object; strange throw value.");
    } catch (err) {
        if (allowUnknown) {
            // If we are allowed to return unknown, we'll just return the original
            // value.
            return subject;
        }
        try {
            // If serialization fails, fall back to a regular Error and use the
            // original object as the message for an Error. We don't know what this
            // object looks like, so we can't do anything else with it.
            return Object.assign(Object.assign({}, (0, exports.serializeError)(new Error(typeof subject === "string" ? subject : (0, json_stringify_safe_1.default)(subject)), false)), {
                // Remove the stack; it's not relevant here
                stack: "",
                [SERIALIZED_KEY]: SERIALIZED_VALUE
            });
        } catch (err) {
            // If this failed, then stringifying the object also failed, so we'll just
            // return a completely generic error.
            // Failing to stringify the object is very unlikely.
            return {
                name: "Could not serialize source error",
                message: "Serializing the source error failed.",
                stack: "",
                [SERIALIZED_KEY]: SERIALIZED_VALUE
            };
        }
    }
};
exports.serializeError = serializeError;
/**
 * Check if an object or a string is a serialised error created by
 * {@link serializeError}.
 */ const isSerializedError = (value)=>{
    try {
        if (typeof value === "string") {
            const parsed = zod_1.z.object({
                [SERIALIZED_KEY]: zod_1.z.literal(SERIALIZED_VALUE),
                name: zod_1.z.enum([
                    ...Array.from(serialize_error_cjs_1.errorConstructors.keys())
                ]),
                message: zod_1.z.string(),
                stack: zod_1.z.string()
            }).passthrough().safeParse(JSON.parse(value));
            if (parsed.success) {
                return parsed.data;
            }
        }
        if (typeof value === "object" && value !== null) {
            const objIsSerializedErr = Object.prototype.hasOwnProperty.call(value, SERIALIZED_KEY) && // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access
            value[SERIALIZED_KEY] === SERIALIZED_VALUE;
            if (objIsSerializedErr) {
                return value;
            }
        }
    } catch (_a) {
    // no-op; we'll return undefined if parsing failed, as it isn't a serialized
    // error
    }
};
exports.isSerializedError = isSerializedError;
/**
 * Deserialise an error created by {@link serializeError}.
 *
 * Ensures we only deserialise errors that meet a minimum level of
 * applicability, inclusive of error handling to ensure that badly serialized
 * errors are still handled.
 */ const deserializeError = (subject, allowUnknown = false)=>{
    const requiredFields = [
        "name",
        "message"
    ];
    try {
        const hasRequiredFields = requiredFields.every((field)=>{
            return Object.prototype.hasOwnProperty.call(subject, field);
        });
        if (!hasRequiredFields) {
            throw new Error();
        }
        const deserializedErr = (0, serialize_error_cjs_1.deserializeError)(subject);
        if ("cause" in deserializedErr) {
            deserializedErr.cause = (0, exports.deserializeError)(deserializedErr.cause, true);
        }
        return deserializedErr;
    } catch (_a) {
        if (allowUnknown) {
            // If we are allowed to return unknown, we'll just return the original
            // value.
            return subject;
        }
        const err = new Error("Unknown error; could not reserialize");
        /**
         * Remove the stack so that it's not misleadingly shown as the Inngest
         * internals.
         */ err.stack = undefined;
        return err;
    }
};
exports.deserializeError = deserializeError;
var ErrCode;
(function(ErrCode) {
    ErrCode["NESTING_STEPS"] = "NESTING_STEPS";
    /**
     * Legacy v0 execution error code for when a function has changed and no
     * longer matches its in-progress state.
     *
     * @deprecated Not for use in latest execution method.
     */ ErrCode["NON_DETERMINISTIC_FUNCTION"] = "NON_DETERMINISTIC_FUNCTION";
    /**
     * Legacy v0 execution error code for when a function is found to be using
     * async actions after memoziation has occurred, which v0 doesn't support.
     *
     * @deprecated Not for use in latest execution method.
     */ ErrCode["ASYNC_DETECTED_AFTER_MEMOIZATION"] = "ASYNC_DETECTED_AFTER_MEMOIZATION";
    /**
     * Legacy v0 execution error code for when a function is found to be using
     * steps after a non-step async action has occurred.
     *
     * @deprecated Not for use in latest execution method.
     */ ErrCode["STEP_USED_AFTER_ASYNC"] = "STEP_USED_AFTER_ASYNC";
    ErrCode["AUTOMATIC_PARALLEL_INDEXING"] = "AUTOMATIC_PARALLEL_INDEXING";
})(ErrCode || (exports.ErrCode = ErrCode = {}));
exports.prettyErrorSplitter = "=================================================";
/**
 * Given an unknown `err`, mutate it to minify any pretty errors that it
 * contains.
 */ const minifyPrettyError = (err)=>{
    var _a, _b, _c, _d;
    try {
        if (!isError(err)) {
            return err;
        }
        const isPrettyError = err.message.includes(exports.prettyErrorSplitter);
        if (!isPrettyError) {
            return err;
        }
        const sanitizedMessage = (0, strip_ansi_1.default)(err.message);
        const message = ((_b = (_a = sanitizedMessage.split("  ")[1]) === null || _a === void 0 ? void 0 : _a.split("\n")[0]) === null || _b === void 0 ? void 0 : _b.trim()) || err.message;
        const code = ((_d = (_c = sanitizedMessage.split("\n\nCode: ")[1]) === null || _c === void 0 ? void 0 : _c.split("\n\n")[0]) === null || _d === void 0 ? void 0 : _d.trim()) || undefined;
        err.message = [
            code,
            message
        ].filter(Boolean).join(" - ");
        if (err.stack) {
            const sanitizedStack = (0, strip_ansi_1.default)(err.stack);
            const stackRest = sanitizedStack.split(`${exports.prettyErrorSplitter}\n`).slice(2).join("\n");
            err.stack = `${err.name}: ${err.message}\n${stackRest}`;
        }
        return err;
    } catch (noopErr) {
        return err;
    }
};
exports.minifyPrettyError = minifyPrettyError;
/**
 * Given an `err`, return a boolean representing whether it is in the shape of
 * an `Error` or not.
 */ const isError = (err)=>{
    try {
        if (err instanceof Error) {
            return true;
        }
        const hasName = Object.prototype.hasOwnProperty.call(err, "name");
        const hasMessage = Object.prototype.hasOwnProperty.call(err, "message");
        return hasName && hasMessage;
    } catch (noopErr) {
        return false;
    }
};
/**
 * Given an `unknown` object, retrieve the `message` property from it, or fall
 * back to the `fallback` string if it doesn't exist or is empty.
 */ const getErrorMessage = (err, fallback)=>{
    const { message } = zod_1.z.object({
        message: zod_1.z.string().min(1)
    }).catch({
        message: fallback
    }).parse(err);
    return message;
};
exports.getErrorMessage = getErrorMessage;
/**
 * Given a {@link PrettyError}, return a nicely-formatted string ready to log
 * or throw.
 *
 * Useful for ensuring that errors are logged in a consistent, helpful format
 * across the SDK by prompting for key pieces of information.
 */ const prettyError = ({ type = "error", whatHappened, otherwise, reassurance, toFixNow, why, consequences, stack, code })=>{
    var _a, _b;
    const { icon, colorFn } = {
        error: {
            icon: "❌",
            colorFn: chalk_1.default.red
        },
        warn: {
            icon: "⚠️",
            colorFn: chalk_1.default.yellow
        }
    }[type];
    let header = `${icon}  ${chalk_1.default.bold.underline(whatHappened.trim())}`;
    if (stack) {
        header += "\n" + [
            ...((_a = new Error().stack) === null || _a === void 0 ? void 0 : _a.split("\n").slice(1).filter(Boolean)) || []
        ].join("\n");
    }
    let toFixNowStr = (_b = Array.isArray(toFixNow) ? toFixNow.map((s)=>s.trim()).filter(Boolean).map((s, i)=>`\t${i + 1}. ${s}`).join("\n") : toFixNow === null || toFixNow === void 0 ? void 0 : toFixNow.trim()) !== null && _b !== void 0 ? _b : "";
    if (Array.isArray(toFixNow) && toFixNowStr) {
        toFixNowStr = `To fix this, you can take one of the following courses of action:\n\n${toFixNowStr}`;
    }
    let body = [
        reassurance === null || reassurance === void 0 ? void 0 : reassurance.trim(),
        why === null || why === void 0 ? void 0 : why.trim(),
        consequences === null || consequences === void 0 ? void 0 : consequences.trim()
    ].filter(Boolean).join(" ");
    body += body ? `\n\n${toFixNowStr}` : toFixNowStr;
    const trailer = [
        otherwise === null || otherwise === void 0 ? void 0 : otherwise.trim()
    ].filter(Boolean).join(" ");
    const message = [
        exports.prettyErrorSplitter,
        header,
        body,
        trailer,
        code ? `Code: ${code}` : "",
        exports.prettyErrorSplitter
    ].filter(Boolean).join("\n\n");
    return colorFn(message);
};
exports.prettyError = prettyError;
exports.fixEventKeyMissingSteps = [
    "Set the `INNGEST_EVENT_KEY` environment variable",
    `Pass a key to the \`new Inngest()\` constructor using the \`${"eventKey"}\` option`,
    `Use \`inngest.${"setEventKey"}()\` at runtime`
];
/**
 * An error that, when thrown, indicates internally that an outgoing operation
 * contains an error.
 *
 * We use this because serialized `data` sent back to Inngest may differ from
 * the error instance itself due to middleware.
 *
 * @internal
 */ class OutgoingResultError extends Error {
    constructor(result){
        super("OutgoingOpError");
        this.result = result;
    }
}
exports.OutgoingResultError = OutgoingResultError;
/**
 * Create a function that will rethrow an error with a prefix added to the
 * message.
 *
 * Useful for adding context to errors that are rethrown.
 *
 * @example
 * ```ts
 * await doSomeAction().catch(rethrowError("Failed to do some action"));
 * ```
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const rethrowError = (prefix)=>{
    return (err)=>{
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, @typescript-eslint/restrict-template-expressions
            err.message && (err.message = `${prefix}; ${err.message}`);
        } catch (noopErr) {
        // no-op
        } finally{
            // eslint-disable-next-line no-unsafe-finally
            throw err;
        }
    };
};
exports.rethrowError = rethrowError;
/**
 * Legacy v0 execution error for functions that don't support mixing steps and
 * regular async actions.
 */ const functionStoppedRunningErr = (code)=>{
    return (0, exports.prettyError)({
        whatHappened: "Your function was stopped from running",
        why: "We detected a mix of asynchronous logic, some using step tooling and some not.",
        consequences: "This can cause unexpected behaviour when a function is paused and resumed and is therefore strongly discouraged; we stopped your function to ensure nothing unexpected happened!",
        stack: true,
        toFixNow: "Ensure that your function is either entirely step-based or entirely non-step-based, by either wrapping all asynchronous logic in `step.run()` calls or by removing all `step.*()` calls.",
        otherwise: "For more information on why step functions work in this manner, see https://www.inngest.com/docs/functions/multi-step#gotchas",
        code
    });
};
exports.functionStoppedRunningErr = functionStoppedRunningErr; //# sourceMappingURL=errors.js.map
}}),
"[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.InngestExecution = exports.PREFERRED_EXECUTION_VERSION = exports.ExecutionVersion = void 0;
const debug_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/debug/src/index.js [app-route] (ecmascript)"));
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
/**
 * The execution models the SDK is aware of.
 *
 * This is used in a number of places to ensure all execution versions are
 * accounted for for a given operation.
 */ var ExecutionVersion;
(function(ExecutionVersion) {
    ExecutionVersion[ExecutionVersion["V0"] = 0] = "V0";
    ExecutionVersion[ExecutionVersion["V1"] = 1] = "V1";
    ExecutionVersion[ExecutionVersion["V2"] = 2] = "V2";
})(ExecutionVersion || (exports.ExecutionVersion = ExecutionVersion = {}));
/**
 * The preferred execution version that will be used by the SDK when handling
 * brand new runs where the Executor is allowing us to choose.
 *
 * Changing this should not ever be a breaking change, as this will only change
 * new runs, not existing ones.
 */ exports.PREFERRED_EXECUTION_VERSION = ExecutionVersion.V1;
class InngestExecution {
    constructor(options){
        this.options = options;
        this.debug = (0, debug_1.default)(`${consts_js_1.debugPrefix}:${this.options.runId}`);
    }
}
exports.InngestExecution = InngestExecution; //# sourceMappingURL=InngestExecution.js.map
}}),
"[project]/node_modules/inngest/types.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/**
 * Internal types and schemas used throughout the Inngest SDK.
 *
 * Note that types intended to be imported and utilized in userland code will be
 * exported from the main entrypoint of the SDK, `inngest`; importing types
 * directly from this file may result in breaking changes in non-major bumps as
 * only those exported from `inngest` are considered stable.
 *
 * @module
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.inBandSyncRequestBodySchema = exports.err = exports.ok = exports.functionConfigSchema = exports.logLevels = exports.sendEventResponseSchema = exports.incomingOpSchema = exports.StepOpCode = exports.jsonErrorSchema = void 0;
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const baseJsonErrorSchema = zod_1.z.object({
    name: zod_1.z.string().trim().optional(),
    error: zod_1.z.string().trim().optional(),
    message: zod_1.z.string().trim().optional(),
    stack: zod_1.z.string().trim().optional()
});
const maybeJsonErrorSchema = zod_1.z.lazy(()=>zod_1.z.object({
        name: zod_1.z.string().trim(),
        message: zod_1.z.string().trim(),
        stack: zod_1.z.string().trim().optional(),
        cause: zod_1.z.union([
            maybeJsonErrorSchema,
            zod_1.z.unknown()
        ]).optional()
    }));
exports.jsonErrorSchema = baseJsonErrorSchema.extend({
    cause: zod_1.z.union([
        maybeJsonErrorSchema,
        zod_1.z.unknown()
    ]).optional()
}).passthrough().catch({}).transform((val)=>{
    return Object.assign(Object.assign({}, val), {
        name: val.name || "Error",
        message: val.message || val.error || "Unknown error",
        stack: val.stack
    });
});
/**
 * Unique codes for the different types of operation that can be sent to Inngest
 * from SDK step functions.
 */ var StepOpCode;
(function(StepOpCode) {
    StepOpCode["WaitForSignal"] = "WaitForSignal";
    StepOpCode["WaitForEvent"] = "WaitForEvent";
    /**
     * Legacy equivalent to `"StepRun"`. Has mixed data wrapping (e.g. `data` or
     * `data.data` depending on SDK version), so this is phased out in favour of
     * `"StepRun"`, which never wraps.
     *
     * Note that it is still used for v0 executions for backwards compatibility.
     *
     * @deprecated Only used for v0 executions; use `"StepRun"` instead.
     */ StepOpCode["Step"] = "Step";
    StepOpCode["StepRun"] = "StepRun";
    StepOpCode["StepError"] = "StepError";
    StepOpCode["StepPlanned"] = "StepPlanned";
    StepOpCode["Sleep"] = "Sleep";
    /**
     * Used to signify that the executor has requested that a step run, but we
     * could not find that step.
     *
     * This is likely indicative that a step was renamed or removed from the
     * function.
     */ StepOpCode["StepNotFound"] = "StepNotFound";
    StepOpCode["InvokeFunction"] = "InvokeFunction";
    StepOpCode["AiGateway"] = "AIGateway";
    StepOpCode["Gateway"] = "Gateway";
})(StepOpCode || (exports.StepOpCode = StepOpCode = {}));
exports.incomingOpSchema = zod_1.z.object({
    id: zod_1.z.string().min(1),
    data: zod_1.z.any().optional(),
    error: zod_1.z.any().optional(),
    input: zod_1.z.any().optional()
});
exports.sendEventResponseSchema = zod_1.z.object({
    /**
     * Event IDs
     */ ids: zod_1.z.array(zod_1.z.string()).default([]),
    /**
     * HTTP Status Code. Will be undefined if no request was sent.
     */ status: zod_1.z.number().default(0),
    /**
     * Error message. Will be undefined if no error occurred.
     */ error: zod_1.z.string().optional()
});
/**
 * A set of log levels that can be used to control the amount of logging output
 * from various parts of the Inngest library.
 *
 * @public
 */ exports.logLevels = [
    "fatal",
    "error",
    "warn",
    "info",
    "debug",
    "silent"
];
/**
 * This schema is used internally to share the shape of a concurrency option
 * when validating config. We cannot add comments to Zod fields, so we just use
 * an extra type check to ensure it matches our exported expectations.
 */ const concurrencyOptionSchema = zod_1.z.strictObject({
    limit: zod_1.z.number(),
    key: zod_1.z.string().optional(),
    scope: zod_1.z.enum([
        "fn",
        "env",
        "account"
    ]).optional()
});
const _checkConcurrencySchemaAligns = true;
/**
 * The schema used to represent an individual function being synced with
 * Inngest.
 *
 * Note that this should only be used to validate the shape of a config object
 * and not used for feature compatibility, such as feature X being exclusive
 * with feature Y; these should be handled on the Inngest side.
 */ exports.functionConfigSchema = zod_1.z.strictObject({
    name: zod_1.z.string().optional(),
    id: zod_1.z.string(),
    triggers: zod_1.z.array(zod_1.z.union([
        zod_1.z.strictObject({
            event: zod_1.z.string(),
            expression: zod_1.z.string().optional()
        }),
        zod_1.z.strictObject({
            cron: zod_1.z.string()
        })
    ])),
    steps: zod_1.z.record(zod_1.z.strictObject({
        id: zod_1.z.string(),
        name: zod_1.z.string(),
        runtime: zod_1.z.strictObject({
            type: zod_1.z.union([
                zod_1.z.literal("http"),
                zod_1.z.literal("ws")
            ]),
            url: zod_1.z.string()
        }),
        retries: zod_1.z.strictObject({
            attempts: zod_1.z.number().optional()
        }).optional()
    })),
    idempotency: zod_1.z.string().optional(),
    batchEvents: zod_1.z.strictObject({
        maxSize: zod_1.z.number(),
        timeout: zod_1.z.string(),
        key: zod_1.z.string().optional()
    }).optional(),
    rateLimit: zod_1.z.strictObject({
        key: zod_1.z.string().optional(),
        limit: zod_1.z.number(),
        period: zod_1.z.string().transform((x)=>x)
    }).optional(),
    throttle: zod_1.z.strictObject({
        key: zod_1.z.string().optional(),
        limit: zod_1.z.number(),
        period: zod_1.z.string().transform((x)=>x),
        burst: zod_1.z.number().optional()
    }).optional(),
    singleton: zod_1.z.strictObject({
        key: zod_1.z.string().optional(),
        mode: zod_1.z.enum([
            "skip"
        ])
    }).optional(),
    cancel: zod_1.z.array(zod_1.z.strictObject({
        event: zod_1.z.string(),
        if: zod_1.z.string().optional(),
        timeout: zod_1.z.string().optional()
    })).optional(),
    debounce: zod_1.z.strictObject({
        key: zod_1.z.string().optional(),
        period: zod_1.z.string().transform((x)=>x),
        timeout: zod_1.z.string().transform((x)=>x).optional()
    }).optional(),
    timeouts: zod_1.z.strictObject({
        start: zod_1.z.string().transform((x)=>x).optional(),
        finish: zod_1.z.string().transform((x)=>x).optional()
    }).optional(),
    priority: zod_1.z.strictObject({
        run: zod_1.z.string().optional()
    }).optional(),
    concurrency: zod_1.z.union([
        zod_1.z.number(),
        concurrencyOptionSchema.transform((x)=>x),
        zod_1.z.array(concurrencyOptionSchema.transform((x)=>x)).min(1).max(2)
    ]).optional()
});
const ok = (data)=>{
    return {
        ok: true,
        value: data
    };
};
exports.ok = ok;
const err = (error)=>{
    return {
        ok: false,
        error
    };
};
exports.err = err;
exports.inBandSyncRequestBodySchema = zod_1.z.strictObject({
    url: zod_1.z.string()
}); //# sourceMappingURL=types.js.map
}}),
"[project]/node_modules/inngest/api/schema.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.batchSchema = exports.stepsSchemas = exports.errorSchema = void 0;
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const InngestExecution_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
exports.errorSchema = zod_1.z.object({
    error: zod_1.z.string(),
    status: zod_1.z.number()
});
const v0StepSchema = zod_1.z.record(zod_1.z.any().refine((v)=>typeof v !== "undefined", {
    message: "Values in steps must be defined"
})).optional().nullable();
const v1StepSchema = zod_1.z.record(zod_1.z.object({
    type: zod_1.z.literal("data").optional().default("data"),
    data: zod_1.z.any().refine((v)=>typeof v !== "undefined", {
        message: "Data in steps must be defined"
    })
}).strict().or(zod_1.z.object({
    type: zod_1.z.literal("error").optional().default("error"),
    error: types_js_1.jsonErrorSchema
}).strict()).or(zod_1.z.object({
    type: zod_1.z.literal("input").optional().default("input"),
    input: zod_1.z.any().refine((v)=>typeof v !== "undefined", {
        message: "If input is present it must not be `undefined`"
    })
}).strict())/**
     * If the result isn't a distcint `data` or `error` object, then it's
     * likely that the executor has set this directly to a value, for example
     * in the case of `sleep` or `waitForEvent`.
     *
     * In this case, pull the entire value through as data.
     */ // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
.or(zod_1.z.any().transform((v)=>({
        type: "data",
        data: v
    })))).default({});
const v2StepSchema = v1StepSchema;
exports.stepsSchemas = {
    [InngestExecution_js_1.ExecutionVersion.V0]: v0StepSchema,
    [InngestExecution_js_1.ExecutionVersion.V1]: v1StepSchema,
    [InngestExecution_js_1.ExecutionVersion.V2]: v2StepSchema
};
exports.batchSchema = zod_1.z.array(zod_1.z.record(zod_1.z.any()).transform((v)=>v)); //# sourceMappingURL=schema.js.map
}}),
"[project]/node_modules/inngest/helpers/functions.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.fetchAllFnData = exports.parseFnData = exports.undefinedToNull = exports.waterfall = exports.cacheFn = void 0;
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const schema_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/api/schema.js [app-route] (ecmascript)");
const InngestExecution_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
/**
 * Wraps a function with a cache. When the returned function is run, it will
 * cache the result and return it on subsequent calls.
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const cacheFn = (fn)=>{
    const key = "value";
    const cache = new Map();
    return (...args)=>{
        if (!cache.has(key)) {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            cache.set(key, fn(...args));
        }
        return cache.get(key);
    };
};
exports.cacheFn = cacheFn;
/**
 * Given an array of functions, return a new function that will run each
 * function in series and return the result of the final function. Regardless of
 * if the functions are synchronous or asynchronous, they'll be made into an
 * async promise chain.
 *
 * If an error is thrown, the waterfall will stop and return the error.
 *
 * Because this needs to support both sync and async functions, it only allows
 * functions that accept a single argument.
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
const waterfall = (fns, /**
 * A function that transforms the result of each function in the waterfall,
 * ready for the next function.
 *
 * Will not be called on the final function.
 */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
transform)=>{
    return (...args)=>{
        const chain = fns.reduce(async (acc, fn)=>{
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const prev = await acc;
            const output = await fn(prev);
            if (transform) {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                return await transform(prev, output);
            }
            if (typeof output === "undefined") {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                return prev;
            }
            return output;
        }, Promise.resolve(args[0]));
        return chain;
    };
};
exports.waterfall = waterfall;
/**
 * Given a value `v`, return `v` if it's not undefined, otherwise return `null`.
 */ const undefinedToNull = (v)=>{
    const isUndefined = typeof v === "undefined";
    return isUndefined ? null : v;
};
exports.undefinedToNull = undefinedToNull;
const fnDataVersionSchema = zod_1.z.object({
    version: zod_1.z.literal(-1).or(zod_1.z.literal(0)).or(zod_1.z.literal(1)).or(zod_1.z.literal(2)).optional().transform((v)=>{
        if (typeof v === "undefined") {
            console.debug(`No request version specified by executor; defaulting to v${InngestExecution_js_1.PREFERRED_EXECUTION_VERSION}`);
            return InngestExecution_js_1.PREFERRED_EXECUTION_VERSION;
        }
        return v === -1 ? InngestExecution_js_1.PREFERRED_EXECUTION_VERSION : v;
    })
});
const parseFnData = (data)=>{
    let version;
    try {
        ({ version } = fnDataVersionSchema.parse(data));
        const versionHandlers = {
            [InngestExecution_js_1.ExecutionVersion.V0]: ()=>Object.assign({
                    version: InngestExecution_js_1.ExecutionVersion.V0
                }, zod_1.z.object({
                    event: zod_1.z.record(zod_1.z.any()),
                    events: zod_1.z.array(zod_1.z.record(zod_1.z.any())).default([]),
                    steps: schema_js_1.stepsSchemas[InngestExecution_js_1.ExecutionVersion.V0],
                    ctx: zod_1.z.object({
                        run_id: zod_1.z.string(),
                        attempt: zod_1.z.number().default(0),
                        stack: zod_1.z.object({
                            stack: zod_1.z.array(zod_1.z.string()).nullable().transform((v)=>Array.isArray(v) ? v : []),
                            current: zod_1.z.number()
                        }).passthrough().optional().nullable()
                    }).optional().nullable(),
                    use_api: zod_1.z.boolean().default(false)
                }).parse(data)),
            [InngestExecution_js_1.ExecutionVersion.V1]: ()=>Object.assign({
                    version: InngestExecution_js_1.ExecutionVersion.V1
                }, zod_1.z.object({
                    event: zod_1.z.record(zod_1.z.any()),
                    events: zod_1.z.array(zod_1.z.record(zod_1.z.any())).default([]),
                    steps: schema_js_1.stepsSchemas[InngestExecution_js_1.ExecutionVersion.V1],
                    ctx: zod_1.z.object({
                        run_id: zod_1.z.string(),
                        attempt: zod_1.z.number().default(0),
                        disable_immediate_execution: zod_1.z.boolean().default(false),
                        use_api: zod_1.z.boolean().default(false),
                        stack: zod_1.z.object({
                            stack: zod_1.z.array(zod_1.z.string()).nullable().transform((v)=>Array.isArray(v) ? v : []),
                            current: zod_1.z.number()
                        }).passthrough().optional().nullable()
                    }).optional().nullable()
                }).parse(data)),
            [InngestExecution_js_1.ExecutionVersion.V2]: ()=>Object.assign({
                    version: InngestExecution_js_1.ExecutionVersion.V2
                }, zod_1.z.object({
                    event: zod_1.z.record(zod_1.z.any()),
                    events: zod_1.z.array(zod_1.z.record(zod_1.z.any())).default([]),
                    steps: schema_js_1.stepsSchemas[InngestExecution_js_1.ExecutionVersion.V2],
                    ctx: zod_1.z.object({
                        run_id: zod_1.z.string(),
                        attempt: zod_1.z.number().default(0),
                        disable_immediate_execution: zod_1.z.boolean().default(false),
                        use_api: zod_1.z.boolean().default(false),
                        stack: zod_1.z.object({
                            stack: zod_1.z.array(zod_1.z.string()).nullable().transform((v)=>Array.isArray(v) ? v : []),
                            current: zod_1.z.number()
                        }).passthrough().optional().nullable()
                    }).optional().nullable()
                }).parse(data))
        };
        return versionHandlers[version]();
    } catch (err) {
        throw new Error(parseFailureErr(err));
    }
};
exports.parseFnData = parseFnData;
const fetchAllFnData = async ({ data, api, version })=>{
    var _a, _b, _c, _d;
    const result = Object.assign({}, data);
    try {
        if (result.version === InngestExecution_js_1.ExecutionVersion.V0 && result.use_api || result.version === InngestExecution_js_1.ExecutionVersion.V1 && ((_a = result.ctx) === null || _a === void 0 ? void 0 : _a.use_api)) {
            if (!((_b = result.ctx) === null || _b === void 0 ? void 0 : _b.run_id)) {
                return (0, types_js_1.err)((0, errors_js_1.prettyError)({
                    whatHappened: "failed to attempt retrieving data from API",
                    consequences: "function execution can't continue",
                    why: "run_id is missing from context",
                    stack: true
                }));
            }
            const [evtResp, stepResp] = await Promise.all([
                api.getRunBatch(result.ctx.run_id),
                api.getRunSteps(result.ctx.run_id, version)
            ]);
            if (evtResp.ok) {
                result.events = evtResp.value;
            } else {
                return (0, types_js_1.err)((0, errors_js_1.prettyError)({
                    whatHappened: "failed to retrieve list of events",
                    consequences: "function execution can't continue",
                    why: (_c = evtResp.error) === null || _c === void 0 ? void 0 : _c.error,
                    stack: true
                }));
            }
            if (stepResp.ok) {
                result.steps = stepResp.value;
            } else {
                return (0, types_js_1.err)((0, errors_js_1.prettyError)({
                    whatHappened: "failed to retrieve steps for function run",
                    consequences: "function execution can't continue",
                    why: (_d = stepResp.error) === null || _d === void 0 ? void 0 : _d.error,
                    stack: true
                }));
            }
        }
        return (0, types_js_1.ok)(result);
    } catch (error) {
        // print it out for now.
        // move to something like protobuf so we don't have to deal with this
        console.error(error);
        return (0, types_js_1.err)(parseFailureErr(error));
    }
};
exports.fetchAllFnData = fetchAllFnData;
const parseFailureErr = (err)=>{
    let why;
    if (err instanceof zod_1.ZodError) {
        why = err.toString();
    }
    return (0, errors_js_1.prettyError)({
        whatHappened: "Failed to parse data from executor.",
        consequences: "Function execution can't continue.",
        toFixNow: "Make sure that your API is set up to parse incoming request bodies as JSON, like body-parser for Express (https://expressjs.com/en/resources/middleware/body-parser.html).",
        stack: true,
        why
    });
}; //# sourceMappingURL=functions.js.map
}}),
"[project]/node_modules/inngest/helpers/net.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.fetchWithAuthFallback = fetchWithAuthFallback;
exports.signDataWithKey = signDataWithKey;
const canonicalize_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/canonicalize/lib/canonicalize.js [app-route] (ecmascript)"));
const hash_js_1 = __turbopack_context__.r("[project]/node_modules/hash.js/lib/hash.js [app-route] (ecmascript)");
/**
 * Send an HTTP request with the given signing key. If the response is a 401 or
 * 403, then try again with the fallback signing key
 */ async function fetchWithAuthFallback({ authToken, authTokenFallback, fetch, options, url }) {
    let res = await fetch(url, Object.assign(Object.assign({}, options), {
        headers: Object.assign(Object.assign({}, options === null || options === void 0 ? void 0 : options.headers), {
            Authorization: `Bearer ${authToken}`
        })
    }));
    if ([
        401,
        403
    ].includes(res.status) && authTokenFallback) {
        res = await fetch(url, Object.assign(Object.assign({}, options), {
            headers: Object.assign(Object.assign({}, options === null || options === void 0 ? void 0 : options.headers), {
                Authorization: `Bearer ${authTokenFallback}`
            })
        }));
    }
    return res;
}
function signDataWithKey(data, signingKey, ts) {
    // Calculate the HMAC of the request body ourselves.
    // We make the assumption here that a stringified body is the same as the
    // raw bytes; it may be pertinent in the future to always parse, then
    // canonicalize the body to ensure it's consistent.
    const encoded = typeof data === "string" ? data : (0, canonicalize_1.default)(data);
    // Remove the `/signkey-[test|prod]-/` prefix from our signing key to calculate the HMAC.
    const key = signingKey.replace(/signkey-\w+-/, "");
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument, @typescript-eslint/no-explicit-any
    const mac = (0, hash_js_1.hmac)(hash_js_1.sha256, key).update(encoded).update(ts).digest("hex");
    return mac;
} //# sourceMappingURL=net.js.map
}}),
"[project]/node_modules/inngest/helpers/stream.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createStream = void 0;
const strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
/**
 * Creates a {@link ReadableStream} that sends a `value` every `interval`
 * milliseconds as a heartbeat, intended to keep a stream open.
 *
 * Returns the `stream` itself and a `finalize` function that can be used to
 * close the stream and send a final value.
 */ const createStream = (opts)=>{
    var _a, _b;
    /**
     * We need to resolve this promise with both the stream and the `finalize`
     * function, but having them both instantiated synchronously is difficult, as
     * we need access to the stream's internals too.
     *
     * We create this cheeky deferred promise to grab the internal `finalize`
     * value. Be warned that simpler solutions may appear to compile, but fail at
     * runtime due to variables not being assigned; make sure to test your code!
     */ let passFinalize;
    const finalizeP = new Promise((resolve)=>{
        passFinalize = resolve;
    });
    const interval = (_a = opts === null || opts === void 0 ? void 0 : opts.interval) !== null && _a !== void 0 ? _a : 3000;
    const value = (_b = opts === null || opts === void 0 ? void 0 : opts.value) !== null && _b !== void 0 ? _b : " ";
    // eslint-disable-next-line @typescript-eslint/no-misused-promises, no-async-promise-executor
    return new Promise(async (resolve, reject)=>{
        try {
            const stream = new ReadableStream({
                start (controller) {
                    const encoder = new TextEncoder();
                    const heartbeat = setInterval(()=>{
                        controller.enqueue(encoder.encode(value));
                    }, interval);
                    const finalize = (data)=>{
                        clearInterval(heartbeat);
                        // `data` may be a `Promise`. If it is, we need to wait for it to
                        // resolve before sending it. To support this elegantly we'll always
                        // assume it's a promise and handle that case.
                        void Promise.resolve(data).then((resolvedData)=>{
                            controller.enqueue(encoder.encode((0, strings_js_1.stringify)(resolvedData)));
                            controller.close();
                        });
                    };
                    passFinalize(finalize);
                }
            });
            resolve({
                stream,
                finalize: await finalizeP
            });
        } catch (err) {
            reject(err);
        }
    });
};
exports.createStream = createStream; //# sourceMappingURL=stream.js.map
}}),
"[project]/node_modules/inngest/components/InngestCommHandler.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __classPrivateFieldGet = this && this.__classPrivateFieldGet || function(receiver, state, kind, f) {
    if (kind === "a" && !f) throw new TypeError("Private accessor was defined without a getter");
    if (typeof state === "function" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError("Cannot read private member from an object whose class did not declare it");
    return kind === "m" ? f : kind === "a" ? f.call(receiver) : f ? f.value : state.get(receiver);
};
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
var _RequestSignature_instances, _RequestSignature_verifySignature;
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.InngestCommHandler = void 0;
const debug_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/debug/src/index.js [app-route] (ecmascript)"));
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const ServerTiming_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/ServerTiming.js [app-route] (ecmascript)");
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const devserver_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/devserver.js [app-route] (ecmascript)");
const enum_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/enum.js [app-route] (ecmascript)");
const env_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/env.js [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
const functions_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/functions.js [app-route] (ecmascript)");
const net_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/net.js [app-route] (ecmascript)");
const promises_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/promises.js [app-route] (ecmascript)");
const stream_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/stream.js [app-route] (ecmascript)");
const strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const version_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/version.js [app-route] (ecmascript)");
const InngestExecution_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)");
/**
 * A schema for the response from Inngest when registering.
 */ const registerResSchema = zod_1.z.object({
    status: zod_1.z.number().default(200),
    skipped: zod_1.z.boolean().optional().default(false),
    modified: zod_1.z.boolean().optional().default(false),
    error: zod_1.z.string().default("Successfully registered")
});
/**
 * `InngestCommHandler` is a class for handling incoming requests from Inngest (or
 * Inngest's tooling such as the dev server or CLI) and taking appropriate
 * action for any served functions.
 *
 * All handlers (Next.js, RedwoodJS, Remix, Deno Fresh, etc.) are created using
 * this class; the exposed `serve` function will - most commonly - create an
 * instance of `InngestCommHandler` and then return `instance.createHandler()`.
 *
 * See individual parameter details for more information, or see the
 * source code for an existing handler, e.g.
 * {@link https://github.com/inngest/inngest-js/blob/main/src/next.ts}
 *
 * @example
 * ```
 * // my-custom-handler.ts
 * import {
 *   InngestCommHandler,
 *   type ServeHandlerOptions,
 * } from "./components/InngestCommHandler";
 *
 * export const serve = (options: ServeHandlerOptions) => {
 *   const handler = new InngestCommHandler({
 *     frameworkName: "my-custom-handler",
 *     ...options,
 *     handler: (req: Request) => {
 *       return {
 *         body: () => req.json(),
 *         headers: (key) => req.headers.get(key),
 *         method: () => req.method,
 *         url: () => new URL(req.url, `https://${req.headers.get("host") || ""}`),
 *         transformResponse: ({ body, status, headers }) => {
 *           return new Response(body, { status, headers });
 *         },
 *       };
 *     },
 *   });
 *
 *   return handler.createHandler();
 * };
 * ```
 *
 * @public
 */ class InngestCommHandler {
    constructor(options){
        var _a;
        /**
         * A private collection of functions that are being served. This map is used
         * to find and register functions when interacting with Inngest Cloud.
         */ this.fns = {};
        this.env = (0, env_js_1.allProcessEnv)();
        // Set input options directly so we can reference them later
        this._options = options;
        /**
         * v2 -> v3 migration error.
         *
         * If a serve handler is passed a client as the first argument, it'll be
         * spread in to these options. We should be able to detect this by picking
         * up a unique property on the object.
         */ if (Object.prototype.hasOwnProperty.call(options, "eventKey")) {
            throw new Error(`${consts_js_1.logPrefix} You've passed an Inngest client as the first argument to your serve handler. This is no longer supported in v3; please pass the Inngest client as the \`client\` property of an options object instead. See https://www.inngest.com/docs/sdk/migration`);
        }
        this.frameworkName = options.frameworkName;
        this.client = options.client;
        if (options.id) {
            console.warn(`${consts_js_1.logPrefix} The \`id\` serve option is deprecated and will be removed in v4`);
        }
        this.id = options.id || this.client.id;
        this.handler = options.handler;
        /**
         * Provide a hidden option to allow expired signatures to be accepted during
         * testing.
         */ this.allowExpiredSignatures = Boolean(// eslint-disable-next-line @typescript-eslint/no-unsafe-member-access, prefer-rest-params
        (_a = arguments["0"]) === null || _a === void 0 ? void 0 : _a.__testingAllowExpiredSignatures);
        // Ensure we filter any undefined functions in case of missing imports.
        this.rawFns = options.functions.filter(Boolean);
        if (this.rawFns.length !== options.functions.length) {
            // TODO PrettyError
            console.warn(`Some functions passed to serve() are undefined and misconfigured.  Please check your imports.`);
        }
        this.fns = this.rawFns.reduce((acc, fn)=>{
            const configs = fn["getConfig"]({
                baseUrl: new URL("https://example.com"),
                appPrefix: this.id
            });
            const fns = configs.reduce((acc, { id }, index)=>{
                return Object.assign(Object.assign({}, acc), {
                    [id]: {
                        fn,
                        onFailure: Boolean(index)
                    }
                });
            }, {});
            configs.forEach(({ id })=>{
                if (acc[id]) {
                    // TODO PrettyError
                    throw new Error(`Duplicate function ID "${id}"; please change a function's name or provide an explicit ID to avoid conflicts.`);
                }
            });
            return Object.assign(Object.assign({}, acc), fns);
        }, {});
        this.inngestRegisterUrl = new URL("/fn/register", this.apiBaseUrl);
        this.signingKey = options.signingKey;
        this.signingKeyFallback = options.signingKeyFallback;
        this._serveHost = options.serveHost || this.env[consts_js_1.envKeys.InngestServeHost];
        this._servePath = options.servePath || this.env[consts_js_1.envKeys.InngestServePath];
        this.skipSignatureValidation = options.skipSignatureValidation || false;
        const defaultLogLevel = "info";
        this.logLevel = zod_1.z.enum(types_js_1.logLevels).default(defaultLogLevel).catch((ctx)=>{
            this.log("warn", `Unknown log level passed: ${String(ctx.input)}; defaulting to ${defaultLogLevel}`);
            return defaultLogLevel;
        }).parse(options.logLevel || this.env[consts_js_1.envKeys.InngestLogLevel]);
        if (this.logLevel === "debug") {
            /**
             * `debug` is an old library; sometimes its runtime detection doesn't work
             * for newer pairings of framework/runtime.
             *
             * One silly symptom of this is that `Debug()` returns an anonymous
             * function with no extra properties instead of a `Debugger` instance if
             * the wrong code is consumed following a bad detection. This results in
             * the following `.enable()` call failing, so we just try carefully to
             * enable it here.
             */ if (debug_1.default.enable && typeof debug_1.default.enable === "function") {
                debug_1.default.enable(`${consts_js_1.debugPrefix}:*`);
            }
        }
        const defaultStreamingOption = false;
        this.streaming = zod_1.z.union([
            zod_1.z.enum([
                "allow",
                "force"
            ]),
            zod_1.z.literal(false)
        ]).default(defaultStreamingOption).catch((ctx)=>{
            this.log("warn", `Unknown streaming option passed: ${String(ctx.input)}; defaulting to ${String(defaultStreamingOption)}`);
            return defaultStreamingOption;
        }).parse(options.streaming || this.env[consts_js_1.envKeys.InngestStreaming]);
        this.fetch = options.fetch ? (0, env_js_1.getFetch)(options.fetch) : this.client["fetch"];
    }
    /**
     * Get the API base URL for the Inngest API.
     *
     * This is a getter to encourage checking the environment for the API base URL
     * each time it's accessed, as it may change during execution.
     */ get apiBaseUrl() {
        return this._options.baseUrl || this.env[consts_js_1.envKeys.InngestApiBaseUrl] || this.env[consts_js_1.envKeys.InngestBaseUrl] || this.client.apiBaseUrl || consts_js_1.defaultInngestApiBaseUrl;
    }
    /**
     * Get the event API base URL for the Inngest API.
     *
     * This is a getter to encourage checking the environment for the event API
     * base URL each time it's accessed, as it may change during execution.
     */ get eventApiBaseUrl() {
        return this._options.baseUrl || this.env[consts_js_1.envKeys.InngestEventApiBaseUrl] || this.env[consts_js_1.envKeys.InngestBaseUrl] || this.client.eventBaseUrl || consts_js_1.defaultInngestEventBaseUrl;
    }
    /**
     * The host used to access the Inngest serve endpoint, e.g.:
     *
     *     "https://myapp.com"
     *
     * By default, the library will try to infer this using request details such
     * as the "Host" header and request path, but sometimes this isn't possible
     * (e.g. when running in a more controlled environments such as AWS Lambda or
     * when dealing with proxies/redirects).
     *
     * Provide the custom hostname here to ensure that the path is reported
     * correctly when registering functions with Inngest.
     *
     * To also provide a custom path, use `servePath`.
     */ get serveHost() {
        return this._serveHost || this.env[consts_js_1.envKeys.InngestServeHost];
    }
    /**
     * The path to the Inngest serve endpoint. e.g.:
     *
     *     "/some/long/path/to/inngest/endpoint"
     *
     * By default, the library will try to infer this using request details such
     * as the "Host" header and request path, but sometimes this isn't possible
     * (e.g. when running in a more controlled environments such as AWS Lambda or
     * when dealing with proxies/redirects).
     *
     * Provide the custom path (excluding the hostname) here to ensure that the
     * path is reported correctly when registering functions with Inngest.
     *
     * To also provide a custom hostname, use `serveHost`.
     *
     * This is a getter to encourage checking the environment for the serve path
     * each time it's accessed, as it may change during execution.
     */ get servePath() {
        return this._servePath || this.env[consts_js_1.envKeys.InngestServePath];
    }
    get hashedEventKey() {
        if (!this.client["eventKey"] || this.client["eventKey"] === consts_js_1.dummyEventKey) {
            return undefined;
        }
        return (0, strings_js_1.hashEventKey)(this.client["eventKey"]);
    }
    // hashedSigningKey creates a sha256 checksum of the signing key with the
    // same signing key prefix.
    get hashedSigningKey() {
        if (!this.signingKey) {
            return undefined;
        }
        return (0, strings_js_1.hashSigningKey)(this.signingKey);
    }
    get hashedSigningKeyFallback() {
        if (!this.signingKeyFallback) {
            return undefined;
        }
        return (0, strings_js_1.hashSigningKey)(this.signingKeyFallback);
    }
    /**
     * Returns a `boolean` representing whether this handler will stream responses
     * or not. Takes into account the user's preference and the platform's
     * capabilities.
     */ async shouldStream(actions) {
        const rawProbe = await actions.queryStringWithDefaults("testing for probe", consts_js_1.queryKeys.Probe);
        if (rawProbe !== undefined) {
            return false;
        }
        // We must be able to stream responses to continue.
        if (!actions.transformStreamingResponse) {
            return false;
        }
        // If the user has forced streaming, we should always stream.
        if (this.streaming === "force") {
            return true;
        }
        // If the user has allowed streaming, we should stream if the platform
        // supports it.
        return this.streaming === "allow" && (0, env_js_1.platformSupportsStreaming)(this.frameworkName, this.env);
    }
    /**
     * `createHandler` should be used to return a type-equivalent version of the
     * `handler` specified during instantiation.
     *
     * @example
     * ```
     * // my-custom-handler.ts
     * import {
     *   InngestCommHandler,
     *   type ServeHandlerOptions,
     * } from "./components/InngestCommHandler";
     *
     * export const serve = (options: ServeHandlerOptions) => {
     *   const handler = new InngestCommHandler({
     *     frameworkName: "my-custom-handler",
     *     ...options,
     *     handler: (req: Request) => {
     *       return {
     *         body: () => req.json(),
     *         headers: (key) => req.headers.get(key),
     *         method: () => req.method,
     *         url: () => new URL(req.url, `https://${req.headers.get("host") || ""}`),
     *         transformResponse: ({ body, status, headers }) => {
     *           return new Response(body, { status, headers });
     *         },
     *       };
     *     },
     *   });
     *
     *   return handler.createHandler();
     * };
     * ```
     */ createHandler() {
        const handler = async (...args)=>{
            var _a, _b;
            const timer = new ServerTiming_js_1.ServerTiming();
            /**
             * Used for testing, allow setting action overrides externally when
             * calling the handler. Always search the final argument.
             */ const lastArg = args[args.length - 1];
            const actionOverrides = typeof lastArg === "object" && lastArg !== null && "actionOverrides" in lastArg && typeof lastArg["actionOverrides"] === "object" && lastArg["actionOverrides"] !== null ? lastArg["actionOverrides"] : {};
            /**
             * We purposefully `await` the handler, as it could be either sync or
             * async.
             */ const rawActions = Object.assign(Object.assign({}, await timer.wrap("handler", ()=>this.handler(...args)).catch((0, errors_js_1.rethrowError)("Serve handler failed to run"))), actionOverrides);
            /**
             * Map over every `action` in `rawActions` and create a new `actions`
             * object where each function is safely promisified with each access
             * requiring a reason.
             *
             * This helps us provide high quality errors about what's going wrong for
             * each access without having to wrap every access in a try/catch.
             */ const promisifiedActions = Object.entries(rawActions).reduce((acc, [key, value])=>{
                if (typeof value !== "function") {
                    return acc;
                }
                return Object.assign(Object.assign({}, acc), {
                    [key]: (reason, ...args)=>{
                        const errMessage = [
                            `Failed calling \`${key}\` from serve handler`,
                            reason
                        ].filter(Boolean).join(" when ");
                        const fn = ()=>value(...args);
                        return (0, promises_js_1.runAsPromise)(fn).catch((0, errors_js_1.rethrowError)(errMessage)).catch((err)=>{
                            this.log("error", err);
                            throw err;
                        });
                    }
                });
            }, {});
            /**
             * Mapped promisified handlers from userland `serve()` function mixed in
             * with some helpers.
             */ const actions = Object.assign(Object.assign(Object.assign({}, promisifiedActions), {
                queryStringWithDefaults: async (reason, key)=>{
                    var _a;
                    const url = await actions.url(reason);
                    const ret = await ((_a = actions.queryString) === null || _a === void 0 ? void 0 : _a.call(actions, reason, key, url)) || url.searchParams.get(key) || undefined;
                    return ret;
                }
            }), actionOverrides);
            const [env, expectedServerKind] = await Promise.all([
                (_a = actions.env) === null || _a === void 0 ? void 0 : _a.call(actions, "starting to handle request"),
                actions.headers("checking expected server kind", consts_js_1.headerKeys.InngestServerKind)
            ]);
            // Always make sure to merge whatever env we've been given with
            // `process.env`; some platforms may not provide all the necessary
            // environment variables or may use two sources.
            this.env = Object.assign(Object.assign({}, (0, env_js_1.allProcessEnv)()), env);
            const getInngestHeaders = ()=>(0, env_js_1.inngestHeaders)({
                    env: this.env,
                    framework: this.frameworkName,
                    client: this.client,
                    expectedServerKind: expectedServerKind || undefined,
                    extras: {
                        "Server-Timing": timer.getHeader()
                    }
                });
            const assumedMode = (0, env_js_1.getMode)({
                env: this.env,
                client: this.client
            });
            if (assumedMode.isExplicit) {
                this._mode = assumedMode;
            } else {
                const serveIsProd = await ((_b = actions.isProduction) === null || _b === void 0 ? void 0 : _b.call(actions, "starting to handle request"));
                if (typeof serveIsProd === "boolean") {
                    this._mode = new env_js_1.Mode({
                        type: serveIsProd ? "cloud" : "dev",
                        isExplicit: false
                    });
                } else {
                    this._mode = assumedMode;
                }
            }
            this.upsertKeysFromEnv();
            const methodP = actions.method("starting to handle request");
            const headerPromises = [
                consts_js_1.headerKeys.TraceParent,
                consts_js_1.headerKeys.TraceState
            ].map(async (header)=>{
                const value = await actions.headers(`fetching ${header} for forwarding`, header);
                return {
                    header,
                    value
                };
            });
            const contentLength = await actions.headers("checking signature for request", consts_js_1.headerKeys.ContentLength).then((value)=>{
                if (!value) {
                    return undefined;
                }
                return parseInt(value, 10);
            });
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            const [signature, method, body] = await Promise.all([
                actions.headers("checking signature for request", consts_js_1.headerKeys.Signature).then((headerSignature)=>{
                    return headerSignature !== null && headerSignature !== void 0 ? headerSignature : undefined;
                }),
                methodP,
                methodP.then((method)=>{
                    if (method === "POST" || method === "PUT") {
                        if (!contentLength) {
                            // Return empty string because req.json() will throw an error.
                            return "";
                        }
                        return actions.body(`checking body for request signing as method is ${method}`);
                    }
                    return "";
                })
            ]);
            const signatureValidation = this.validateSignature(signature, body);
            const headersToForwardP = Promise.all(headerPromises).then((fetchedHeaders)=>{
                return fetchedHeaders.reduce((acc, { header, value })=>{
                    if (value) {
                        acc[header] = value;
                    }
                    return acc;
                }, {});
            });
            const actionRes = timer.wrap("action", ()=>this.handleAction({
                    actions,
                    timer,
                    getInngestHeaders,
                    reqArgs: args,
                    signatureValidation,
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                    body,
                    method,
                    headers: headersToForwardP
                }));
            /**
             * Prepares an action response by merging returned data to provide
             * trailing information such as `Server-Timing` headers.
             *
             * It should always prioritize the headers returned by the action, as they
             * may contain important information such as `Content-Type`.
             */ const prepareActionRes = async (res)=>{
                var _a;
                const headers = Object.assign(Object.assign(Object.assign(Object.assign({}, getInngestHeaders()), await headersToForwardP), res.headers), res.version === null ? {} : {
                    [consts_js_1.headerKeys.RequestVersion]: ((_a = res.version) !== null && _a !== void 0 ? _a : InngestExecution_js_1.PREFERRED_EXECUTION_VERSION).toString()
                });
                let signature;
                try {
                    signature = await signatureValidation.then((result)=>{
                        if (!result.success || !result.keyUsed) {
                            return undefined;
                        }
                        return this.getResponseSignature(result.keyUsed, res.body);
                    });
                } catch (err) {
                    // If we fail to sign, retun a 500 with the error.
                    return Object.assign(Object.assign({}, res), {
                        headers,
                        body: (0, strings_js_1.stringify)((0, errors_js_1.serializeError)(err)),
                        status: 500
                    });
                }
                if (signature) {
                    headers[consts_js_1.headerKeys.Signature] = signature;
                }
                return Object.assign(Object.assign({}, res), {
                    headers
                });
            };
            if (await this.shouldStream(actions)) {
                const method = await actions.method("starting streaming response");
                if (method === "POST") {
                    const { stream, finalize } = await (0, stream_js_1.createStream)();
                    /**
                     * Errors are handled by `handleAction` here to ensure that an
                     * appropriate response is always given.
                     */ void actionRes.then((res)=>{
                        return finalize(prepareActionRes(res));
                    });
                    return timer.wrap("res", ()=>{
                        var _a;
                        return (_a = actions.transformStreamingResponse) === null || _a === void 0 ? void 0 : _a.call(actions, "starting streaming response", {
                            status: 201,
                            headers: getInngestHeaders(),
                            body: stream,
                            version: null
                        });
                    });
                }
            }
            return timer.wrap("res", async ()=>{
                return actionRes.then(prepareActionRes).then((actionRes)=>{
                    // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                    return actions.transformResponse("sending back response", actionRes);
                });
            });
        };
        /**
         * Some platforms check (at runtime) the length of the function being used
         * to handle an endpoint. If this is a variadic function, it will fail that
         * check.
         *
         * Therefore, we expect the arguments accepted to be the same length as the
         * `handler` function passed internally.
         *
         * We also set a name to avoid a common useless name in tracing such as
         * `"anonymous"` or `"bound function"`.
         *
         * https://github.com/getsentry/sentry-javascript/issues/3284
         */ Object.defineProperties(handler, {
            name: {
                value: "InngestHandler"
            },
            length: {
                value: this.handler.length
            }
        });
        return handler;
    }
    get mode() {
        return this._mode;
    }
    set mode(m) {
        this._mode = m;
        if (m) {
            this.client["mode"] = m;
        }
    }
    /**
     * Given a set of functions to check if an action is available from the
     * instance's handler, enact any action that is found.
     *
     * This method can fetch varying payloads of data, but ultimately is the place
     * where _decisions_ are made regarding functionality.
     *
     * For example, if we find that we should be viewing the UI, this function
     * will decide whether the UI should be visible based on the payload it has
     * found (e.g. env vars, options, etc).
     */ async handleAction({ actions, timer, getInngestHeaders, reqArgs, signatureValidation, body, method, headers }) {
        var _a;
        // This is when the request body is completely missing; it does not
        // include an empty body. This commonly happens when the HTTP framework
        // doesn't have body parsing middleware.
        const isMissingBody = body === undefined;
        try {
            let url = await actions.url("starting to handle request");
            if (method === "POST") {
                if (isMissingBody) {
                    this.log("error", "Missing body when executing, possibly due to missing request body middleware");
                    return {
                        status: 500,
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: (0, strings_js_1.stringify)((0, errors_js_1.serializeError)(new Error("Missing request body when executing, possibly due to missing request body middleware"))),
                        version: undefined
                    };
                }
                const validationResult = await signatureValidation;
                if (!validationResult.success) {
                    return {
                        status: 401,
                        headers: {
                            "Content-Type": "application/json"
                        },
                        body: (0, strings_js_1.stringify)((0, errors_js_1.serializeError)(validationResult.err)),
                        version: undefined
                    };
                }
                const rawProbe = await actions.queryStringWithDefaults("testing for probe", consts_js_1.queryKeys.Probe);
                if (rawProbe) {
                    const probe = (0, enum_js_1.enumFromValue)(consts_js_1.probe, rawProbe);
                    if (!probe) {
                        // If we're here, we've received a probe that we don't recognize.
                        // Fail.
                        return {
                            status: 400,
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: (0, strings_js_1.stringify)((0, errors_js_1.serializeError)(new Error(`Unknown probe "${rawProbe}"`))),
                            version: undefined
                        };
                    }
                    // Provide actions for every probe available.
                    const probeActions = {
                        [consts_js_1.probe.Trust]: ()=>({
                                status: 200,
                                headers: {
                                    "Content-Type": "application/json"
                                },
                                body: "",
                                version: undefined
                            })
                    };
                    return probeActions[probe]();
                }
                const fnId = await actions.queryStringWithDefaults("processing run request", consts_js_1.queryKeys.FnId);
                if (!fnId) {
                    // TODO PrettyError
                    throw new Error("No function ID found in request");
                }
                const stepId = await actions.queryStringWithDefaults("processing run request", consts_js_1.queryKeys.StepId) || null;
                const { version, result } = this.runStep({
                    functionId: fnId,
                    data: body,
                    stepId,
                    timer,
                    reqArgs,
                    headers: await headers
                });
                const stepOutput = await result;
                /**
                 * Functions can return `undefined`, but we'll always convert this to
                 * `null`, as this is appropriately serializable by JSON.
                 */ const opDataUndefinedToNull = (op)=>{
                    op.data = (0, functions_js_1.undefinedToNull)(op.data);
                    return op;
                };
                const resultHandlers = {
                    "function-rejected": (result)=>{
                        return {
                            status: result.retriable ? 500 : 400,
                            headers: Object.assign({
                                "Content-Type": "application/json",
                                [consts_js_1.headerKeys.NoRetry]: result.retriable ? "false" : "true"
                            }, typeof result.retriable === "string" ? {
                                [consts_js_1.headerKeys.RetryAfter]: result.retriable
                            } : {}),
                            body: (0, strings_js_1.stringify)((0, functions_js_1.undefinedToNull)(result.error)),
                            version
                        };
                    },
                    "function-resolved": (result)=>{
                        return {
                            status: 200,
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: (0, strings_js_1.stringify)((0, functions_js_1.undefinedToNull)(result.data)),
                            version
                        };
                    },
                    "step-not-found": (result)=>{
                        return {
                            status: 500,
                            headers: {
                                "Content-Type": "application/json",
                                [consts_js_1.headerKeys.NoRetry]: "false"
                            },
                            body: (0, strings_js_1.stringify)({
                                error: `Could not find step "${result.step.displayName || result.step.id}" to run; timed out`
                            }),
                            version
                        };
                    },
                    "step-ran": (result)=>{
                        const step = opDataUndefinedToNull(result.step);
                        return {
                            status: 206,
                            headers: Object.assign({
                                "Content-Type": "application/json"
                            }, typeof result.retriable !== "undefined" ? Object.assign({
                                [consts_js_1.headerKeys.NoRetry]: result.retriable ? "false" : "true"
                            }, typeof result.retriable === "string" ? {
                                [consts_js_1.headerKeys.RetryAfter]: result.retriable
                            } : {}) : {}),
                            body: (0, strings_js_1.stringify)([
                                step
                            ]),
                            version
                        };
                    },
                    "steps-found": (result)=>{
                        const steps = result.steps.map(opDataUndefinedToNull);
                        return {
                            status: 206,
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: (0, strings_js_1.stringify)(steps),
                            version
                        };
                    }
                };
                const handler = resultHandlers[stepOutput.type];
                try {
                    return await handler(stepOutput);
                } catch (err) {
                    this.log("error", "Error handling execution result", err);
                    throw err;
                }
            }
            // TODO: This feels hacky, so we should probably make it not hacky.
            const env = (_a = getInngestHeaders()[consts_js_1.headerKeys.Environment]) !== null && _a !== void 0 ? _a : null;
            if (method === "GET") {
                return {
                    status: 200,
                    body: (0, strings_js_1.stringify)(await this.introspectionBody({
                        actions,
                        env,
                        signatureValidation,
                        url
                    })),
                    headers: {
                        "Content-Type": "application/json"
                    },
                    version: undefined
                };
            }
            if (method === "PUT") {
                const [deployId, inBandSyncRequested] = await Promise.all([
                    actions.queryStringWithDefaults("processing deployment request", consts_js_1.queryKeys.DeployId).then((deployId)=>{
                        return deployId === "undefined" ? undefined : deployId;
                    }),
                    Promise.resolve((0, env_js_1.parseAsBoolean)(this.env[consts_js_1.envKeys.InngestAllowInBandSync])).then((allowInBandSync)=>{
                        if (allowInBandSync !== undefined && !allowInBandSync) {
                            return consts_js_1.syncKind.OutOfBand;
                        }
                        return actions.headers("processing deployment request", consts_js_1.headerKeys.InngestSyncKind);
                    }).then((kind)=>{
                        return kind === consts_js_1.syncKind.InBand;
                    })
                ]);
                if (inBandSyncRequested) {
                    if (isMissingBody) {
                        this.log("error", "Missing body when syncing, possibly due to missing request body middleware");
                        return {
                            status: 500,
                            headers: {
                                "Content-Type": "application/json"
                            },
                            body: (0, strings_js_1.stringify)((0, errors_js_1.serializeError)(new Error("Missing request body when syncing, possibly due to missing request body middleware"))),
                            version: undefined
                        };
                    }
                    // Validation can be successful if we're in dev mode and did not
                    // actually validate a key. In this case, also check that we did indeed
                    // use a particular key to validate.
                    const sigCheck = await signatureValidation;
                    if (!sigCheck.success) {
                        return {
                            status: 401,
                            body: (0, strings_js_1.stringify)({
                                code: "sig_verification_failed"
                            }),
                            headers: {
                                "Content-Type": "application/json"
                            },
                            version: undefined
                        };
                    }
                    const res = types_js_1.inBandSyncRequestBodySchema.safeParse(body);
                    if (!res.success) {
                        return {
                            status: 400,
                            body: (0, strings_js_1.stringify)({
                                code: "invalid_request",
                                message: res.error.message
                            }),
                            headers: {
                                "Content-Type": "application/json"
                            },
                            version: undefined
                        };
                    }
                    // We can trust the URL here because it's coming from
                    // signature-verified request.
                    url = this.reqUrl(new URL(res.data.url));
                    // This should be an in-band sync
                    const respBody = await this.inBandRegisterBody({
                        actions,
                        deployId,
                        env,
                        signatureValidation,
                        url
                    });
                    return {
                        status: 200,
                        body: (0, strings_js_1.stringify)(respBody),
                        headers: {
                            "Content-Type": "application/json",
                            [consts_js_1.headerKeys.InngestSyncKind]: consts_js_1.syncKind.InBand
                        },
                        version: undefined
                    };
                }
                // If we're here, this is a legacy out-of-band sync
                const { status, message, modified } = await this.register(this.reqUrl(url), deployId, getInngestHeaders);
                return {
                    status,
                    body: (0, strings_js_1.stringify)({
                        message,
                        modified
                    }),
                    headers: {
                        "Content-Type": "application/json",
                        [consts_js_1.headerKeys.InngestSyncKind]: consts_js_1.syncKind.OutOfBand
                    },
                    version: undefined
                };
            }
        } catch (err) {
            return {
                status: 500,
                body: (0, strings_js_1.stringify)(Object.assign({
                    type: "internal"
                }, (0, errors_js_1.serializeError)(err))),
                headers: {
                    "Content-Type": "application/json"
                },
                version: undefined
            };
        }
        return {
            status: 405,
            body: JSON.stringify({
                message: "No action found; request was likely not POST, PUT, or GET",
                mode: this._mode
            }),
            headers: {},
            version: undefined
        };
    }
    runStep({ functionId, stepId, data, timer, reqArgs, headers }) {
        var _a, _b;
        const fn = this.fns[functionId];
        if (!fn) {
            // TODO PrettyError
            throw new Error(`Could not find function with ID "${functionId}"`);
        }
        const immediateFnData = (0, functions_js_1.parseFnData)(data);
        let { version } = immediateFnData;
        // Handle opting in to optimized parallelism in v3.
        if (version === InngestExecution_js_1.ExecutionVersion.V1 && ((_b = (_a = fn.fn)["shouldOptimizeParallelism"]) === null || _b === void 0 ? void 0 : _b.call(_a))) {
            version = InngestExecution_js_1.ExecutionVersion.V2;
        }
        const result = (0, promises_js_1.runAsPromise)(async ()=>{
            const anyFnData = await (0, functions_js_1.fetchAllFnData)({
                data: immediateFnData,
                api: this.client["inngestApi"],
                version
            });
            if (!anyFnData.ok) {
                throw new Error(anyFnData.error);
            }
            const executionStarters = ((s)=>s)({
                [InngestExecution_js_1.ExecutionVersion.V0]: ({ event, events, steps, ctx, version })=>{
                    var _a, _b, _c;
                    const stepState = Object.entries(steps !== null && steps !== void 0 ? steps : {}).reduce((acc, [id, data])=>{
                        return Object.assign(Object.assign({}, acc), {
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                            [id]: {
                                id,
                                data
                            }
                        });
                    }, {});
                    return {
                        version,
                        partialOptions: {
                            client: this.client,
                            runId: (ctx === null || ctx === void 0 ? void 0 : ctx.run_id) || "",
                            data: {
                                event: event,
                                events: events,
                                runId: (ctx === null || ctx === void 0 ? void 0 : ctx.run_id) || "",
                                attempt: (_a = ctx === null || ctx === void 0 ? void 0 : ctx.attempt) !== null && _a !== void 0 ? _a : 0
                            },
                            stepState,
                            requestedRunStep: stepId === "step" ? undefined : stepId || undefined,
                            timer,
                            isFailureHandler: fn.onFailure,
                            stepCompletionOrder: (_c = (_b = ctx === null || ctx === void 0 ? void 0 : ctx.stack) === null || _b === void 0 ? void 0 : _b.stack) !== null && _c !== void 0 ? _c : [],
                            reqArgs,
                            headers
                        }
                    };
                },
                [InngestExecution_js_1.ExecutionVersion.V1]: ({ event, events, steps, ctx, version })=>{
                    var _a, _b, _c;
                    const stepState = Object.entries(steps !== null && steps !== void 0 ? steps : {}).reduce((acc, [id, result])=>{
                        return Object.assign(Object.assign({}, acc), {
                            [id]: result.type === "data" ? {
                                id,
                                data: result.data
                            } : result.type === "input" ? {
                                id,
                                input: result.input
                            } : {
                                id,
                                error: result.error
                            }
                        });
                    }, {});
                    return {
                        version,
                        partialOptions: {
                            client: this.client,
                            runId: (ctx === null || ctx === void 0 ? void 0 : ctx.run_id) || "",
                            data: {
                                event: event,
                                events: events,
                                runId: (ctx === null || ctx === void 0 ? void 0 : ctx.run_id) || "",
                                attempt: (_a = ctx === null || ctx === void 0 ? void 0 : ctx.attempt) !== null && _a !== void 0 ? _a : 0
                            },
                            stepState,
                            requestedRunStep: stepId === "step" ? undefined : stepId || undefined,
                            timer,
                            isFailureHandler: fn.onFailure,
                            disableImmediateExecution: ctx === null || ctx === void 0 ? void 0 : ctx.disable_immediate_execution,
                            stepCompletionOrder: (_c = (_b = ctx === null || ctx === void 0 ? void 0 : ctx.stack) === null || _b === void 0 ? void 0 : _b.stack) !== null && _c !== void 0 ? _c : [],
                            reqArgs,
                            headers
                        }
                    };
                },
                [InngestExecution_js_1.ExecutionVersion.V2]: ({ event, events, steps, ctx, version })=>{
                    var _a, _b, _c;
                    const stepState = Object.entries(steps !== null && steps !== void 0 ? steps : {}).reduce((acc, [id, result])=>{
                        return Object.assign(Object.assign({}, acc), {
                            [id]: result.type === "data" ? {
                                id,
                                data: result.data
                            } : result.type === "input" ? {
                                id,
                                input: result.input
                            } : {
                                id,
                                error: result.error
                            }
                        });
                    }, {});
                    return {
                        version,
                        partialOptions: {
                            client: this.client,
                            runId: (ctx === null || ctx === void 0 ? void 0 : ctx.run_id) || "",
                            data: {
                                event: event,
                                events: events,
                                runId: (ctx === null || ctx === void 0 ? void 0 : ctx.run_id) || "",
                                attempt: (_a = ctx === null || ctx === void 0 ? void 0 : ctx.attempt) !== null && _a !== void 0 ? _a : 0
                            },
                            stepState,
                            requestedRunStep: stepId === "step" ? undefined : stepId || undefined,
                            timer,
                            isFailureHandler: fn.onFailure,
                            disableImmediateExecution: ctx === null || ctx === void 0 ? void 0 : ctx.disable_immediate_execution,
                            stepCompletionOrder: (_c = (_b = ctx === null || ctx === void 0 ? void 0 : ctx.stack) === null || _b === void 0 ? void 0 : _b.stack) !== null && _c !== void 0 ? _c : [],
                            reqArgs,
                            headers
                        }
                    };
                }
            });
            const executionOptions = await executionStarters[version](anyFnData.value);
            return fn.fn["createExecution"](executionOptions).start();
        });
        return {
            version,
            result
        };
    }
    configs(url) {
        const configs = Object.values(this.rawFns).reduce((acc, fn)=>[
                ...acc,
                ...fn["getConfig"]({
                    baseUrl: url,
                    appPrefix: this.id
                })
            ], []);
        for (const config of configs){
            const check = types_js_1.functionConfigSchema.safeParse(config);
            if (!check.success) {
                const errors = check.error.errors.map((err)=>err.message).join("; ");
                this.log("warn", `Config invalid for function "${config.id}" : ${errors}`);
            }
        }
        return configs;
    }
    /**
     * Return an Inngest serve endpoint URL given a potential `path` and `host`.
     *
     * Will automatically use the `serveHost` and `servePath` if they have been
     * set when registering.
     */ reqUrl(url) {
        let ret = new URL(url);
        const serveHost = this.serveHost || this.env[consts_js_1.envKeys.InngestServeHost];
        const servePath = this.servePath || this.env[consts_js_1.envKeys.InngestServePath];
        if (servePath) {
            ret.pathname = servePath;
        }
        if (serveHost) {
            ret = new URL(ret.pathname + ret.search, serveHost);
        }
        return ret;
    }
    registerBody({ url, deployId }) {
        const body = {
            url: url.href,
            deployType: "ping",
            framework: this.frameworkName,
            appName: this.id,
            functions: this.configs(url),
            sdk: `js:v${version_js_1.version}`,
            v: "0.1",
            deployId: deployId || undefined,
            capabilities: {
                trust_probe: "v1",
                connect: "v1"
            },
            appVersion: this.client.appVersion
        };
        return body;
    }
    async inBandRegisterBody({ actions, deployId, env, signatureValidation, url }) {
        const registerBody = this.registerBody({
            deployId,
            url
        });
        const introspectionBody = await this.introspectionBody({
            actions,
            env,
            signatureValidation,
            url
        });
        const body = {
            app_id: this.id,
            appVersion: this.client.appVersion,
            capabilities: registerBody.capabilities,
            env,
            framework: registerBody.framework,
            functions: registerBody.functions,
            inspection: introspectionBody,
            platform: (0, env_js_1.getPlatformName)(Object.assign(Object.assign({}, (0, env_js_1.allProcessEnv)()), this.env)),
            sdk_author: "inngest",
            sdk_language: "",
            sdk_version: "",
            sdk: registerBody.sdk,
            url: registerBody.url
        };
        if (introspectionBody.authentication_succeeded) {
            body.sdk_language = introspectionBody.sdk_language;
            body.sdk_version = introspectionBody.sdk_version;
        }
        return body;
    }
    async introspectionBody({ actions, env, signatureValidation, url }) {
        var _a, _b, _c, _d, _e;
        const registerBody = this.registerBody({
            url: this.reqUrl(url),
            deployId: null
        });
        if (!this._mode) {
            throw new Error("No mode set; cannot introspect without mode");
        }
        let introspection = {
            authentication_succeeded: null,
            extra: {
                is_mode_explicit: this._mode.isExplicit
            },
            has_event_key: this.client["eventKeySet"](),
            has_signing_key: Boolean(this.signingKey),
            function_count: registerBody.functions.length,
            mode: this._mode.type,
            schema_version: "2024-05-24"
        };
        // Only allow authenticated introspection in Cloud mode, since Dev mode skips
        // signature validation
        if (this._mode.type === "cloud") {
            try {
                const validationResult = await signatureValidation;
                if (!validationResult.success) {
                    throw new Error("Signature validation failed");
                }
                introspection = Object.assign(Object.assign({}, introspection), {
                    authentication_succeeded: true,
                    api_origin: this.apiBaseUrl,
                    app_id: this.id,
                    capabilities: {
                        trust_probe: "v1",
                        connect: "v1"
                    },
                    env,
                    event_api_origin: this.eventApiBaseUrl,
                    event_key_hash: (_a = this.hashedEventKey) !== null && _a !== void 0 ? _a : null,
                    extra: Object.assign(Object.assign({}, introspection.extra), {
                        is_streaming: await this.shouldStream(actions)
                    }),
                    framework: this.frameworkName,
                    sdk_language: "js",
                    sdk_version: version_js_1.version,
                    serve_origin: (_b = this.serveHost) !== null && _b !== void 0 ? _b : null,
                    serve_path: (_c = this.servePath) !== null && _c !== void 0 ? _c : null,
                    signing_key_fallback_hash: (_d = this.hashedSigningKeyFallback) !== null && _d !== void 0 ? _d : null,
                    signing_key_hash: (_e = this.hashedSigningKey) !== null && _e !== void 0 ? _e : null
                });
            } catch (_f) {
                // Swallow signature validation error since we'll just return the
                // unauthenticated introspection
                introspection = Object.assign(Object.assign({}, introspection), {
                    authentication_succeeded: false
                });
            }
        }
        return introspection;
    }
    async register(url, deployId, getHeaders) {
        var _a;
        const body = this.registerBody({
            url,
            deployId
        });
        let res;
        // Whenever we register, we check to see if the dev server is up.  This
        // is a noop and returns false in production. Clone the URL object to avoid
        // mutating the property between requests.
        let registerURL = new URL(this.inngestRegisterUrl.href);
        const inferredDevMode = this._mode && this._mode.isInferred && this._mode.isDev;
        if (inferredDevMode) {
            const host = (0, env_js_1.devServerHost)(this.env);
            const hasDevServer = await (0, devserver_js_1.devServerAvailable)(host, this.fetch);
            if (hasDevServer) {
                registerURL = (0, devserver_js_1.devServerUrl)(host, "/fn/register");
            }
        } else if ((_a = this._mode) === null || _a === void 0 ? void 0 : _a.explicitDevUrl) {
            registerURL = (0, devserver_js_1.devServerUrl)(this._mode.explicitDevUrl.href, "/fn/register");
        }
        if (deployId) {
            registerURL.searchParams.set(consts_js_1.queryKeys.DeployId, deployId);
        }
        try {
            res = await (0, net_js_1.fetchWithAuthFallback)({
                authToken: this.hashedSigningKey,
                authTokenFallback: this.hashedSigningKeyFallback,
                fetch: this.fetch,
                url: registerURL.href,
                options: {
                    method: "POST",
                    body: (0, strings_js_1.stringify)(body),
                    headers: Object.assign(Object.assign({}, getHeaders()), {
                        [consts_js_1.headerKeys.InngestSyncKind]: consts_js_1.syncKind.OutOfBand
                    }),
                    redirect: "follow"
                }
            });
        } catch (err) {
            this.log("error", err);
            return {
                status: 500,
                message: `Failed to register${err instanceof Error ? `; ${err.message}` : ""}`,
                modified: false
            };
        }
        const raw = await res.text();
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        let data = {};
        try {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            data = JSON.parse(raw);
        } catch (err) {
            this.log("warn", "Couldn't unpack register response:", err);
            let message = "Failed to register";
            if (err instanceof Error) {
                message += `; ${err.message}`;
            }
            message += `; status code: ${res.status}`;
            return {
                status: 500,
                message,
                modified: false
            };
        }
        let status;
        let error;
        let skipped;
        let modified;
        try {
            ({ status, error, skipped, modified } = registerResSchema.parse(data));
        } catch (err) {
            this.log("warn", "Invalid register response schema:", err);
            let message = "Failed to register";
            if (err instanceof Error) {
                message += `; ${err.message}`;
            }
            message += `; status code: ${res.status}`;
            return {
                status: 500,
                message,
                modified: false
            };
        }
        // The dev server polls this endpoint to register functions every few
        // seconds, but we only want to log that we've registered functions if
        // the function definitions change.  Therefore, we compare the body sent
        // during registration with the body of the current functions and refuse
        // to register if the functions are the same.
        if (!skipped) {
            this.log("debug", "registered inngest functions:", res.status, res.statusText, data);
        }
        return {
            status,
            message: error,
            modified
        };
    }
    /**
     * Given an environment, upsert any missing keys. This is useful in
     * situations where environment variables are passed directly to handlers or
     * are otherwise difficult to access during initialization.
     */ upsertKeysFromEnv() {
        if (this.env[consts_js_1.envKeys.InngestSigningKey]) {
            if (!this.signingKey) {
                this.signingKey = String(this.env[consts_js_1.envKeys.InngestSigningKey]);
            }
            this.client["inngestApi"].setSigningKey(this.signingKey);
        }
        if (this.env[consts_js_1.envKeys.InngestSigningKeyFallback]) {
            if (!this.signingKeyFallback) {
                this.signingKeyFallback = String(this.env[consts_js_1.envKeys.InngestSigningKeyFallback]);
            }
            this.client["inngestApi"].setSigningKeyFallback(this.signingKeyFallback);
        }
        if (!this.client["eventKeySet"]() && this.env[consts_js_1.envKeys.InngestEventKey]) {
            this.client.setEventKey(String(this.env[consts_js_1.envKeys.InngestEventKey]));
        }
        // v2 -> v3 migration warnings
        if (this.env[consts_js_1.envKeys.InngestDevServerUrl]) {
            this.log("warn", `Use of ${consts_js_1.envKeys.InngestDevServerUrl} has been deprecated in v3; please use ${consts_js_1.envKeys.InngestBaseUrl} instead. See https://www.inngest.com/docs/sdk/migration`);
        }
    }
    /**
     * Validate the signature of a request and return the signing key used to
     * validate it.
     */ // eslint-disable-next-line @typescript-eslint/require-await
    async validateSignature(sig, body) {
        try {
            // Skip signature validation if requested (used by connect)
            if (this.skipSignatureValidation) {
                return {
                    success: true,
                    keyUsed: ""
                };
            }
            // Never validate signatures outside of prod. Make sure to check the mode
            // exists here instead of using nullish coalescing to confirm that the check
            // has been completed.
            if (this._mode && !this._mode.isCloud) {
                return {
                    success: true,
                    keyUsed: ""
                };
            }
            // If we're here, we're in production; lack of a signing key is an error.
            if (!this.signingKey) {
                // TODO PrettyError
                throw new Error(`No signing key found in client options or ${consts_js_1.envKeys.InngestSigningKey} env var. Find your keys at https://app.inngest.com/secrets`);
            }
            // If we're here, we're in production; lack of a req signature is an error.
            if (!sig) {
                // TODO PrettyError
                throw new Error(`No ${consts_js_1.headerKeys.Signature} provided`);
            }
            // Validate the signature
            return {
                success: true,
                keyUsed: new RequestSignature(sig).verifySignature({
                    body,
                    allowExpiredSignatures: this.allowExpiredSignatures,
                    signingKey: this.signingKey,
                    signingKeyFallback: this.signingKeyFallback
                })
            };
        } catch (err) {
            return {
                success: false,
                err: err
            };
        }
    }
    getResponseSignature(key, body) {
        const now = Date.now();
        const mac = (0, net_js_1.signDataWithKey)(body, key, now.toString());
        return `t=${now}&s=${mac}`;
    }
    /**
     * Log to stdout/stderr if the log level is set to include the given level.
     * The default log level is `"info"`.
     *
     * This is an abstraction over `console.log` and will try to use the correct
     * method for the given log level.  For example, `log("error", "foo")` will
     * call `console.error("foo")`.
     */ log(level, ...args) {
        const logLevels = [
            "debug",
            "info",
            "warn",
            "error",
            "fatal",
            "silent"
        ];
        const logLevelSetting = logLevels.indexOf(this.logLevel);
        const currentLevel = logLevels.indexOf(level);
        if (currentLevel >= logLevelSetting) {
            let logger = console.log;
            if (Object.prototype.hasOwnProperty.call(console, level)) {
                logger = console[level];
            }
            logger(`${consts_js_1.logPrefix} ${level} -`, ...args);
        }
    }
}
exports.InngestCommHandler = InngestCommHandler;
class RequestSignature {
    constructor(sig){
        _RequestSignature_instances.add(this);
        const params = new URLSearchParams(sig);
        this.timestamp = params.get("t") || "";
        this.signature = params.get("s") || "";
        if (!this.timestamp || !this.signature) {
            // TODO PrettyError
            throw new Error(`Invalid ${consts_js_1.headerKeys.Signature} provided`);
        }
    }
    hasExpired(allowExpiredSignatures) {
        if (allowExpiredSignatures) {
            return false;
        }
        const delta = Date.now() - new Date(parseInt(this.timestamp) * 1000).valueOf();
        return delta > 1000 * 60 * 5;
    }
    verifySignature({ body, signingKey, signingKeyFallback, allowExpiredSignatures }) {
        try {
            __classPrivateFieldGet(this, _RequestSignature_instances, "m", _RequestSignature_verifySignature).call(this, {
                body,
                signingKey,
                allowExpiredSignatures
            });
            return signingKey;
        } catch (err) {
            if (!signingKeyFallback) {
                throw err;
            }
            __classPrivateFieldGet(this, _RequestSignature_instances, "m", _RequestSignature_verifySignature).call(this, {
                body,
                signingKey: signingKeyFallback,
                allowExpiredSignatures
            });
            return signingKeyFallback;
        }
    }
}
_RequestSignature_instances = new WeakSet(), _RequestSignature_verifySignature = function _RequestSignature_verifySignature({ body, signingKey, allowExpiredSignatures }) {
    if (this.hasExpired(allowExpiredSignatures)) {
        // TODO PrettyError
        throw new Error("Signature has expired");
    }
    const mac = (0, net_js_1.signDataWithKey)(body, signingKey, this.timestamp);
    if (mac !== this.signature) {
        // TODO PrettyError
        throw new Error("Invalid signature");
    }
}; //# sourceMappingURL=InngestCommHandler.js.map
}}),
"[project]/node_modules/inngest/next.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/**
 * An adapter for Next.js to serve and register any declared functions with
 * Inngest, making them available to be triggered by events.
 *
 * Supports Next.js 12+, both serverless and edge.
 *
 * @example Next.js <=12 or the pages router can export the handler directly
 * ```ts
 * export default serve({ client: inngest, functions: [fn1, fn2] });
 * ```
 *
 * @example Next.js >=13 with the `app` dir must export individual methods
 * ```ts
 * export const { GET, POST, PUT } = serve({
 *            client: inngest,
 *            functions: [fn1, fn2],
 * });
 * ```
 *
 * @module
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.serve = exports.frameworkName = void 0;
const InngestCommHandler_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestCommHandler.js [app-route] (ecmascript)");
const env_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/env.js [app-route] (ecmascript)");
/**
 * The name of the framework, used to identify the framework in Inngest
 * dashboards and during testing.
 */ exports.frameworkName = "nextjs";
const isRecord = (val)=>{
    return typeof val === "object" && val !== null;
};
const isFunction = (val)=>{
    return typeof val === "function";
};
const isNext12ApiResponse = (val)=>{
    return isRecord(val) && isFunction(val.setHeader) && isFunction(val.status) && isFunction(val.send);
};
/**
 * In Next.js, serve and register any declared functions with Inngest, making
 * them available to be triggered by events.
 *
 * Supports Next.js 12+, both serverless and edge.
 *
 * @example Next.js <=12 or the pages router can export the handler directly
 * ```ts
 * export default serve({ client: inngest, functions: [fn1, fn2] });
 * ```
 *
 * @example Next.js >=13 with the `app` dir must export individual methods
 * ```ts
 * export const { GET, POST, PUT } = serve({
 *            client: inngest,
 *            functions: [fn1, fn2],
 * });
 * ```
 *
 * @public
 */ // Has explicit return type to avoid JSR-defined "slow types"
const serve = (options)=>{
    const handler = new InngestCommHandler_js_1.InngestCommHandler(Object.assign(Object.assign({
        frameworkName: exports.frameworkName
    }, options), {
        handler: (reqMethod, ...args)=>{
            const [expectedReq, res] = args;
            const req = expectedReq;
            const getHeader = (key)=>{
                const header = typeof req.headers.get === "function" ? req.headers.get(key) : req.headers[key];
                return Array.isArray(header) ? header[0] : header;
            };
            return {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                body: ()=>typeof req.json === "function" ? req.json() : req.body,
                headers: getHeader,
                method: ()=>{
                    /**
                     * `req.method`, though types say otherwise, is not available in Next.js
                     * 13 {@link https://beta.nextjs.org/docs/routing/route-handlers Route Handlers}.
                     *
                     * Therefore, we must try to set the method ourselves where we know it.
                     */ const method = reqMethod || req.method || "";
                    return method;
                },
                isProduction: ()=>{
                    /**
                     * Vercel Edge Functions do not allow dynamic access to environment
                     * variables, so we'll manage production checks directly here.
                     *
                     * We try/catch to avoid situations where Next.js is being used in
                     * environments where `process.env` is not accessible or polyfilled.
                     */ try {
                        // eslint-disable-next-line @inngest/internal/process-warn
                        const isProd = ("TURBOPACK compile-time value", "development") === "production";
                        return isProd;
                    } catch (err) {
                    // no-op
                    }
                },
                queryString: (key, url)=>{
                    var _a;
                    const qs = ((_a = req.query) === null || _a === void 0 ? void 0 : _a[key]) || url.searchParams.get(key);
                    return Array.isArray(qs) ? qs[0] : qs;
                },
                url: ()=>{
                    let absoluteUrl;
                    try {
                        absoluteUrl = new URL(req.url);
                    } catch (_a) {
                    // no-op
                    }
                    if (absoluteUrl) {
                        /**
                         * `req.url` here should may be the full URL, including query string.
                         * There are some caveats, however, where Next.js will obfuscate
                         * the host. For example, in the case of `host.docker.internal`,
                         * Next.js will instead set the host here to `localhost`.
                         *
                         * To avoid this, we'll try to parse the URL from `req.url`, but
                         * also use the `host` header if it's available.
                         */ const host = options.serveHost || getHeader("host");
                        if (host) {
                            const hostWithProtocol = new URL(host.includes("://") ? host : `${absoluteUrl.protocol}//${host}`);
                            absoluteUrl.protocol = hostWithProtocol.protocol;
                            absoluteUrl.host = hostWithProtocol.host;
                            absoluteUrl.port = hostWithProtocol.port;
                            absoluteUrl.username = hostWithProtocol.username;
                            absoluteUrl.password = hostWithProtocol.password;
                        }
                        return absoluteUrl;
                    }
                    let scheme = "https";
                    const host = options.serveHost || getHeader("host") || "";
                    try {
                        // eslint-disable-next-line @inngest/internal/process-warn
                        if (("TURBOPACK compile-time value", "development") === "development") {
                            scheme = "http";
                        }
                    } catch (err) {
                    // no-op
                    }
                    const url = new URL(req.url, `${scheme}://${host}`);
                    return url;
                },
                transformResponse: ({ body, headers, status })=>{
                    /**
                     * Carefully attempt to set headers and data on the response object
                     * for Next.js 12 support.
                     *
                     * This also assumes that we're not using Next.js 15, where the `res`
                     * object is repopulated as a `RouteContext` object. We expect these
                     * methods to NOT be defined in Next.js 15.
                     *
                     * We could likely use `instanceof ServerResponse` to better check the
                     * type of this, though Next.js 12 had issues with this due to not
                     * instantiating the response correctly.
                     */ if (isNext12ApiResponse(res)) {
                        for (const [key, value] of Object.entries(headers)){
                            res.setHeader(key, value);
                        }
                        res.status(status);
                        res.send(body);
                        /**
                         * If we're here, we're in a serverless endpoint (not edge), so
                         * we've correctly sent the response and can return `undefined`.
                         *
                         * Next.js 13 edge requires that the return value is typed as
                         * `Response`, so we still enforce that as we cannot dynamically
                         * adjust typing based on the environment.
                         */ return undefined;
                    }
                    /**
                     * If we're here, we're in an edge environment and need to return a
                     * `Response` object.
                     *
                     * We also don't know if the current environment has a native
                     * `Response` object, so we'll grab that first.
                     */ const Res = (0, env_js_1.getResponse)();
                    return new Res(body, {
                        status,
                        headers
                    });
                },
                transformStreamingResponse: ({ body, headers, status })=>{
                    return new Response(body, {
                        status,
                        headers
                    });
                }
            };
        }
    }));
    /**
     * Next.js 13 uses
     * {@link https://beta.nextjs.org/docs/routing/route-handlers Route Handlers}
     * to declare API routes instead of a generic catch-all method that was
     * available using the `pages/api` directory.
     *
     * This means that users must now export a function for each method supported
     * by the endpoint. For us, this means requiring a user explicitly exports
     * `GET`, `POST`, and `PUT` functions.
     *
     * Because of this, we'll add circular references to those property names of
     * the returned handler, meaning we can write some succinct code to export
     * them. Thanks, @goodoldneon.
     *
     * @example
     * ```ts
     * export const { GET, POST, PUT } = serve(...);
     * ```
     *
     * See {@link https://beta.nextjs.org/docs/routing/route-handlers}
     */ const baseFn = handler.createHandler();
    const fn = baseFn.bind(null, undefined);
    /**
     * Ensure we have a non-variadic length to avoid issues with forced type
     * checking.
     */ Object.defineProperty(fn, "length", {
        value: 1
    });
    const handlerFn = Object.defineProperties(fn, {
        GET: {
            value: baseFn.bind(null, "GET")
        },
        POST: {
            value: baseFn.bind(null, "POST")
        },
        PUT: {
            value: baseFn.bind(null, "PUT")
        }
    });
    return handlerFn;
};
exports.serve = serve; //# sourceMappingURL=next.js.map
}}),
"[project]/node_modules/inngest/components/EventSchemas.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.EventSchemas = void 0;
/**
 * Provide an `EventSchemas` class to type events, providing type safety when
 * sending events and running functions via Inngest.
 *
 * You can provide generated Inngest types, custom types, types using Zod, or
 * a combination of the above. See {@link EventSchemas} for more information.
 *
 * @example
 *
 * ```ts
 * export const inngest = new Inngest({
 *   id: "my-app",
 *   schemas: new EventSchemas().fromZod({
 *     "app/user.created": {
 *       data: z.object({
 *         id: z.string(),
 *         name: z.string(),
 *       }),
 *     },
 *   }),
 * });
 * ```
 *
 * @public
 */ class EventSchemas {
    constructor(){
        this.runtimeSchemas = {};
    }
    addRuntimeSchemas(schemas) {
        this.runtimeSchemas = Object.assign(Object.assign({}, this.runtimeSchemas), schemas);
    }
    /**
     * Use generated Inngest types to type events.
     */ fromGenerated() {
        return this;
    }
    /**
     * Use a `Record<>` type to type events.
     *
     * @example
     *
     * ```ts
     * export const inngest = new Inngest({
     *   id: "my-app",
     *   schemas: new EventSchemas().fromRecord<{
     *     "app/user.created": {
     *       data: {
     *         id: string;
     *         name: string;
     *       };
     *     };
     *   }>(),
     * });
     * ```
     */ fromRecord(..._args) {
        return this;
    }
    /**
     * Use a union type to type events.
     *
     * @example
     *
     * ```ts
     * type AccountCreated = {
     *   name: "app/account.created";
     *   data: { org: string };
     *   user: { id: string };
     * };
     *
     * type AccountDeleted = {
     *   name: "app/account.deleted";
     *   data: { org: string };
     *   user: { id: string };
     * };
     *
     * type Events = AccountCreated | AccountDeleted;
     *
     * export const inngest = new Inngest({
     *   id: "my-app",
     *   schemas: new EventSchemas().fromUnion<Events>(),
     * });
     * ```
     */ fromUnion() {
        return this;
    }
    /**
     * Use Zod to type events.
     *
     * @example
     *
     * ```ts
     * export const inngest = new Inngest({
     *   id: "my-app",
     *   schemas: new EventSchemas().fromZod({
     *     "app/user.created": {
     *       data: z.object({
     *         id: z.string(),
     *         name: z.string(),
     *       }),
     *     },
     *   }),
     * });
     * ```
     */ fromZod(schemas) {
        let runtimeSchemas;
        if (Array.isArray(schemas)) {
            runtimeSchemas = schemas.reduce((acc, schema)=>{
                const _a = schema.shape, { name: { value: name } } = _a, rest = __rest(_a, [
                    "name"
                ]);
                return Object.assign(Object.assign({}, acc), {
                    [name]: rest
                });
            }, {});
        } else {
            runtimeSchemas = schemas;
        }
        this.addRuntimeSchemas(runtimeSchemas);
        return this;
    }
}
exports.EventSchemas = EventSchemas; //# sourceMappingURL=EventSchemas.js.map
}}),
"[project]/node_modules/inngest/components/execution/als.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function() {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function(o) {
            var ar = [];
            for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function(mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) {
            for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== "default") __createBinding(result, mod, k[i]);
        }
        __setModuleDefault(result, mod);
        return result;
    };
}();
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getAsyncLocalStorage = exports.getAsyncCtx = void 0;
/**
 * A local-only symbol used as a key in global state to store the async local
 * storage instance.
 */ const alsSymbol = Symbol.for("inngest:als");
/**
 * Retrieve the async context for the current execution.
 */ const getAsyncCtx = async ()=>{
    return (0, exports.getAsyncLocalStorage)().then((als)=>als.getStore());
};
exports.getAsyncCtx = getAsyncCtx;
/**
 * Get a singleton instance of `AsyncLocalStorage` used to store and retrieve
 * async context for the current execution.
 */ const getAsyncLocalStorage = async ()=>{
    var _a;
    var _b;
    (_a = (_b = globalThis)[alsSymbol]) !== null && _a !== void 0 ? _a : _b[alsSymbol] = new Promise(// eslint-disable-next-line @typescript-eslint/no-misused-promises, no-async-promise-executor
    async (resolve)=>{
        try {
            const { AsyncLocalStorage } = await Promise.resolve().then(()=>__importStar(__turbopack_context__.r("[externals]/node:async_hooks [external] (node:async_hooks, cjs)")));
            resolve(new AsyncLocalStorage());
        } catch (err) {
            console.warn("node:async_hooks is not supported in this runtime. Experimental async context is disabled.");
            resolve({
                getStore: ()=>undefined,
                run: (_, fn)=>fn()
            });
        }
    });
    return globalThis[alsSymbol];
};
exports.getAsyncLocalStorage = getAsyncLocalStorage; //# sourceMappingURL=als.js.map
}}),
"[project]/node_modules/inngest/helpers/temporal.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getISOString = exports.isTemporalZonedDateTime = exports.isTemporalInstant = exports.isTemporalDuration = void 0;
/**
 * Asserts that the given `input` is a `Temporal.Duration` object.
 */ const isTemporalDuration = (/**
 * The input to check.
 */ input)=>{
    try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        return input[Symbol.toStringTag] === "Temporal.Duration";
    } catch (_a) {
        return false;
    }
};
exports.isTemporalDuration = isTemporalDuration;
/**
 * Asserts that the given `input` is a `Temporal.TimeZone` object.
 */ const isTemporalInstant = (/**
 * The input to check.
 */ input)=>{
    try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        return input[Symbol.toStringTag] === "Temporal.Instant";
    } catch (_a) {
        return false;
    }
};
exports.isTemporalInstant = isTemporalInstant;
/**
 * Asserts that the given `input` is a `Temporal.ZonedDateTime` object.
 */ const isTemporalZonedDateTime = (/**
 * The input to check.
 */ input)=>{
    try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any, @typescript-eslint/no-unsafe-member-access
        return input[Symbol.toStringTag] === "Temporal.ZonedDateTime";
    } catch (_a) {
        return false;
    }
};
exports.isTemporalZonedDateTime = isTemporalZonedDateTime;
/**
 * Converts a given `Date`, `string`, `Temporal.Instant`, or
 * `Temporal.ZonedDateTime` to an ISO 8601 string.
 */ const getISOString = (time)=>{
    if (typeof time === "string") {
        return new Date(time).toISOString();
    }
    if (time instanceof Date) {
        return time.toISOString();
    }
    if ((0, exports.isTemporalZonedDateTime)(time)) {
        return time.toInstant().toString();
    }
    if ((0, exports.isTemporalInstant)(time)) {
        return time.toString();
    }
    throw new TypeError("Invalid date input");
};
exports.getISOString = getISOString; //# sourceMappingURL=temporal.js.map
}}),
"[project]/node_modules/inngest/components/InngestMiddleware.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.getHookStack = exports.InngestMiddleware = void 0;
const functions_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/functions.js [app-route] (ecmascript)");
/**
 * A middleware that can be registered with Inngest to hook into various
 * lifecycles of the SDK and affect input and output of Inngest functionality.
 *
 * See {@link https://innge.st/middleware}
 *
 * @example
 *
 * ```ts
 * export const inngest = new Inngest({
 *   middleware: [
 *     new InngestMiddleware({
 *       name: "My Middleware",
 *       init: () => {
 *         // ...
 *       }
 *     })
 *   ]
 * });
 * ```
 *
 * @public
 */ class InngestMiddleware {
    constructor({ name, init }){
        this.name = name;
        this.init = init;
    }
}
exports.InngestMiddleware = InngestMiddleware;
/**
 * Given some middleware and an entrypoint, runs the initializer for the given
 * `key` and returns functions that will pass arguments through a stack of each
 * given hook in a middleware's lifecycle.
 *
 * Lets the middleware initialize before starting.
 */ const getHookStack = async (/**
 * The stack of middleware that will be used to run hooks.
 */ middleware, /**
 * The hook type to initialize.
 */ key, /**
 * Arguments for the initial hook.
 */ arg, transforms)=>{
    // Wait for middleware to initialize
    const mwStack = await middleware;
    // Step through each middleware and get the hook for the given key
    const keyFns = mwStack.reduce((acc, mw)=>{
        const fn = mw[key];
        if (fn) {
            return [
                ...acc,
                fn
            ];
        }
        return acc;
    }, []);
    // Run each hook found in sequence and collect the results
    const hooksRegistered = await keyFns.reduce(async (acc, fn)=>{
        // eslint-disable-next-line @typescript-eslint/no-unsafe-return
        return [
            ...await acc,
            await fn(arg)
        ];
    }, Promise.resolve([]));
    // Prepare the return object - mutating this instead of using reduce as it
    // results in cleaner code.
    const ret = {};
    // Step through each hook result and create a waterfall joining each key
    for (const hook of hooksRegistered){
        const hookKeys = Object.keys(hook);
        for (const key of hookKeys){
            let fns = [
                hook[key]
            ];
            const existingWaterfall = ret[key];
            if (existingWaterfall) {
                fns = [
                    existingWaterfall,
                    hook[key]
                ];
            }
            const transform = transforms[key];
            ret[key] = (0, functions_js_1.waterfall)(fns, transform);
        }
    }
    // Cache each function in the stack to ensure each can only be called once
    for (const k of Object.keys(ret)){
        const key = k;
        ret[key] = (0, functions_js_1.cacheFn)(ret[key]);
    }
    return ret;
};
exports.getHookStack = getHookStack; //# sourceMappingURL=InngestMiddleware.js.map
}}),
"[project]/node_modules/inngest/components/RetryAfterError.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.RetryAfterError = void 0;
const ms_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/ms/index.js [app-route] (ecmascript)"));
/**
 * An error that, when thrown, indicates to Inngest that the function should be
 * retried after a given amount of time.
 *
 * A `message` must be provided, as well as a `retryAfter` parameter, which can
 * be a `number` of milliseconds, an `ms`-compatible time string, or a `Date`.
 *
 * An optional `cause` can be provided to provide more context to the error.
 *
 * @public
 */ class RetryAfterError extends Error {
    constructor(message, /**
     * The time after which the function should be retried. Represents either a
     * number of milliseconds or a RFC3339 date.
     */ retryAfter, options){
        super(message);
        if (retryAfter instanceof Date) {
            this.retryAfter = retryAfter.toISOString();
        } else {
            const seconds = `${Math.ceil((typeof retryAfter === "string" ? (0, ms_1.default)(retryAfter) : retryAfter) / 1000)}`;
            if (!isFinite(Number(seconds))) {
                throw new Error("retryAfter must be a number of milliseconds, a ms-compatible string, or a Date");
            }
            this.retryAfter = seconds;
        }
        // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
        this.cause = options === null || options === void 0 ? void 0 : options.cause;
    }
}
exports.RetryAfterError = RetryAfterError; //# sourceMappingURL=RetryAfterError.js.map
}}),
"[project]/node_modules/inngest/components/execution/v0.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __rest = this && this.__rest || function(s, e) {
    var t = {};
    for(var p in s)if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0) t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function") for(var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++){
        if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i])) t[p[i]] = s[p[i]];
    }
    return t;
};
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports._internals = exports.V0InngestExecution = exports.createV0InngestExecution = void 0;
const canonicalize_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/canonicalize/lib/canonicalize.js [app-route] (ecmascript)"));
const hash_js_1 = __turbopack_context__.r("[project]/node_modules/hash.js/lib/hash.js [app-route] (ecmascript)");
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
const functions_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/functions.js [app-route] (ecmascript)");
const promises_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/promises.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const InngestMiddleware_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestMiddleware.js [app-route] (ecmascript)");
const InngestStepTools_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestStepTools.js [app-route] (ecmascript)");
const NonRetriableError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/NonRetriableError.js [app-route] (ecmascript)");
const RetryAfterError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/RetryAfterError.js [app-route] (ecmascript)");
const InngestExecution_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)");
const createV0InngestExecution = (options)=>{
    return new V0InngestExecution(options);
};
exports.createV0InngestExecution = createV0InngestExecution;
class V0InngestExecution extends InngestExecution_js_1.InngestExecution {
    constructor(options){
        super(options);
        this.userFnToRun = this.getUserFnToRun();
        this.state = this.createExecutionState();
        this.fnArg = this.createFnArg();
    }
    start() {
        var _a;
        this.debug("starting V0 execution");
        return (_a = this.execution) !== null && _a !== void 0 ? _a : this.execution = this._start().then((result)=>{
            this.debug("result:", result);
            return result;
        });
    }
    async _start() {
        var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x;
        this.state.hooks = await this.initializeMiddleware();
        try {
            await this.transformInput();
            await ((_b = (_a = this.state.hooks).beforeMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
            if (this.state.opStack.length === 0 && !this.options.requestedRunStep) {
                await ((_d = (_c = this.state.hooks).afterMemoization) === null || _d === void 0 ? void 0 : _d.call(_c));
                await ((_f = (_e = this.state.hooks).beforeExecution) === null || _f === void 0 ? void 0 : _f.call(_e));
            }
            const userFnPromise = (0, promises_js_1.runAsPromise)(()=>this.userFnToRun(this.fnArg));
            let pos = -1;
            do {
                if (pos >= 0) {
                    if (!this.options.requestedRunStep && pos === this.state.opStack.length - 1) {
                        await ((_h = (_g = this.state.hooks).afterMemoization) === null || _h === void 0 ? void 0 : _h.call(_g));
                        await ((_k = (_j = this.state.hooks).beforeExecution) === null || _k === void 0 ? void 0 : _k.call(_j));
                    }
                    this.state.tickOps = {};
                    const incomingOp = this.state.opStack[pos];
                    this.state.currentOp = this.state.allFoundOps[incomingOp.id];
                    if (!this.state.currentOp) {
                        /**
                         * We're trying to resume the function, but we can't find where to go.
                         *
                         * This means that either the function has changed or there are async
                         * actions in-between steps that we haven't noticed in previous
                         * executions.
                         *
                         * Whichever the case, this is bad and we can't continue in this
                         * undefined state.
                         */ throw new NonRetriableError_js_1.NonRetriableError((0, errors_js_1.prettyError)({
                            whatHappened: " Your function was stopped from running",
                            why: "We couldn't resume your function's state because it may have changed since the run started or there are async actions in-between steps that we haven't noticed in previous executions.",
                            consequences: "Continuing to run the function may result in unexpected behaviour, so we've stopped your function to ensure nothing unexpected happened!",
                            toFixNow: "Ensure that your function is either entirely step-based or entirely non-step-based, by either wrapping all asynchronous logic in `step.run()` calls or by removing all `step.*()` calls.",
                            otherwise: "For more information on why step functions work in this manner, see https://www.inngest.com/docs/functions/multi-step#gotchas",
                            stack: true,
                            code: errors_js_1.ErrCode.NON_DETERMINISTIC_FUNCTION
                        }));
                    }
                    this.state.currentOp.fulfilled = true;
                    if (typeof incomingOp.data !== "undefined") {
                        this.state.currentOp.resolve(incomingOp.data);
                    } else {
                        this.state.currentOp.reject(incomingOp.error);
                    }
                }
                await (0, promises_js_1.resolveAfterPending)();
                this.state.reset();
                pos++;
            }while (pos < this.state.opStack.length)
            await ((_m = (_l = this.state.hooks).afterMemoization) === null || _m === void 0 ? void 0 : _m.call(_l));
            const discoveredOps = Object.values(this.state.tickOps).map(tickOpToOutgoing);
            const runStep = this.options.requestedRunStep || this.getEarlyExecRunStep(discoveredOps);
            if (runStep) {
                const userFnOp = this.state.allFoundOps[runStep];
                const stepToRun = userFnOp === null || userFnOp === void 0 ? void 0 : userFnOp.fn;
                if (!stepToRun) {
                    throw new Error(`Bad stack; executor requesting to run unknown step "${runStep}"`);
                }
                const outgoingUserFnOp = Object.assign(Object.assign({}, tickOpToOutgoing(userFnOp)), {
                    op: types_js_1.StepOpCode.Step
                });
                await ((_p = (_o = this.state.hooks).beforeExecution) === null || _p === void 0 ? void 0 : _p.call(_o));
                this.state.executingStep = true;
                const result = await (0, promises_js_1.runAsPromise)(stepToRun).finally(()=>{
                    this.state.executingStep = false;
                }).catch(async (error)=>{
                    return await this.transformOutput({
                        error
                    }, outgoingUserFnOp);
                }).then(async (data)=>{
                    var _a, _b;
                    await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterExecution) === null || _b === void 0 ? void 0 : _b.call(_a));
                    return await this.transformOutput({
                        data
                    }, outgoingUserFnOp);
                });
                const { type: _type } = result, rest = __rest(result, [
                    "type"
                ]);
                return {
                    type: "step-ran",
                    ctx: this.fnArg,
                    ops: this.ops,
                    step: Object.assign(Object.assign({}, outgoingUserFnOp), rest)
                };
            }
            if (!discoveredOps.length) {
                const fnRet = await Promise.race([
                    userFnPromise.then((data)=>({
                            type: "complete",
                            data
                        })),
                    (0, promises_js_1.resolveNextTick)().then(()=>({
                            type: "incomplete"
                        }))
                ]);
                if (fnRet.type === "complete") {
                    await ((_r = (_q = this.state.hooks).afterExecution) === null || _r === void 0 ? void 0 : _r.call(_q));
                    const allOpsFulfilled = Object.values(this.state.allFoundOps).every((op)=>{
                        return op.fulfilled;
                    });
                    if (allOpsFulfilled) {
                        return await this.transformOutput({
                            data: fnRet.data
                        });
                    }
                } else if (!this.state.hasUsedTools) {
                    this.state.nonStepFnDetected = true;
                    const data = await userFnPromise;
                    await ((_t = (_s = this.state.hooks).afterExecution) === null || _t === void 0 ? void 0 : _t.call(_s));
                    return await this.transformOutput({
                        data
                    });
                } else {
                    const hasOpsPending = Object.values(this.state.allFoundOps).some((op)=>{
                        return op.fulfilled === false;
                    });
                    if (!hasOpsPending) {
                        throw new NonRetriableError_js_1.NonRetriableError((0, errors_js_1.functionStoppedRunningErr)(errors_js_1.ErrCode.ASYNC_DETECTED_AFTER_MEMOIZATION));
                    }
                }
            }
            await ((_v = (_u = this.state.hooks).afterExecution) === null || _v === void 0 ? void 0 : _v.call(_u));
            return {
                type: "steps-found",
                ctx: this.fnArg,
                ops: this.ops,
                steps: discoveredOps
            };
        } catch (error) {
            return await this.transformOutput({
                error
            });
        } finally{
            await ((_x = (_w = this.state.hooks).beforeResponse) === null || _x === void 0 ? void 0 : _x.call(_w));
        }
    }
    async initializeMiddleware() {
        const ctx = this.options.data;
        const hooks = await (0, InngestMiddleware_js_1.getHookStack)(this.options.fn["middleware"], "onFunctionRun", {
            ctx,
            fn: this.options.fn,
            steps: Object.values(this.options.stepState),
            reqArgs: this.options.reqArgs
        }, {
            transformInput: (prev, output)=>{
                return {
                    ctx: Object.assign(Object.assign({}, prev.ctx), output === null || output === void 0 ? void 0 : output.ctx),
                    fn: this.options.fn,
                    steps: prev.steps.map((step, i)=>{
                        var _a;
                        return Object.assign(Object.assign({}, step), (_a = output === null || output === void 0 ? void 0 : output.steps) === null || _a === void 0 ? void 0 : _a[i]);
                    }),
                    reqArgs: prev.reqArgs
                };
            },
            transformOutput: (prev, output)=>{
                return {
                    result: Object.assign(Object.assign({}, prev.result), output === null || output === void 0 ? void 0 : output.result),
                    step: prev.step
                };
            }
        });
        return hooks;
    }
    createExecutionState() {
        const state = {
            allFoundOps: {},
            tickOps: {},
            tickOpHashes: {},
            currentOp: undefined,
            hasUsedTools: false,
            reset: ()=>{
                state.tickOpHashes = {};
                state.allFoundOps = Object.assign(Object.assign({}, state.allFoundOps), state.tickOps);
            },
            nonStepFnDetected: false,
            executingStep: false,
            opStack: this.options.stepCompletionOrder.reduce((acc, stepId)=>{
                const stepState = this.options.stepState[stepId];
                if (!stepState) {
                    return acc;
                }
                return [
                    ...acc,
                    stepState
                ];
            }, [])
        };
        return state;
    }
    get ops() {
        return Object.fromEntries(Object.entries(this.state.allFoundOps).map(([id, op])=>[
                id,
                {
                    id: op.id,
                    rawArgs: op.rawArgs,
                    data: op.data,
                    error: op.error,
                    fulfilled: op.fulfilled,
                    seen: true
                }
            ]));
    }
    getUserFnToRun() {
        if (!this.options.isFailureHandler) {
            return this.options.fn["fn"];
        }
        if (!this.options.fn["onFailureFn"]) {
            /**
             * Somehow, we've ended up detecting that this is a failure handler but
             * doesn't have an `onFailure` function. This should never happen.
             */ throw new Error("Cannot find function `onFailure` handler");
        }
        // TODO: Review; inferred types results in an `any` here!
        return this.options.fn["onFailureFn"];
    }
    createFnArg() {
        var _a, _b, _c, _d;
        // Start referencing everything
        this.state.tickOps = this.state.allFoundOps;
        /**
         * Create a unique hash of an operation using only a subset of the operation's
         * properties; will never use `data` and will guarantee the order of the
         * object so we don't rely on individual tools for that.
         *
         * If the operation already contains an ID, the current ID will be used
         * instead, so that users can provide their own IDs.
         */ const hashOp = (/**
         * The op to generate a hash from. We only use a subset of the op's
         * properties when creating the hash.
         */ op)=>{
            var _a, _b, _c, _d;
            /**
             * It's difficult for v0 to understand whether or not an op has
             * historically contained a custom ID, as all step usage now require them.
             *
             * For this reason, we make the assumption that steps in v0 do not have a
             * custom ID and generate one for them as we would in all recommendations
             * and examples.
             */ const obj = {
                parent: (_b = (_a = this.state.currentOp) === null || _a === void 0 ? void 0 : _a.id) !== null && _b !== void 0 ? _b : null,
                op: op.op,
                name: op.name,
                // Historically, no v0 runs could have options for `step.run()` call,
                // but this object can be specified in future versions.
                //
                // For this purpose, we change this to always use `null` if the op is
                // that of a `step.run()`.
                opts: op.op === types_js_1.StepOpCode.StepPlanned ? null : (_c = op.opts) !== null && _c !== void 0 ? _c : null
            };
            const collisionHash = exports._internals.hashData(obj);
            const pos = this.state.tickOpHashes[collisionHash] = ((_d = this.state.tickOpHashes[collisionHash]) !== null && _d !== void 0 ? _d : -1) + 1;
            return Object.assign(Object.assign({}, op), {
                id: exports._internals.hashData(Object.assign({
                    pos
                }, obj))
            });
        };
        const stepHandler = ({ args, matchOp, opts })=>{
            if (this.state.nonStepFnDetected) {
                throw new NonRetriableError_js_1.NonRetriableError((0, errors_js_1.functionStoppedRunningErr)(errors_js_1.ErrCode.STEP_USED_AFTER_ASYNC));
            }
            if (this.state.executingStep) {
                throw new NonRetriableError_js_1.NonRetriableError((0, errors_js_1.prettyError)({
                    whatHappened: "Your function was stopped from running",
                    why: "We detected that you have nested `step.*` tooling.",
                    consequences: "Nesting `step.*` tooling is not supported.",
                    stack: true,
                    toFixNow: "Make sure you're not using `step.*` tooling inside of other `step.*` tooling. If you need to compose steps together, you can create a new async function and call it from within your step function, or use promise chaining.",
                    otherwise: "For more information on step functions with Inngest, see https://www.inngest.com/docs/functions/multi-step",
                    code: errors_js_1.ErrCode.NESTING_STEPS
                }));
            }
            this.state.hasUsedTools = true;
            const stepOptions = (0, InngestStepTools_js_1.getStepOptions)(args[0]);
            const opId = hashOp(matchOp(stepOptions, ...args.slice(1)));
            return new Promise((resolve, reject)=>{
                this.state.tickOps[opId.id] = Object.assign(Object.assign(Object.assign({}, opId), (opts === null || opts === void 0 ? void 0 : opts.fn) ? {
                    fn: ()=>{
                        var _a;
                        return (_a = opts.fn) === null || _a === void 0 ? void 0 : _a.call(opts, ...args);
                    }
                } : {}), {
                    rawArgs: args,
                    resolve,
                    reject,
                    fulfilled: false
                });
            });
        };
        const step = (0, InngestStepTools_js_1.createStepTools)(this.options.client, this, stepHandler);
        let fnArg = Object.assign(Object.assign({}, this.options.data), {
            step
        });
        if (this.options.isFailureHandler) {
            const eventData = zod_1.z.object({
                error: types_js_1.jsonErrorSchema
            }).parse((_a = fnArg.event) === null || _a === void 0 ? void 0 : _a.data);
            fnArg = Object.assign(Object.assign({}, fnArg), {
                error: (0, errors_js_1.deserializeError)(eventData.error)
            });
        }
        return (_d = (_c = (_b = this.options).transformCtx) === null || _c === void 0 ? void 0 : _c.call(_b, fnArg)) !== null && _d !== void 0 ? _d : fnArg;
    }
    /**
     * Using middleware, transform input before running.
     */ async transformInput() {
        var _a, _b;
        const inputMutations = await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.transformInput) === null || _b === void 0 ? void 0 : _b.call(_a, {
            ctx: Object.assign({}, this.fnArg),
            steps: Object.values(this.options.stepState),
            fn: this.options.fn,
            reqArgs: this.options.reqArgs
        }));
        if (inputMutations === null || inputMutations === void 0 ? void 0 : inputMutations.ctx) {
            this.fnArg = inputMutations.ctx;
        }
        if (inputMutations === null || inputMutations === void 0 ? void 0 : inputMutations.steps) {
            this.state.opStack = [
                ...inputMutations.steps
            ];
        }
    }
    getEarlyExecRunStep(ops) {
        if (ops.length !== 1) return;
        const op = ops[0];
        if (op && op.op === types_js_1.StepOpCode.StepPlanned) {
            return op.id;
        }
    }
    /**
     * Using middleware, transform output before returning.
     */ async transformOutput(dataOrError, step) {
        var _a, _b, _c, _d;
        const output = Object.assign({}, dataOrError);
        if (typeof output.error !== "undefined") {
            output.data = (0, errors_js_1.serializeError)(output.error);
        }
        const transformedOutput = await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.transformOutput) === null || _b === void 0 ? void 0 : _b.call(_a, {
            result: Object.assign({}, output),
            step
        }));
        const { data, error } = Object.assign(Object.assign({}, output), transformedOutput === null || transformedOutput === void 0 ? void 0 : transformedOutput.result);
        if (!step) {
            await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.finished) === null || _d === void 0 ? void 0 : _d.call(_c, {
                result: Object.assign({}, typeof error !== "undefined" ? {
                    error
                } : {
                    data
                })
            }));
        }
        if (typeof error !== "undefined") {
            /**
             * Ensure we give middleware the chance to decide on retriable behaviour
             * by looking at the error returned from output transformation.
             */ let retriable = !(error instanceof NonRetriableError_js_1.NonRetriableError);
            if (retriable && error instanceof RetryAfterError_js_1.RetryAfterError) {
                retriable = error.retryAfter;
            }
            const serializedError = (0, errors_js_1.serializeError)(error);
            return {
                type: "function-rejected",
                ctx: this.fnArg,
                ops: this.ops,
                error: serializedError,
                retriable
            };
        }
        return {
            type: "function-resolved",
            ctx: this.fnArg,
            ops: this.ops,
            data: (0, functions_js_1.undefinedToNull)(data)
        };
    }
}
exports.V0InngestExecution = V0InngestExecution;
const tickOpToOutgoing = (op)=>{
    return {
        op: op.op,
        id: op.id,
        name: op.name,
        opts: op.opts
    };
};
const hashData = (op)=>{
    return (0, hash_js_1.sha1)().update((0, canonicalize_1.default)(op)).digest("hex");
};
/**
 * Exported for testing.
 */ exports._internals = {
    hashData
}; //# sourceMappingURL=v0.js.map
}}),
"[project]/node_modules/inngest/components/StepError.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.StepError = void 0;
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
/**
 * An error that represents a step exhausting all retries and failing. This is
 * thrown by an Inngest step if it fails.
 *
 * It's synonymous with an `Error`, with the addition of the `stepId` that
 * failed.
 *
 * @public
 */ class StepError extends Error {
    constructor(/**
     * The ID of the step that failed.
     */ stepId, err){
        var _a;
        const parsedErr = types_js_1.jsonErrorSchema.parse(err);
        super(parsedErr.message);
        this.stepId = stepId;
        this.name = parsedErr.name;
        this.stepId = stepId;
        // Don't show the internal stack trace if we don't have one.
        this.stack = (_a = parsedErr.stack) !== null && _a !== void 0 ? _a : undefined;
        // Try setting the cause if we have one
        this.cause = parsedErr.cause ? (0, errors_js_1.deserializeError)(parsedErr.cause, true) : undefined;
    }
}
exports.StepError = StepError; //# sourceMappingURL=StepError.js.map
}}),
"[project]/node_modules/inngest/components/execution/otel/access.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/**
 * A file used to access client processors safely without also importing any
 * otel-specific libraries. Useful for ensuring that the otel libraries can be
 * tree-shaken if they're not used directly by the user.
 */ Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.clientProcessorMap = void 0;
/**
 * A map of Inngest clients to their OTel span processors. This is used to
 * ensure that we only create one span processor per client, and that we can
 * access the span processor from the client without exposing the OTel
 * libraries to the user.
 */ exports.clientProcessorMap = new WeakMap(); //# sourceMappingURL=access.js.map
}}),
"[project]/node_modules/inngest/components/execution/v1.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __asyncValues = this && this.__asyncValues || function(o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
        return this;
    }, i);
    "TURBOPACK unreachable";
    function verb(n) {
        i[n] = o[n] && function(v) {
            return new Promise(function(resolve, reject) {
                v = o[n](v), settle(resolve, reject, v.done, v.value);
            });
        };
    }
    function settle(resolve, reject, d, v) {
        Promise.resolve(v).then(function(v) {
            resolve({
                value: v,
                done: d
            });
        }, reject);
    }
};
var __await = this && this.__await || function(v) {
    return this instanceof __await ? (this.v = v, this) : new __await(v);
};
var __asyncGenerator = this && this.__asyncGenerator || function(thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function awaitReturn(f) {
        return function(v) {
            return Promise.resolve(v).then(f, reject);
        };
    }
    function verb(n, f) {
        if (g[n]) {
            i[n] = function(v) {
                return new Promise(function(a, b) {
                    q.push([
                        n,
                        v,
                        a,
                        b
                    ]) > 1 || resume(n, v);
                });
            };
            if (f) i[n] = f(i[n]);
        }
    }
    function resume(n, v) {
        try {
            step(g[n](v));
        } catch (e) {
            settle(q[0][3], e);
        }
    }
    function step(r) {
        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
    }
    function fulfill(value) {
        resume("next", value);
    }
    function reject(value) {
        resume("throw", value);
    }
    function settle(f, v) {
        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
    }
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports._internals = exports.createV1InngestExecution = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const hash_js_1 = __turbopack_context__.r("[project]/node_modules/hash.js/lib/hash.js [app-route] (ecmascript)");
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
const functions_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/functions.js [app-route] (ecmascript)");
const promises_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/promises.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const version_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/version.js [app-route] (ecmascript)");
const InngestMiddleware_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestMiddleware.js [app-route] (ecmascript)");
const InngestStepTools_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestStepTools.js [app-route] (ecmascript)");
const NonRetriableError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/NonRetriableError.js [app-route] (ecmascript)");
const RetryAfterError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/RetryAfterError.js [app-route] (ecmascript)");
const StepError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/StepError.js [app-route] (ecmascript)");
const InngestExecution_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)");
const als_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/als.js [app-route] (ecmascript)");
const access_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/otel/access.js [app-route] (ecmascript)");
const createV1InngestExecution = (options)=>{
    return new V1InngestExecution(options);
};
exports.createV1InngestExecution = createV1InngestExecution;
class V1InngestExecution extends InngestExecution_js_1.InngestExecution {
    constructor(options){
        super(options);
        this.timeoutDuration = 1000 * 10;
        this.userFnToRun = this.getUserFnToRun();
        this.state = this.createExecutionState();
        this.fnArg = this.createFnArg();
        this.checkpointHandlers = this.createCheckpointHandlers();
        this.initializeTimer(this.state);
        this.debug("created new V1 execution for run;", this.options.requestedRunStep ? `wanting to run step "${this.options.requestedRunStep}"` : "discovering steps");
        this.debug("existing state keys:", Object.keys(this.state.stepState));
    }
    /**
     * Idempotently start the execution of the user's function.
     */ start() {
        if (!this.execution) {
            this.debug("starting V1 execution");
            const tracer = api_1.trace.getTracer("inngest", version_js_1.version);
            this.execution = (0, als_js_1.getAsyncLocalStorage)().then((als)=>{
                return als.run({
                    app: this.options.client,
                    ctx: this.fnArg
                }, async ()=>{
                    return tracer.startActiveSpan("inngest.execution", (span)=>{
                        var _a;
                        (_a = access_js_1.clientProcessorMap.get(this.options.client)) === null || _a === void 0 ? void 0 : _a.declareStartingSpan({
                            span,
                            runId: this.options.runId,
                            traceparent: this.options.headers[consts_js_1.headerKeys.TraceParent],
                            tracestate: this.options.headers[consts_js_1.headerKeys.TraceState]
                        });
                        return this._start().then((result)=>{
                            this.debug("result:", result);
                            return result;
                        }).finally(()=>{
                            span.end();
                        });
                    });
                });
            });
        }
        return this.execution;
    }
    /**
     * Starts execution of the user's function and the core loop.
     */ async _start() {
        var _a, e_1, _b, _c;
        var _d, _e;
        try {
            const allCheckpointHandler = this.getCheckpointHandler("");
            this.state.hooks = await this.initializeMiddleware();
            await this.startExecution();
            try {
                for(var _f = true, _g = __asyncValues(this.state.loop), _h; _h = await _g.next(), _a = _h.done, !_a; _f = true){
                    _c = _h.value;
                    _f = false;
                    const checkpoint = _c;
                    await allCheckpointHandler(checkpoint);
                    const handler = this.getCheckpointHandler(checkpoint.type);
                    const result = await handler(checkpoint);
                    if (result) {
                        return result;
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (!_f && !_a && (_b = _g.return)) await _b.call(_g);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
        } catch (error) {
            return await this.transformOutput({
                error
            });
        } finally{
            void this.state.loop.return();
            await ((_e = (_d = this.state.hooks) === null || _d === void 0 ? void 0 : _d.beforeResponse) === null || _e === void 0 ? void 0 : _e.call(_d));
        }
        /**
         * If we're here, the generator somehow finished without returning a value.
         * This should never happen.
         */ throw new Error("Core loop finished without returning a value");
    }
    /**
     * Creates a handler for every checkpoint type, defining what to do when we
     * reach that checkpoint in the core loop.
     */ createCheckpointHandlers() {
        return {
            /**
             * Run for all checkpoints. Best used for logging or common actions.
             * Use other handlers to return values and interrupt the core loop.
             */ "": (checkpoint)=>{
                this.debug("checkpoint:", checkpoint);
            },
            /**
             * The user's function has completed and returned a value.
             */ "function-resolved": async (checkpoint)=>{
                return await this.transformOutput({
                    data: checkpoint.data
                });
            },
            /**
             * The user's function has thrown an error.
             */ "function-rejected": async (checkpoint)=>{
                return await this.transformOutput({
                    error: checkpoint.error
                });
            },
            /**
             * We've found one or more steps. Here we may want to run a step or report
             * them back to Inngest.
             */ "steps-found": async ({ steps })=>{
                const stepResult = await this.tryExecuteStep(steps);
                if (stepResult) {
                    const transformResult = await this.transformOutput(stepResult);
                    /**
                     * Transforming output will always return either function rejection or
                     * resolution. In most cases, this can be immediately returned, but in
                     * this particular case we want to handle it differently.
                     */ if (transformResult.type === "function-resolved") {
                        return {
                            type: "step-ran",
                            ctx: transformResult.ctx,
                            ops: transformResult.ops,
                            step: exports._internals.hashOp(Object.assign(Object.assign({}, stepResult), {
                                data: transformResult.data
                            }))
                        };
                    } else if (transformResult.type === "function-rejected") {
                        return {
                            type: "step-ran",
                            ctx: transformResult.ctx,
                            ops: transformResult.ops,
                            step: exports._internals.hashOp(Object.assign(Object.assign({}, stepResult), {
                                error: transformResult.error
                            })),
                            retriable: transformResult.retriable
                        };
                    }
                    return transformResult;
                }
                const newSteps = await this.filterNewSteps(Array.from(this.state.steps.values()));
                if (newSteps) {
                    return {
                        type: "steps-found",
                        ctx: this.fnArg,
                        ops: this.ops,
                        steps: newSteps
                    };
                }
            },
            /**
             * While trying to find a step that Inngest has told us to run, we've
             * timed out or have otherwise decided that it doesn't exist.
             */ "step-not-found": ({ step })=>{
                return {
                    type: "step-not-found",
                    ctx: this.fnArg,
                    ops: this.ops,
                    step
                };
            }
        };
    }
    getCheckpointHandler(type) {
        return this.checkpointHandlers[type];
    }
    async tryExecuteStep(steps) {
        var _a;
        const hashedStepIdToRun = this.options.requestedRunStep || this.getEarlyExecRunStep(steps);
        if (!hashedStepIdToRun) {
            return;
        }
        const step = steps.find((step)=>step.hashedId === hashedStepIdToRun && step.fn);
        if (step) {
            return await this.executeStep(step);
        }
        /**
         * Ensure we reset the timeout if we have a requested run step but couldn't
         * find it, but also that we don't reset if we found and executed it.
         */ void ((_a = this.timeout) === null || _a === void 0 ? void 0 : _a.reset());
    }
    /**
     * Given a list of outgoing ops, decide if we can execute an op early and
     * return the ID of the step to execute if we can.
     */ getEarlyExecRunStep(steps) {
        /**
         * We may have been disabled due to parallelism, in which case we can't
         * immediately execute unless explicitly requested.
         */ if (this.options.disableImmediateExecution) return;
        const unfulfilledSteps = steps.filter((step)=>!step.fulfilled);
        if (unfulfilledSteps.length !== 1) return;
        const op = unfulfilledSteps[0];
        if (op && op.op === types_js_1.StepOpCode.StepPlanned) {
            return op.hashedId;
        }
    }
    async filterNewSteps(foundSteps) {
        var _a, _b, _c, _d, _e, _f;
        if (this.options.requestedRunStep) {
            return;
        }
        /**
         * Gather any steps that aren't memoized and report them.
         */ const newSteps = foundSteps.filter((step)=>!step.fulfilled);
        if (!newSteps.length) {
            return;
        }
        /**
         * Warn if we've found new steps but haven't yet seen all previous
         * steps. This may indicate that step presence isn't determinate.
         */ let knownSteps = 0;
        for (const step of foundSteps){
            if (step.fulfilled) {
                knownSteps++;
            }
        }
        const foundAllCompletedSteps = this.state.stepsToFulfill === knownSteps;
        if (!foundAllCompletedSteps) {
            // TODO Tag
            console.warn((0, errors_js_1.prettyError)({
                type: "warn",
                whatHappened: "Function may be indeterminate",
                why: "We found new steps before seeing all previous steps, which may indicate that the function is non-deterministic.",
                consequences: "This may cause unexpected behaviour as Inngest executes your function.",
                reassurance: "This is expected if a function is updated in the middle of a run, but may indicate a bug if not."
            }));
        }
        /**
         * We're finishing up; let's trigger the last of the hooks.
         */ await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
        await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
        await ((_f = (_e = this.state.hooks) === null || _e === void 0 ? void 0 : _e.afterExecution) === null || _f === void 0 ? void 0 : _f.call(_e));
        const stepList = newSteps.map((step)=>({
                displayName: step.displayName,
                op: step.op,
                id: step.hashedId,
                name: step.name,
                opts: step.opts
            }));
        /**
         * We also run `onSendEvent` middleware hooks against `step.invoke()` steps
         * to ensure that their `data` is transformed correctly.
         */ return await this.transformNewSteps(stepList);
    }
    /**
     * Using middleware, transform any newly-found steps before returning them to
     * an Inngest Server.
     */ async transformNewSteps(steps) {
        return Promise.all(steps.map(async (step)=>{
            var _a, _b, _c, _d, _e, _f, _g;
            if (step.op !== types_js_1.StepOpCode.InvokeFunction) {
                return step;
            }
            const onSendEventHooks = await (0, InngestMiddleware_js_1.getHookStack)(this.options.fn["middleware"], "onSendEvent", undefined, {
                transformInput: (prev, output)=>{
                    return Object.assign(Object.assign({}, prev), output);
                },
                transformOutput: (prev, output)=>{
                    return {
                        result: Object.assign(Object.assign({}, prev.result), output === null || output === void 0 ? void 0 : output.result)
                    };
                }
            });
            /**
             * For each event being sent, create a new `onSendEvent` hook stack to
             * process it. We do this as middleware hooks are intended to run once
             * during each lifecycle (onFunctionRun or onSendEvent) and here, a hook
             * is run for every single event.
             *
             * This is done because a developer can use this hook to filter out
             * events entirely; if we batch all of the events together, we can't
             * tell which ones were filtered out if we're processing >1 invocation
             * here.
             */ const transformedPayload = await ((_a = onSendEventHooks.transformInput) === null || _a === void 0 ? void 0 : _a.call(onSendEventHooks, {
                payloads: [
                    Object.assign(Object.assign({}, (_c = (_b = step.opts) === null || _b === void 0 ? void 0 : _b.payload) !== null && _c !== void 0 ? _c : {}), {
                        name: consts_js_1.internalEvents.FunctionInvoked
                    })
                ]
            }));
            const newPayload = InngestStepTools_js_1.invokePayloadSchema.parse((_e = (_d = transformedPayload === null || transformedPayload === void 0 ? void 0 : transformedPayload.payloads) === null || _d === void 0 ? void 0 : _d[0]) !== null && _e !== void 0 ? _e : {});
            return Object.assign(Object.assign({}, step), {
                opts: Object.assign(Object.assign({}, step.opts), {
                    payload: Object.assign(Object.assign({}, (_g = (_f = step.opts) === null || _f === void 0 ? void 0 : _f.payload) !== null && _g !== void 0 ? _g : {}), newPayload)
                })
            });
        }));
    }
    async executeStep({ id, name, opts, fn, displayName }) {
        var _a, _b, _c, _d, _e;
        (_a = this.timeout) === null || _a === void 0 ? void 0 : _a.clear();
        await ((_c = (_b = this.state.hooks) === null || _b === void 0 ? void 0 : _b.afterMemoization) === null || _c === void 0 ? void 0 : _c.call(_b));
        await ((_e = (_d = this.state.hooks) === null || _d === void 0 ? void 0 : _d.beforeExecution) === null || _e === void 0 ? void 0 : _e.call(_d));
        const outgoingOp = {
            id,
            op: types_js_1.StepOpCode.StepRun,
            name,
            opts,
            displayName
        };
        this.state.executingStep = outgoingOp;
        const store = await (0, als_js_1.getAsyncCtx)();
        if (store) {
            store.executingStep = {
                id,
                name: displayName
            };
        }
        this.debug(`executing step "${id}"`);
        return (0, promises_js_1.runAsPromise)(fn)// eslint-disable-next-line @typescript-eslint/no-misused-promises
        .finally(async ()=>{
            var _a, _b;
            if (store) {
                delete store.executingStep;
            }
            await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterExecution) === null || _b === void 0 ? void 0 : _b.call(_a));
        }).then((data)=>{
            return Object.assign(Object.assign({}, outgoingOp), {
                data
            });
        }).catch((error)=>{
            return Object.assign(Object.assign({}, outgoingOp), {
                op: types_js_1.StepOpCode.StepError,
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                error
            });
        });
    }
    /**
     * Starts execution of the user's function, including triggering checkpoints
     * and middleware hooks where appropriate.
     */ async startExecution() {
        var _a, _b, _c, _d, _e, _f, _g;
        /**
         * Mutate input as neccessary based on middleware.
         */ await this.transformInput();
        /**
         * Start the timer to time out the run if needed.
         */ void ((_a = this.timeout) === null || _a === void 0 ? void 0 : _a.start());
        await ((_c = (_b = this.state.hooks) === null || _b === void 0 ? void 0 : _b.beforeMemoization) === null || _c === void 0 ? void 0 : _c.call(_b));
        /**
         * If we had no state to begin with, immediately end the memoization phase.
         */ if (this.state.allStateUsed()) {
            await ((_e = (_d = this.state.hooks) === null || _d === void 0 ? void 0 : _d.afterMemoization) === null || _e === void 0 ? void 0 : _e.call(_d));
            await ((_g = (_f = this.state.hooks) === null || _f === void 0 ? void 0 : _f.beforeExecution) === null || _g === void 0 ? void 0 : _g.call(_f));
        }
        /**
         * Trigger the user's function.
         */ (0, promises_js_1.runAsPromise)(()=>this.userFnToRun(this.fnArg))// eslint-disable-next-line @typescript-eslint/no-misused-promises
        .finally(async ()=>{
            var _a, _b, _c, _d, _e, _f;
            await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
            await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
            await ((_f = (_e = this.state.hooks) === null || _e === void 0 ? void 0 : _e.afterExecution) === null || _f === void 0 ? void 0 : _f.call(_e));
        }).then((data)=>{
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            this.state.setCheckpoint({
                type: "function-resolved",
                data
            });
        }).catch((error)=>{
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            this.state.setCheckpoint({
                type: "function-rejected",
                error
            });
        });
    }
    /**
     * Using middleware, transform input before running.
     */ async transformInput() {
        var _a, _b;
        const inputMutations = await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.transformInput) === null || _b === void 0 ? void 0 : _b.call(_a, {
            ctx: Object.assign({}, this.fnArg),
            steps: Object.values(this.state.stepState),
            fn: this.options.fn,
            reqArgs: this.options.reqArgs
        }));
        if (inputMutations === null || inputMutations === void 0 ? void 0 : inputMutations.ctx) {
            this.fnArg = inputMutations.ctx;
        }
        if (inputMutations === null || inputMutations === void 0 ? void 0 : inputMutations.steps) {
            this.state.stepState = Object.fromEntries(inputMutations.steps.map((step)=>[
                    step.id,
                    step
                ]));
        }
    }
    /**
     * Using middleware, transform output before returning.
     */ async transformOutput(dataOrError) {
        var _a, _b, _c, _d;
        const output = Object.assign({}, dataOrError);
        /**
         * If we've been given an error and it's one that we just threw from a step,
         * we should return a `NonRetriableError` to stop execution.
         */ if (typeof output.error !== "undefined") {
            output.data = (0, errors_js_1.serializeError)(output.error);
        }
        const isStepExecution = Boolean(this.state.executingStep);
        const transformedOutput = await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.transformOutput) === null || _b === void 0 ? void 0 : _b.call(_a, {
            result: Object.assign({}, output),
            step: this.state.executingStep
        }));
        const { data, error } = Object.assign(Object.assign({}, output), transformedOutput === null || transformedOutput === void 0 ? void 0 : transformedOutput.result);
        if (!isStepExecution) {
            await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.finished) === null || _d === void 0 ? void 0 : _d.call(_c, {
                result: Object.assign({}, typeof error !== "undefined" ? {
                    error
                } : {
                    data
                })
            }));
        }
        if (typeof error !== "undefined") {
            /**
             * Ensure we give middleware the chance to decide on retriable behaviour
             * by looking at the error returned from output transformation.
             */ let retriable = !(error instanceof NonRetriableError_js_1.NonRetriableError || error instanceof StepError_js_1.StepError);
            if (retriable && error instanceof RetryAfterError_js_1.RetryAfterError) {
                retriable = error.retryAfter;
            }
            const serializedError = (0, errors_js_1.minifyPrettyError)((0, errors_js_1.serializeError)(error));
            return {
                type: "function-rejected",
                ctx: this.fnArg,
                ops: this.ops,
                error: serializedError,
                retriable
            };
        }
        return {
            type: "function-resolved",
            ctx: this.fnArg,
            ops: this.ops,
            data: (0, functions_js_1.undefinedToNull)(data)
        };
    }
    createExecutionState() {
        const d = (0, promises_js_1.createDeferredPromiseWithStack)();
        let checkpointResolve = d.deferred.resolve;
        const checkpointResults = d.results;
        const loop = function(cleanUp) {
            return __asyncGenerator(this, arguments, function*() {
                try {
                    while(true){
                        const res = (yield __await(checkpointResults.next())).value;
                        if (res) {
                            yield yield __await(res);
                        }
                    }
                } finally{
                    cleanUp === null || cleanUp === void 0 ? void 0 : cleanUp();
                }
            });
        }(()=>{
            var _a;
            (_a = this.timeout) === null || _a === void 0 ? void 0 : _a.clear();
            void checkpointResults.return();
        });
        const stepsToFulfill = Object.keys(this.options.stepState).length;
        const state = {
            stepState: this.options.stepState,
            stepsToFulfill,
            steps: new Map(),
            loop,
            hasSteps: Boolean(stepsToFulfill),
            stepCompletionOrder: [
                ...this.options.stepCompletionOrder
            ],
            remainingStepsToBeSeen: new Set(this.options.stepCompletionOrder),
            setCheckpoint: (checkpoint)=>{
                ({ resolve: checkpointResolve } = checkpointResolve(checkpoint));
            },
            allStateUsed: ()=>{
                return this.state.remainingStepsToBeSeen.size === 0;
            }
        };
        return state;
    }
    get ops() {
        return Object.fromEntries(this.state.steps);
    }
    createFnArg() {
        var _a, _b, _c, _d;
        const step = this.createStepTools();
        let fnArg = Object.assign(Object.assign({}, this.options.data), {
            step
        });
        /**
         * Handle use of the `onFailure` option by deserializing the error.
         */ if (this.options.isFailureHandler) {
            const eventData = zod_1.z.object({
                error: types_js_1.jsonErrorSchema
            }).parse((_a = fnArg.event) === null || _a === void 0 ? void 0 : _a.data);
            fnArg = Object.assign(Object.assign({}, fnArg), {
                error: (0, errors_js_1.deserializeError)(eventData.error)
            });
        }
        return (_d = (_c = (_b = this.options).transformCtx) === null || _c === void 0 ? void 0 : _c.call(_b, fnArg)) !== null && _d !== void 0 ? _d : fnArg;
    }
    createStepTools() {
        /**
         * A list of steps that have been found and are being rolled up before being
         * reported to the core loop.
         */ const foundStepsToReport = new Map();
        /**
         * A map of the subset of found steps to report that have not yet been
         * handled. Used for fast access to steps that need to be handled in order.
         */ const unhandledFoundStepsToReport = new Map();
        /**
         * A map of the latest sequential step indexes found for each step ID. Used
         * to ensure that we don't index steps in parallel.
         *
         * Note that these must be sequential; if we've seen or assigned `a:1`,
         * `a:2` and `a:4`, the latest sequential step index is `2`.
         *
         */ const expectedNextStepIndexes = new Map();
        /**
         * An ordered list of step IDs that have yet to be handled in this
         * execution. Used to ensure that we handle steps in the order they were
         * found and based on the `stepCompletionOrder` in this execution's state.
         */ const remainingStepCompletionOrder = this.state.stepCompletionOrder.slice();
        /**
         * A promise that's used to ensure that step reporting cannot be run more than
         * once in a given asynchronous time span.
         */ let foundStepsReportPromise;
        /**
         * A promise that's used to represent middleware hooks running before
         * execution.
         */ let beforeExecHooksPromise;
        /**
         * A flag used to ensure that we only warn about parallel indexing once per
         * execution to avoid spamming the console.
         */ let warnOfParallelIndexing = false;
        /**
         * Counts the number of times we've extended this tick.
         */ let tickExtensionCount = 0;
        /**
         * Given a colliding step ID, maybe warn the user about parallel indexing.
         */ const maybeWarnOfParallelIndexing = (collisionId)=>{
            if (warnOfParallelIndexing) {
                return;
            }
            const stepExists = this.state.steps.has(collisionId);
            if (stepExists) {
                const stepFoundThisTick = foundStepsToReport.has(collisionId);
                if (!stepFoundThisTick) {
                    warnOfParallelIndexing = true;
                    console.warn((0, errors_js_1.prettyError)({
                        type: "warn",
                        whatHappened: "We detected that you have multiple steps with the same ID.",
                        code: errors_js_1.ErrCode.AUTOMATIC_PARALLEL_INDEXING,
                        why: `This can happen if you're using the same ID for multiple steps across different chains of parallel work. We found the issue with step "${collisionId}".`,
                        reassurance: "Your function is still running, though it may exhibit unexpected behaviour.",
                        consequences: "Using the same IDs across parallel chains of work can cause unexpected behaviour.",
                        toFixNow: "We recommend using a unique ID for each step, especially those happening in parallel."
                    }));
                }
            }
        };
        /**
         * A helper used to report steps to the core loop. Used after adding an item
         * to `foundStepsToReport`.
         */ const reportNextTick = ()=>{
            // Being explicit instead of using `??=` to appease TypeScript.
            if (foundStepsReportPromise) {
                return;
            }
            let extensionPromise;
            if (++tickExtensionCount >= 10) {
                tickExtensionCount = 0;
                extensionPromise = new Promise((resolve)=>setTimeout(resolve));
            } else {
                extensionPromise = (0, promises_js_1.resolveAfterPending)();
            }
            foundStepsReportPromise = extensionPromise/**
                 * Ensure that we wait for this promise to resolve before continuing.
                 *
                 * The groups in which steps are reported can affect how we detect some
                 * more complex determinism issues like parallel indexing. This promise
                 * can represent middleware hooks being run early, in the middle of
                 * ingesting steps to report.
                 *
                 * Because of this, it's important we wait for this middleware to resolve
                 * before continuing to report steps to ensure that all steps have a
                 * chance to be reported throughout this asynchronous action.
                 */ .then(()=>beforeExecHooksPromise).then(()=>{
                var _a;
                foundStepsReportPromise = undefined;
                for(let i = 0; i < remainingStepCompletionOrder.length; i++){
                    const nextStepId = remainingStepCompletionOrder[i];
                    if (!nextStepId) {
                        continue;
                    }
                    const handled = (_a = unhandledFoundStepsToReport.get(nextStepId)) === null || _a === void 0 ? void 0 : _a.handle();
                    if (handled) {
                        remainingStepCompletionOrder.splice(i, 1);
                        unhandledFoundStepsToReport.delete(nextStepId);
                        return void reportNextTick();
                    }
                }
                // If we've handled no steps in this "tick," roll up everything we've
                // found and report it.
                const steps = [
                    ...foundStepsToReport.values()
                ];
                foundStepsToReport.clear();
                unhandledFoundStepsToReport.clear();
                return void this.state.setCheckpoint({
                    type: "steps-found",
                    steps: steps
                });
            });
        };
        /**
         * A helper used to push a step to the list of steps to report.
         */ const pushStepToReport = (step)=>{
            foundStepsToReport.set(step.id, step);
            unhandledFoundStepsToReport.set(step.hashedId, step);
            reportNextTick();
        };
        const stepHandler = async ({ args, matchOp, opts })=>{
            var _a, _b, _c, _d;
            await beforeExecHooksPromise;
            const stepOptions = (0, InngestStepTools_js_1.getStepOptions)(args[0]);
            const opId = matchOp(stepOptions, ...args.slice(1));
            if (this.state.executingStep) {
                /**
                 * If a step is found after asynchronous actions during another step's
                 * execution, everything is fine. The problem here is if we've found
                 * that a step nested inside another a step, which is something we don't
                 * support at the time of writing.
                 *
                 * In this case, we could use something like Async Hooks to understand
                 * how the step is being triggered, though this isn't available in all
                 * environments.
                 *
                 * Therefore, we'll only show a warning here to indicate that this is
                 * potentially an issue.
                 */ console.warn((0, errors_js_1.prettyError)({
                    whatHappened: `We detected that you have nested \`step.*\` tooling in \`${(_a = opId.displayName) !== null && _a !== void 0 ? _a : opId.id}\``,
                    consequences: "Nesting `step.*` tooling is not supported.",
                    type: "warn",
                    reassurance: "It's possible to see this warning if steps are separated by regular asynchronous calls, which is fine.",
                    stack: true,
                    toFixNow: "Make sure you're not using `step.*` tooling inside of other `step.*` tooling. If you need to compose steps together, you can create a new async function and call it from within your step function, or use promise chaining.",
                    code: errors_js_1.ErrCode.NESTING_STEPS
                }));
            }
            if (this.state.steps.has(opId.id)) {
                const originalId = opId.id;
                maybeWarnOfParallelIndexing(originalId);
                const expectedNextIndex = (_b = expectedNextStepIndexes.get(originalId)) !== null && _b !== void 0 ? _b : 1;
                for(let i = expectedNextIndex;; i++){
                    const newId = originalId + InngestStepTools_js_1.STEP_INDEXING_SUFFIX + i;
                    if (!this.state.steps.has(newId)) {
                        expectedNextStepIndexes.set(originalId, i + 1);
                        opId.id = newId;
                        break;
                    }
                }
            }
            const { promise, resolve, reject } = (0, promises_js_1.createDeferredPromise)();
            const hashedId = exports._internals.hashId(opId.id);
            const stepState = this.state.stepState[hashedId];
            let isFulfilled = false;
            if (stepState) {
                stepState.seen = true;
                this.state.remainingStepsToBeSeen.delete(hashedId);
                if (typeof stepState.input === "undefined") {
                    isFulfilled = true;
                }
            }
            let extraOpts;
            let fnArgs = [
                ...args
            ];
            if (typeof (stepState === null || stepState === void 0 ? void 0 : stepState.input) !== "undefined" && Array.isArray(stepState.input)) {
                switch(opId.op){
                    // `step.run()` has its function input affected
                    case types_js_1.StepOpCode.StepPlanned:
                        {
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                            fnArgs = [
                                ...args.slice(0, 2),
                                ...stepState.input
                            ];
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                            extraOpts = {
                                input: [
                                    ...stepState.input
                                ]
                            };
                            break;
                        }
                    // `step.ai.infer()` has its body affected
                    case types_js_1.StepOpCode.AiGateway:
                        {
                            extraOpts = {
                                body: Object.assign(Object.assign({}, typeof ((_c = opId.opts) === null || _c === void 0 ? void 0 : _c.body) === "object" ? Object.assign({}, opId.opts.body) : {}), stepState.input[0])
                            };
                            break;
                        }
                }
            }
            const step = Object.assign(Object.assign({}, opId), {
                opts: Object.assign(Object.assign({}, opId.opts), extraOpts),
                rawArgs: fnArgs,
                hashedId,
                input: stepState === null || stepState === void 0 ? void 0 : stepState.input,
                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                fn: (opts === null || opts === void 0 ? void 0 : opts.fn) ? ()=>{
                    var _a;
                    return (_a = opts.fn) === null || _a === void 0 ? void 0 : _a.call(opts, ...fnArgs);
                } : undefined,
                promise,
                fulfilled: isFulfilled,
                hasStepState: Boolean(stepState),
                displayName: (_d = opId.displayName) !== null && _d !== void 0 ? _d : opId.id,
                handled: false,
                handle: ()=>{
                    if (step.handled) {
                        return false;
                    }
                    step.handled = true;
                    if (isFulfilled && stepState) {
                        stepState.fulfilled = true;
                        // For some execution scenarios such as testing, `data`, `error`,
                        // and `input` may be `Promises`. This could also be the case for
                        // future middleware applications. For this reason, we'll make sure
                        // the values are fully resolved before continuing.
                        void Promise.all([
                            stepState.data,
                            stepState.error,
                            stepState.input
                        ]).then(()=>{
                            if (typeof stepState.data !== "undefined") {
                                resolve(stepState.data);
                            } else {
                                this.state.recentlyRejectedStepError = new StepError_js_1.StepError(opId.id, stepState.error);
                                reject(this.state.recentlyRejectedStepError);
                            }
                        });
                    }
                    return true;
                }
            });
            this.state.steps.set(opId.id, step);
            this.state.hasSteps = true;
            pushStepToReport(step);
            /**
             * If this is the last piece of state we had, we've now finished
             * memoizing.
             */ if (!beforeExecHooksPromise && this.state.allStateUsed()) {
                await (beforeExecHooksPromise = (async ()=>{
                    var _a, _b, _c, _d;
                    await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
                    await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
                })());
            }
            return promise;
        };
        return (0, InngestStepTools_js_1.createStepTools)(this.options.client, this, stepHandler);
    }
    getUserFnToRun() {
        if (!this.options.isFailureHandler) {
            return this.options.fn["fn"];
        }
        if (!this.options.fn["onFailureFn"]) {
            /**
             * Somehow, we've ended up detecting that this is a failure handler but
             * doesn't have an `onFailure` function. This should never happen.
             */ throw new Error("Cannot find function `onFailure` handler");
        }
        return this.options.fn["onFailureFn"];
    }
    initializeTimer(state) {
        if (!this.options.requestedRunStep) {
            return;
        }
        this.timeout = (0, promises_js_1.createTimeoutPromise)(this.timeoutDuration);
        void this.timeout.then(async ()=>{
            var _a, _b, _c, _d, _e, _f;
            await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
            await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
            await ((_f = (_e = this.state.hooks) === null || _e === void 0 ? void 0 : _e.afterExecution) === null || _f === void 0 ? void 0 : _f.call(_e));
            state.setCheckpoint({
                type: "step-not-found",
                step: {
                    id: this.options.requestedRunStep,
                    op: types_js_1.StepOpCode.StepNotFound
                }
            });
        });
    }
    async initializeMiddleware() {
        const ctx = this.options.data;
        const hooks = await (0, InngestMiddleware_js_1.getHookStack)(this.options.fn["middleware"], "onFunctionRun", {
            ctx,
            fn: this.options.fn,
            steps: Object.values(this.options.stepState),
            reqArgs: this.options.reqArgs
        }, {
            transformInput: (prev, output)=>{
                return {
                    ctx: Object.assign(Object.assign({}, prev.ctx), output === null || output === void 0 ? void 0 : output.ctx),
                    fn: this.options.fn,
                    steps: prev.steps.map((step, i)=>{
                        var _a;
                        return Object.assign(Object.assign({}, step), (_a = output === null || output === void 0 ? void 0 : output.steps) === null || _a === void 0 ? void 0 : _a[i]);
                    }),
                    reqArgs: prev.reqArgs
                };
            },
            transformOutput: (prev, output)=>{
                return {
                    result: Object.assign(Object.assign({}, prev.result), output === null || output === void 0 ? void 0 : output.result),
                    step: prev.step
                };
            }
        });
        return hooks;
    }
}
const hashId = (id)=>{
    return (0, hash_js_1.sha1)().update(id).digest("hex");
};
const hashOp = (op)=>{
    return Object.assign(Object.assign({}, op), {
        id: hashId(op.id)
    });
};
/**
 * Exported for testing.
 */ exports._internals = {
    hashOp,
    hashId
}; //# sourceMappingURL=v1.js.map
}}),
"[project]/node_modules/inngest/components/execution/v2.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __asyncValues = this && this.__asyncValues || function(o) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var m = o[Symbol.asyncIterator], i;
    return m ? m.call(o) : (o = typeof __values === "function" ? __values(o) : o[Symbol.iterator](), i = {}, verb("next"), verb("throw"), verb("return"), i[Symbol.asyncIterator] = function() {
        return this;
    }, i);
    "TURBOPACK unreachable";
    function verb(n) {
        i[n] = o[n] && function(v) {
            return new Promise(function(resolve, reject) {
                v = o[n](v), settle(resolve, reject, v.done, v.value);
            });
        };
    }
    function settle(resolve, reject, d, v) {
        Promise.resolve(v).then(function(v) {
            resolve({
                value: v,
                done: d
            });
        }, reject);
    }
};
var __await = this && this.__await || function(v) {
    return this instanceof __await ? (this.v = v, this) : new __await(v);
};
var __asyncGenerator = this && this.__asyncGenerator || function(thisArg, _arguments, generator) {
    if (!Symbol.asyncIterator) throw new TypeError("Symbol.asyncIterator is not defined.");
    var g = generator.apply(thisArg, _arguments || []), i, q = [];
    return i = Object.create((typeof AsyncIterator === "function" ? AsyncIterator : Object).prototype), verb("next"), verb("throw"), verb("return", awaitReturn), i[Symbol.asyncIterator] = function() {
        return this;
    }, i;
    "TURBOPACK unreachable";
    function awaitReturn(f) {
        return function(v) {
            return Promise.resolve(v).then(f, reject);
        };
    }
    function verb(n, f) {
        if (g[n]) {
            i[n] = function(v) {
                return new Promise(function(a, b) {
                    q.push([
                        n,
                        v,
                        a,
                        b
                    ]) > 1 || resume(n, v);
                });
            };
            if (f) i[n] = f(i[n]);
        }
    }
    function resume(n, v) {
        try {
            step(g[n](v));
        } catch (e) {
            settle(q[0][3], e);
        }
    }
    function step(r) {
        r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r);
    }
    function fulfill(value) {
        resume("next", value);
    }
    function reject(value) {
        resume("throw", value);
    }
    function settle(f, v) {
        if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]);
    }
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports._internals = exports.createV2InngestExecution = void 0;
const api_1 = __turbopack_context__.r("[externals]/@opentelemetry/api [external] (@opentelemetry/api, cjs)");
const hash_js_1 = __turbopack_context__.r("[project]/node_modules/hash.js/lib/hash.js [app-route] (ecmascript)");
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
const functions_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/functions.js [app-route] (ecmascript)");
const promises_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/promises.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const version_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/version.js [app-route] (ecmascript)");
const InngestMiddleware_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestMiddleware.js [app-route] (ecmascript)");
const InngestStepTools_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestStepTools.js [app-route] (ecmascript)");
const NonRetriableError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/NonRetriableError.js [app-route] (ecmascript)");
const RetryAfterError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/RetryAfterError.js [app-route] (ecmascript)");
const StepError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/StepError.js [app-route] (ecmascript)");
const InngestExecution_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)");
const als_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/als.js [app-route] (ecmascript)");
const access_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/otel/access.js [app-route] (ecmascript)");
const createV2InngestExecution = (options)=>{
    return new V2InngestExecution(options);
};
exports.createV2InngestExecution = createV2InngestExecution;
class V2InngestExecution extends InngestExecution_js_1.InngestExecution {
    constructor(options){
        super(options);
        this.timeoutDuration = 1000 * 10;
        this.userFnToRun = this.getUserFnToRun();
        this.state = this.createExecutionState();
        this.fnArg = this.createFnArg();
        this.checkpointHandlers = this.createCheckpointHandlers();
        this.initializeTimer(this.state);
        this.debug("created new V2 execution for run;", this.options.requestedRunStep ? `wanting to run step "${this.options.requestedRunStep}"` : "discovering steps");
        this.debug("existing state keys:", Object.keys(this.state.stepState));
    }
    /**
     * Idempotently start the execution of the user's function.
     */ start() {
        if (!this.execution) {
            this.debug("starting V2 execution");
            const tracer = api_1.trace.getTracer("inngest", version_js_1.version);
            this.execution = (0, als_js_1.getAsyncLocalStorage)().then((als)=>{
                return als.run({
                    app: this.options.client,
                    ctx: this.fnArg
                }, async ()=>{
                    return tracer.startActiveSpan("inngest.execution", (span)=>{
                        var _a;
                        (_a = access_js_1.clientProcessorMap.get(this.options.client)) === null || _a === void 0 ? void 0 : _a.declareStartingSpan({
                            span,
                            runId: this.options.runId,
                            traceparent: this.options.headers[consts_js_1.headerKeys.TraceParent],
                            tracestate: this.options.headers[consts_js_1.headerKeys.TraceState]
                        });
                        return this._start().then((result)=>{
                            this.debug("result:", result);
                            return result;
                        }).finally(()=>{
                            span.end();
                        });
                    });
                });
            });
        }
        return this.execution;
    }
    /**
     * Starts execution of the user's function and the core loop.
     */ async _start() {
        var _a, e_1, _b, _c;
        var _d, _e;
        try {
            const allCheckpointHandler = this.getCheckpointHandler("");
            this.state.hooks = await this.initializeMiddleware();
            await this.startExecution();
            try {
                for(var _f = true, _g = __asyncValues(this.state.loop), _h; _h = await _g.next(), _a = _h.done, !_a; _f = true){
                    _c = _h.value;
                    _f = false;
                    const checkpoint = _c;
                    await allCheckpointHandler(checkpoint);
                    const handler = this.getCheckpointHandler(checkpoint.type);
                    const result = await handler(checkpoint);
                    if (result) {
                        return result;
                    }
                }
            } catch (e_1_1) {
                e_1 = {
                    error: e_1_1
                };
            } finally{
                try {
                    if (!_f && !_a && (_b = _g.return)) await _b.call(_g);
                } finally{
                    if (e_1) throw e_1.error;
                }
            }
        } catch (error) {
            return await this.transformOutput({
                error
            });
        } finally{
            void this.state.loop.return();
            await ((_e = (_d = this.state.hooks) === null || _d === void 0 ? void 0 : _d.beforeResponse) === null || _e === void 0 ? void 0 : _e.call(_d));
        }
        /**
         * If we're here, the generator somehow finished without returning a value.
         * This should never happen.
         */ throw new Error("Core loop finished without returning a value");
    }
    /**
     * Creates a handler for every checkpoint type, defining what to do when we
     * reach that checkpoint in the core loop.
     */ createCheckpointHandlers() {
        return {
            /**
             * Run for all checkpoints. Best used for logging or common actions.
             * Use other handlers to return values and interrupt the core loop.
             */ "": (checkpoint)=>{
                this.debug("checkpoint:", checkpoint);
            },
            /**
             * The user's function has completed and returned a value.
             */ "function-resolved": async (checkpoint)=>{
                return await this.transformOutput({
                    data: checkpoint.data
                });
            },
            /**
             * The user's function has thrown an error.
             */ "function-rejected": async (checkpoint)=>{
                return await this.transformOutput({
                    error: checkpoint.error
                });
            },
            /**
             * We've found one or more steps. Here we may want to run a step or report
             * them back to Inngest.
             */ "steps-found": async ({ steps })=>{
                const stepResult = await this.tryExecuteStep(steps);
                if (stepResult) {
                    const transformResult = await this.transformOutput(stepResult);
                    /**
                     * Transforming output will always return either function rejection or
                     * resolution. In most cases, this can be immediately returned, but in
                     * this particular case we want to handle it differently.
                     */ if (transformResult.type === "function-resolved") {
                        return {
                            type: "step-ran",
                            ctx: transformResult.ctx,
                            ops: transformResult.ops,
                            step: exports._internals.hashOp(Object.assign(Object.assign({}, stepResult), {
                                data: transformResult.data
                            }))
                        };
                    } else if (transformResult.type === "function-rejected") {
                        return {
                            type: "step-ran",
                            ctx: transformResult.ctx,
                            ops: transformResult.ops,
                            step: exports._internals.hashOp(Object.assign(Object.assign({}, stepResult), {
                                error: transformResult.error
                            })),
                            retriable: transformResult.retriable
                        };
                    }
                    return transformResult;
                }
                const newSteps = await this.filterNewSteps(Array.from(this.state.steps.values()));
                if (newSteps) {
                    return {
                        type: "steps-found",
                        ctx: this.fnArg,
                        ops: this.ops,
                        steps: newSteps
                    };
                }
            },
            /**
             * While trying to find a step that Inngest has told us to run, we've
             * timed out or have otherwise decided that it doesn't exist.
             */ "step-not-found": ({ step })=>{
                return {
                    type: "step-not-found",
                    ctx: this.fnArg,
                    ops: this.ops,
                    step
                };
            }
        };
    }
    getCheckpointHandler(type) {
        return this.checkpointHandlers[type];
    }
    async tryExecuteStep(steps) {
        var _a;
        const hashedStepIdToRun = this.options.requestedRunStep || this.getEarlyExecRunStep(steps);
        if (!hashedStepIdToRun) {
            return;
        }
        const step = steps.find((step)=>step.hashedId === hashedStepIdToRun && step.fn);
        if (step) {
            return await this.executeStep(step);
        }
        /**
         * Ensure we reset the timeout if we have a requested run step but couldn't
         * find it, but also that we don't reset if we found and executed it.
         */ void ((_a = this.timeout) === null || _a === void 0 ? void 0 : _a.reset());
    }
    /**
     * Given a list of outgoing ops, decide if we can execute an op early and
     * return the ID of the step to execute if we can.
     */ getEarlyExecRunStep(steps) {
        /**
         * We may have been disabled due to parallelism, in which case we can't
         * immediately execute unless explicitly requested.
         */ if (this.options.disableImmediateExecution) return;
        const unfulfilledSteps = steps.filter((step)=>!step.fulfilled);
        if (unfulfilledSteps.length !== 1) return;
        const op = unfulfilledSteps[0];
        if (op && op.op === types_js_1.StepOpCode.StepPlanned) {
            return op.hashedId;
        }
    }
    async filterNewSteps(foundSteps) {
        var _a, _b, _c, _d, _e, _f;
        if (this.options.requestedRunStep) {
            return;
        }
        /**
         * Gather any steps that aren't memoized and report them.
         */ const newSteps = foundSteps.filter((step)=>!step.fulfilled);
        if (!newSteps.length) {
            return;
        }
        /**
         * Warn if we've found new steps but haven't yet seen all previous
         * steps. This may indicate that step presence isn't determinate.
         */ let knownSteps = 0;
        for (const step of foundSteps){
            if (step.fulfilled) {
                knownSteps++;
            }
        }
        const foundAllCompletedSteps = this.state.stepsToFulfill === knownSteps;
        if (!foundAllCompletedSteps) {
            // TODO Tag
            console.warn((0, errors_js_1.prettyError)({
                type: "warn",
                whatHappened: "Function may be indeterminate",
                why: "We found new steps before seeing all previous steps, which may indicate that the function is non-deterministic.",
                consequences: "This may cause unexpected behaviour as Inngest executes your function.",
                reassurance: "This is expected if a function is updated in the middle of a run, but may indicate a bug if not."
            }));
        }
        /**
         * We're finishing up; let's trigger the last of the hooks.
         */ await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
        await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
        await ((_f = (_e = this.state.hooks) === null || _e === void 0 ? void 0 : _e.afterExecution) === null || _f === void 0 ? void 0 : _f.call(_e));
        const stepList = newSteps.map((step)=>({
                displayName: step.displayName,
                op: step.op,
                id: step.hashedId,
                name: step.name,
                opts: step.opts
            }));
        /**
         * We also run `onSendEvent` middleware hooks against `step.invoke()` steps
         * to ensure that their `data` is transformed correctly.
         */ return await this.transformNewSteps(stepList);
    }
    /**
     * Using middleware, transform any newly-found steps before returning them to
     * an Inngest Server.
     */ async transformNewSteps(steps) {
        return Promise.all(steps.map(async (step)=>{
            var _a, _b, _c, _d, _e, _f, _g;
            if (step.op !== types_js_1.StepOpCode.InvokeFunction) {
                return step;
            }
            const onSendEventHooks = await (0, InngestMiddleware_js_1.getHookStack)(this.options.fn["middleware"], "onSendEvent", undefined, {
                transformInput: (prev, output)=>{
                    return Object.assign(Object.assign({}, prev), output);
                },
                transformOutput: (prev, output)=>{
                    return {
                        result: Object.assign(Object.assign({}, prev.result), output === null || output === void 0 ? void 0 : output.result)
                    };
                }
            });
            /**
             * For each event being sent, create a new `onSendEvent` hook stack to
             * process it. We do this as middleware hooks are intended to run once
             * during each lifecycle (onFunctionRun or onSendEvent) and here, a hook
             * is run for every single event.
             *
             * This is done because a developer can use this hook to filter out
             * events entirely; if we batch all of the events together, we can't
             * tell which ones were filtered out if we're processing >1 invocation
             * here.
             */ const transformedPayload = await ((_a = onSendEventHooks.transformInput) === null || _a === void 0 ? void 0 : _a.call(onSendEventHooks, {
                payloads: [
                    Object.assign(Object.assign({}, (_c = (_b = step.opts) === null || _b === void 0 ? void 0 : _b.payload) !== null && _c !== void 0 ? _c : {}), {
                        name: consts_js_1.internalEvents.FunctionInvoked
                    })
                ]
            }));
            const newPayload = InngestStepTools_js_1.invokePayloadSchema.parse((_e = (_d = transformedPayload === null || transformedPayload === void 0 ? void 0 : transformedPayload.payloads) === null || _d === void 0 ? void 0 : _d[0]) !== null && _e !== void 0 ? _e : {});
            return Object.assign(Object.assign({}, step), {
                opts: Object.assign(Object.assign({}, step.opts), {
                    payload: Object.assign(Object.assign({}, (_g = (_f = step.opts) === null || _f === void 0 ? void 0 : _f.payload) !== null && _g !== void 0 ? _g : {}), newPayload)
                })
            });
        }));
    }
    async executeStep({ id, name, opts, fn, displayName }) {
        var _a, _b, _c, _d, _e;
        (_a = this.timeout) === null || _a === void 0 ? void 0 : _a.clear();
        await ((_c = (_b = this.state.hooks) === null || _b === void 0 ? void 0 : _b.afterMemoization) === null || _c === void 0 ? void 0 : _c.call(_b));
        await ((_e = (_d = this.state.hooks) === null || _d === void 0 ? void 0 : _d.beforeExecution) === null || _e === void 0 ? void 0 : _e.call(_d));
        const outgoingOp = {
            id,
            op: types_js_1.StepOpCode.StepRun,
            name,
            opts,
            displayName
        };
        this.state.executingStep = outgoingOp;
        const store = await (0, als_js_1.getAsyncCtx)();
        if (store) {
            store.executingStep = {
                id,
                name: displayName
            };
        }
        this.debug(`executing step "${id}"`);
        return (0, promises_js_1.runAsPromise)(fn)// eslint-disable-next-line @typescript-eslint/no-misused-promises
        .finally(async ()=>{
            var _a, _b;
            if (store) {
                delete store.executingStep;
            }
            await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterExecution) === null || _b === void 0 ? void 0 : _b.call(_a));
        }).then((data)=>{
            return Object.assign(Object.assign({}, outgoingOp), {
                data
            });
        }).catch((error)=>{
            return Object.assign(Object.assign({}, outgoingOp), {
                op: types_js_1.StepOpCode.StepError,
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                error
            });
        });
    }
    /**
     * Starts execution of the user's function, including triggering checkpoints
     * and middleware hooks where appropriate.
     */ async startExecution() {
        var _a, _b, _c, _d, _e, _f, _g;
        /**
         * Mutate input as neccessary based on middleware.
         */ await this.transformInput();
        /**
         * Start the timer to time out the run if needed.
         */ void ((_a = this.timeout) === null || _a === void 0 ? void 0 : _a.start());
        await ((_c = (_b = this.state.hooks) === null || _b === void 0 ? void 0 : _b.beforeMemoization) === null || _c === void 0 ? void 0 : _c.call(_b));
        /**
         * If we had no state to begin with, immediately end the memoization phase.
         */ if (this.state.allStateUsed()) {
            await ((_e = (_d = this.state.hooks) === null || _d === void 0 ? void 0 : _d.afterMemoization) === null || _e === void 0 ? void 0 : _e.call(_d));
            await ((_g = (_f = this.state.hooks) === null || _f === void 0 ? void 0 : _f.beforeExecution) === null || _g === void 0 ? void 0 : _g.call(_f));
        }
        /**
         * Trigger the user's function.
         */ (0, promises_js_1.runAsPromise)(()=>this.userFnToRun(this.fnArg))// eslint-disable-next-line @typescript-eslint/no-misused-promises
        .finally(async ()=>{
            var _a, _b, _c, _d, _e, _f;
            await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
            await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
            await ((_f = (_e = this.state.hooks) === null || _e === void 0 ? void 0 : _e.afterExecution) === null || _f === void 0 ? void 0 : _f.call(_e));
        }).then((data)=>{
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            this.state.setCheckpoint({
                type: "function-resolved",
                data
            });
        }).catch((error)=>{
            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
            this.state.setCheckpoint({
                type: "function-rejected",
                error
            });
        });
    }
    /**
     * Using middleware, transform input before running.
     */ async transformInput() {
        var _a, _b;
        const inputMutations = await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.transformInput) === null || _b === void 0 ? void 0 : _b.call(_a, {
            ctx: Object.assign({}, this.fnArg),
            steps: Object.values(this.state.stepState),
            fn: this.options.fn,
            reqArgs: this.options.reqArgs
        }));
        if (inputMutations === null || inputMutations === void 0 ? void 0 : inputMutations.ctx) {
            this.fnArg = inputMutations.ctx;
        }
        if (inputMutations === null || inputMutations === void 0 ? void 0 : inputMutations.steps) {
            this.state.stepState = Object.fromEntries(inputMutations.steps.map((step)=>[
                    step.id,
                    step
                ]));
        }
    }
    /**
     * Using middleware, transform output before returning.
     */ async transformOutput(dataOrError) {
        var _a, _b, _c, _d;
        const output = Object.assign({}, dataOrError);
        /**
         * If we've been given an error and it's one that we just threw from a step,
         * we should return a `NonRetriableError` to stop execution.
         */ if (typeof output.error !== "undefined") {
            output.data = (0, errors_js_1.serializeError)(output.error);
        }
        const isStepExecution = Boolean(this.state.executingStep);
        const transformedOutput = await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.transformOutput) === null || _b === void 0 ? void 0 : _b.call(_a, {
            result: Object.assign({}, output),
            step: this.state.executingStep
        }));
        const { data, error } = Object.assign(Object.assign({}, output), transformedOutput === null || transformedOutput === void 0 ? void 0 : transformedOutput.result);
        if (!isStepExecution) {
            await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.finished) === null || _d === void 0 ? void 0 : _d.call(_c, {
                result: Object.assign({}, typeof error !== "undefined" ? {
                    error
                } : {
                    data
                })
            }));
        }
        if (typeof error !== "undefined") {
            /**
             * Ensure we give middleware the chance to decide on retriable behaviour
             * by looking at the error returned from output transformation.
             */ let retriable = !(error instanceof NonRetriableError_js_1.NonRetriableError || error instanceof StepError_js_1.StepError);
            if (retriable && error instanceof RetryAfterError_js_1.RetryAfterError) {
                retriable = error.retryAfter;
            }
            const serializedError = (0, errors_js_1.minifyPrettyError)((0, errors_js_1.serializeError)(error));
            return {
                type: "function-rejected",
                ctx: this.fnArg,
                ops: this.ops,
                error: serializedError,
                retriable
            };
        }
        return {
            type: "function-resolved",
            ctx: this.fnArg,
            ops: this.ops,
            data: (0, functions_js_1.undefinedToNull)(data)
        };
    }
    createExecutionState() {
        const d = (0, promises_js_1.createDeferredPromiseWithStack)();
        let checkpointResolve = d.deferred.resolve;
        const checkpointResults = d.results;
        const loop = function(cleanUp) {
            return __asyncGenerator(this, arguments, function*() {
                try {
                    while(true){
                        const res = (yield __await(checkpointResults.next())).value;
                        if (res) {
                            yield yield __await(res);
                        }
                    }
                } finally{
                    cleanUp === null || cleanUp === void 0 ? void 0 : cleanUp();
                }
            });
        }(()=>{
            var _a;
            (_a = this.timeout) === null || _a === void 0 ? void 0 : _a.clear();
            void checkpointResults.return();
        });
        const stepsToFulfill = Object.keys(this.options.stepState).length;
        const state = {
            stepState: this.options.stepState,
            stepsToFulfill,
            steps: new Map(),
            loop,
            hasSteps: Boolean(stepsToFulfill),
            stepCompletionOrder: [
                ...this.options.stepCompletionOrder
            ],
            remainingStepsToBeSeen: new Set(this.options.stepCompletionOrder),
            setCheckpoint: (checkpoint)=>{
                ({ resolve: checkpointResolve } = checkpointResolve(checkpoint));
            },
            allStateUsed: ()=>{
                return this.state.remainingStepsToBeSeen.size === 0;
            }
        };
        return state;
    }
    get ops() {
        return Object.fromEntries(this.state.steps);
    }
    createFnArg() {
        var _a, _b, _c, _d;
        const step = this.createStepTools();
        let fnArg = Object.assign(Object.assign({}, this.options.data), {
            step
        });
        /**
         * Handle use of the `onFailure` option by deserializing the error.
         */ if (this.options.isFailureHandler) {
            const eventData = zod_1.z.object({
                error: types_js_1.jsonErrorSchema
            }).parse((_a = fnArg.event) === null || _a === void 0 ? void 0 : _a.data);
            fnArg = Object.assign(Object.assign({}, fnArg), {
                error: (0, errors_js_1.deserializeError)(eventData.error)
            });
        }
        return (_d = (_c = (_b = this.options).transformCtx) === null || _c === void 0 ? void 0 : _c.call(_b, fnArg)) !== null && _d !== void 0 ? _d : fnArg;
    }
    createStepTools() {
        /**
         * A list of steps that have been found and are being rolled up before being
         * reported to the core loop.
         */ const foundStepsToReport = new Map();
        /**
         * A map of the subset of found steps to report that have not yet been
         * handled. Used for fast access to steps that need to be handled in order.
         */ const unhandledFoundStepsToReport = new Map();
        /**
         * A map of the latest sequential step indexes found for each step ID. Used
         * to ensure that we don't index steps in parallel.
         *
         * Note that these must be sequential; if we've seen or assigned `a:1`,
         * `a:2` and `a:4`, the latest sequential step index is `2`.
         *
         */ const expectedNextStepIndexes = new Map();
        /**
         * A promise that's used to ensure that step reporting cannot be run more than
         * once in a given asynchronous time span.
         */ let foundStepsReportPromise;
        /**
         * A promise that's used to represent middleware hooks running before
         * execution.
         */ let beforeExecHooksPromise;
        /**
         * A helper used to report steps to the core loop. Used after adding an item
         * to `foundStepsToReport`.
         */ const reportNextTick = ()=>{
            // Being explicit instead of using `??=` to appease TypeScript.
            if (foundStepsReportPromise) {
                return;
            }
            foundStepsReportPromise = new Promise((resolve)=>setImmediate(resolve))/**
                 * Ensure that we wait for this promise to resolve before continuing.
                 *
                 * The groups in which steps are reported can affect how we detect some
                 * more complex determinism issues like parallel indexing. This promise
                 * can represent middleware hooks being run early, in the middle of
                 * ingesting steps to report.
                 *
                 * Because of this, it's important we wait for this middleware to resolve
                 * before continuing to report steps to ensure that all steps have a
                 * chance to be reported throughout this asynchronous action.
                 */ .then(()=>beforeExecHooksPromise).then(()=>{
                foundStepsReportPromise = undefined;
                for (const [hashedId, step] of unhandledFoundStepsToReport){
                    if (step.handle()) {
                        unhandledFoundStepsToReport.delete(hashedId);
                        if (step.fulfilled) {
                            foundStepsToReport.delete(step.id);
                        }
                    }
                }
                if (foundStepsToReport.size) {
                    const steps = [
                        ...foundStepsToReport.values()
                    ];
                    foundStepsToReport.clear();
                    return void this.state.setCheckpoint({
                        type: "steps-found",
                        steps: steps
                    });
                }
            });
        };
        /**
         * A helper used to push a step to the list of steps to report.
         */ const pushStepToReport = (step)=>{
            foundStepsToReport.set(step.id, step);
            unhandledFoundStepsToReport.set(step.hashedId, step);
            reportNextTick();
        };
        const stepHandler = async ({ args, matchOp, opts })=>{
            var _a, _b, _c, _d;
            await beforeExecHooksPromise;
            const stepOptions = (0, InngestStepTools_js_1.getStepOptions)(args[0]);
            const opId = matchOp(stepOptions, ...args.slice(1));
            if (this.state.executingStep) {
                /**
                 * If a step is found after asynchronous actions during another step's
                 * execution, everything is fine. The problem here is if we've found
                 * that a step nested inside another a step, which is something we don't
                 * support at the time of writing.
                 *
                 * In this case, we could use something like Async Hooks to understand
                 * how the step is being triggered, though this isn't available in all
                 * environments.
                 *
                 * Therefore, we'll only show a warning here to indicate that this is
                 * potentially an issue.
                 */ console.warn((0, errors_js_1.prettyError)({
                    whatHappened: `We detected that you have nested \`step.*\` tooling in \`${(_a = opId.displayName) !== null && _a !== void 0 ? _a : opId.id}\``,
                    consequences: "Nesting `step.*` tooling is not supported.",
                    type: "warn",
                    reassurance: "It's possible to see this warning if steps are separated by regular asynchronous calls, which is fine.",
                    stack: true,
                    toFixNow: "Make sure you're not using `step.*` tooling inside of other `step.*` tooling. If you need to compose steps together, you can create a new async function and call it from within your step function, or use promise chaining.",
                    code: errors_js_1.ErrCode.NESTING_STEPS
                }));
            }
            if (this.state.steps.has(opId.id)) {
                const originalId = opId.id;
                const expectedNextIndex = (_b = expectedNextStepIndexes.get(originalId)) !== null && _b !== void 0 ? _b : 1;
                for(let i = expectedNextIndex;; i++){
                    const newId = originalId + InngestStepTools_js_1.STEP_INDEXING_SUFFIX + i;
                    if (!this.state.steps.has(newId)) {
                        expectedNextStepIndexes.set(originalId, i + 1);
                        opId.id = newId;
                        break;
                    }
                }
            }
            const { promise, resolve, reject } = (0, promises_js_1.createDeferredPromise)();
            const hashedId = exports._internals.hashId(opId.id);
            const stepState = this.state.stepState[hashedId];
            let isFulfilled = false;
            if (stepState) {
                stepState.seen = true;
                this.state.remainingStepsToBeSeen.delete(hashedId);
                if (typeof stepState.input === "undefined") {
                    isFulfilled = true;
                }
            }
            let extraOpts;
            let fnArgs = [
                ...args
            ];
            if (typeof (stepState === null || stepState === void 0 ? void 0 : stepState.input) !== "undefined" && Array.isArray(stepState.input)) {
                switch(opId.op){
                    // `step.run()` has its function input affected
                    case types_js_1.StepOpCode.StepPlanned:
                        {
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                            fnArgs = [
                                ...args.slice(0, 2),
                                ...stepState.input
                            ];
                            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                            extraOpts = {
                                input: [
                                    ...stepState.input
                                ]
                            };
                            break;
                        }
                    // `step.ai.infer()` has its body affected
                    case types_js_1.StepOpCode.AiGateway:
                        {
                            extraOpts = {
                                body: Object.assign(Object.assign({}, typeof ((_c = opId.opts) === null || _c === void 0 ? void 0 : _c.body) === "object" ? Object.assign({}, opId.opts.body) : {}), stepState.input[0])
                            };
                            break;
                        }
                }
            }
            const step = Object.assign(Object.assign({}, opId), {
                opts: Object.assign(Object.assign({}, opId.opts), extraOpts),
                rawArgs: fnArgs,
                hashedId,
                input: stepState === null || stepState === void 0 ? void 0 : stepState.input,
                // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
                fn: (opts === null || opts === void 0 ? void 0 : opts.fn) ? ()=>{
                    var _a;
                    return (_a = opts.fn) === null || _a === void 0 ? void 0 : _a.call(opts, ...fnArgs);
                } : undefined,
                promise,
                fulfilled: isFulfilled,
                hasStepState: Boolean(stepState),
                displayName: (_d = opId.displayName) !== null && _d !== void 0 ? _d : opId.id,
                handled: false,
                handle: ()=>{
                    if (step.handled) {
                        return false;
                    }
                    step.handled = true;
                    if (isFulfilled && stepState) {
                        stepState.fulfilled = true;
                        // For some execution scenarios such as testing, `data`, `error`,
                        // and `input` may be `Promises`. This could also be the case for
                        // future middleware applications. For this reason, we'll make sure
                        // the values are fully resolved before continuing.
                        void Promise.all([
                            stepState.data,
                            stepState.error,
                            stepState.input
                        ]).then(()=>{
                            if (typeof stepState.data !== "undefined") {
                                resolve(stepState.data);
                            } else {
                                this.state.recentlyRejectedStepError = new StepError_js_1.StepError(opId.id, stepState.error);
                                reject(this.state.recentlyRejectedStepError);
                            }
                        });
                    }
                    return true;
                }
            });
            this.state.steps.set(opId.id, step);
            this.state.hasSteps = true;
            pushStepToReport(step);
            /**
             * If this is the last piece of state we had, we've now finished
             * memoizing.
             */ if (!beforeExecHooksPromise && this.state.allStateUsed()) {
                await (beforeExecHooksPromise = (async ()=>{
                    var _a, _b, _c, _d;
                    await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
                    await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
                })());
            }
            return promise;
        };
        return (0, InngestStepTools_js_1.createStepTools)(this.options.client, this, stepHandler);
    }
    getUserFnToRun() {
        if (!this.options.isFailureHandler) {
            return this.options.fn["fn"];
        }
        if (!this.options.fn["onFailureFn"]) {
            /**
             * Somehow, we've ended up detecting that this is a failure handler but
             * doesn't have an `onFailure` function. This should never happen.
             */ throw new Error("Cannot find function `onFailure` handler");
        }
        return this.options.fn["onFailureFn"];
    }
    initializeTimer(state) {
        if (!this.options.requestedRunStep) {
            return;
        }
        this.timeout = (0, promises_js_1.createTimeoutPromise)(this.timeoutDuration);
        void this.timeout.then(async ()=>{
            var _a, _b, _c, _d, _e, _f;
            await ((_b = (_a = this.state.hooks) === null || _a === void 0 ? void 0 : _a.afterMemoization) === null || _b === void 0 ? void 0 : _b.call(_a));
            await ((_d = (_c = this.state.hooks) === null || _c === void 0 ? void 0 : _c.beforeExecution) === null || _d === void 0 ? void 0 : _d.call(_c));
            await ((_f = (_e = this.state.hooks) === null || _e === void 0 ? void 0 : _e.afterExecution) === null || _f === void 0 ? void 0 : _f.call(_e));
            state.setCheckpoint({
                type: "step-not-found",
                step: {
                    id: this.options.requestedRunStep,
                    op: types_js_1.StepOpCode.StepNotFound
                }
            });
        });
    }
    async initializeMiddleware() {
        const ctx = this.options.data;
        const hooks = await (0, InngestMiddleware_js_1.getHookStack)(this.options.fn["middleware"], "onFunctionRun", {
            ctx,
            fn: this.options.fn,
            steps: Object.values(this.options.stepState),
            reqArgs: this.options.reqArgs
        }, {
            transformInput: (prev, output)=>{
                return {
                    ctx: Object.assign(Object.assign({}, prev.ctx), output === null || output === void 0 ? void 0 : output.ctx),
                    fn: this.options.fn,
                    steps: prev.steps.map((step, i)=>{
                        var _a;
                        return Object.assign(Object.assign({}, step), (_a = output === null || output === void 0 ? void 0 : output.steps) === null || _a === void 0 ? void 0 : _a[i]);
                    }),
                    reqArgs: prev.reqArgs
                };
            },
            transformOutput: (prev, output)=>{
                return {
                    result: Object.assign(Object.assign({}, prev.result), output === null || output === void 0 ? void 0 : output.result),
                    step: prev.step
                };
            }
        });
        return hooks;
    }
}
const hashId = (id)=>{
    return (0, hash_js_1.sha1)().update(id).digest("hex");
};
const hashOp = (op)=>{
    return Object.assign(Object.assign({}, op), {
        id: hashId(op.id)
    });
};
/**
 * Exported for testing.
 */ exports._internals = {
    hashOp,
    hashId
}; //# sourceMappingURL=v2.js.map
}}),
"[project]/node_modules/inngest/components/InngestFunction.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.InngestFunction = void 0;
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
const InngestExecution_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/InngestExecution.js [app-route] (ecmascript)");
const v0_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/v0.js [app-route] (ecmascript)");
const v1_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/v1.js [app-route] (ecmascript)");
const v2_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/v2.js [app-route] (ecmascript)");
/**
 * A stateless Inngest function, wrapping up function configuration and any
 * in-memory steps to run when triggered.
 *
 * This function can be "registered" to create a handler that Inngest can
 * trigger remotely.
 *
 * @public
 */ class InngestFunction {
    /**
     * A stateless Inngest function, wrapping up function configuration and any
     * in-memory steps to run when triggered.
     *
     * This function can be "registered" to create a handler that Inngest can
     * trigger remotely.
     */ constructor(client, /**
     * Options
     */ opts, fn){
        this.client = client;
        this.opts = opts;
        this.fn = fn;
        this.onFailureFn = this.opts.onFailure;
        this.middleware = this.client["initializeMiddleware"](this.opts.middleware, {
            registerInput: {
                fn: this
            },
            prefixStack: this.client["middleware"]
        });
    }
    /**
     * The generated or given ID for this function.
     */ id(prefix) {
        return [
            prefix,
            this.opts.id
        ].filter(Boolean).join("-");
    }
    /**
     * The generated or given ID for this function, prefixed with the app ID. This
     * is used for routing invokes and identifying the function across apps.
     */ get absoluteId() {
        return this.id(this.client.id);
    }
    /**
     * The name of this function as it will appear in the Inngest Cloud UI.
     */ get name() {
        return this.opts.name || this.id();
    }
    /**
     * The description of this function.
     */ get description() {
        return this.opts.description;
    }
    /**
     * Retrieve the Inngest config for this function.
     */ getConfig({ baseUrl, appPrefix, isConnect }) {
        var _a, _b;
        const fnId = this.id(appPrefix);
        const stepUrl = new URL(baseUrl.href);
        stepUrl.searchParams.set(consts_js_1.queryKeys.FnId, fnId);
        stepUrl.searchParams.set(consts_js_1.queryKeys.StepId, InngestFunction.stepId);
        const { retries: attempts, cancelOn, idempotency, batchEvents, rateLimit, throttle, concurrency, debounce, timeouts, priority, singleton } = this.opts;
        /**
         * Convert retries into the format required when defining function
         * configuration.
         */ const retries = typeof attempts === "undefined" ? undefined : {
            attempts
        };
        const fn = {
            id: fnId,
            name: this.name,
            triggers: ((_a = this.opts.triggers) !== null && _a !== void 0 ? _a : []).map((trigger)=>{
                if ("event" in trigger) {
                    return {
                        event: trigger.event,
                        expression: trigger.if
                    };
                }
                return {
                    cron: trigger.cron
                };
            }),
            steps: {
                [InngestFunction.stepId]: {
                    id: InngestFunction.stepId,
                    name: InngestFunction.stepId,
                    runtime: {
                        type: isConnect ? "ws" : "http",
                        url: stepUrl.href
                    },
                    retries
                }
            },
            idempotency,
            batchEvents,
            rateLimit,
            throttle,
            concurrency,
            debounce,
            priority,
            timeouts,
            singleton
        };
        if (cancelOn) {
            fn.cancel = cancelOn.map(({ event, timeout, if: ifStr, match })=>{
                const ret = {
                    event
                };
                if (timeout) {
                    ret.timeout = (0, strings_js_1.timeStr)(timeout);
                }
                if (match) {
                    ret.if = `event.${match} == async.${match}`;
                } else if (ifStr) {
                    ret.if = ifStr;
                }
                return ret;
            }, []);
        }
        const config = [
            fn
        ];
        if (this.onFailureFn) {
            const id = `${fn.id}${InngestFunction.failureSuffix}`;
            const name = `${(_b = fn.name) !== null && _b !== void 0 ? _b : fn.id} (failure)`;
            const failureStepUrl = new URL(stepUrl.href);
            failureStepUrl.searchParams.set(consts_js_1.queryKeys.FnId, id);
            config.push({
                id,
                name,
                triggers: [
                    {
                        event: consts_js_1.internalEvents.FunctionFailed,
                        expression: `event.data.function_id == '${fnId}'`
                    }
                ],
                steps: {
                    [InngestFunction.stepId]: {
                        id: InngestFunction.stepId,
                        name: InngestFunction.stepId,
                        runtime: {
                            type: "http",
                            url: failureStepUrl.href
                        },
                        retries: {
                            attempts: 1
                        }
                    }
                }
            });
        }
        return config;
    }
    createExecution(opts) {
        const options = Object.assign({
            fn: this
        }, opts.partialOptions);
        const versionHandlers = {
            [InngestExecution_js_1.ExecutionVersion.V2]: ()=>(0, v2_js_1.createV2InngestExecution)(options),
            [InngestExecution_js_1.ExecutionVersion.V1]: ()=>(0, v1_js_1.createV1InngestExecution)(options),
            [InngestExecution_js_1.ExecutionVersion.V0]: ()=>(0, v0_js_1.createV0InngestExecution)(options)
        };
        return versionHandlers[opts.version]();
    }
    shouldOptimizeParallelism() {
        var _a, _b;
        // TODO We should check the commhandler's client instead of this one?
        return (_b = (_a = this.opts.optimizeParallelism) !== null && _a !== void 0 ? _a : this.client["options"].optimizeParallelism) !== null && _b !== void 0 ? _b : false;
    }
}
exports.InngestFunction = InngestFunction;
InngestFunction.stepId = "step";
InngestFunction.failureSuffix = "-failure"; //# sourceMappingURL=InngestFunction.js.map
}}),
"[project]/node_modules/inngest/components/InngestFunctionReference.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.referenceFunction = exports.InngestFunctionReference = void 0;
/**
 * A reference to an `InngestFunction` that can be used to represent both local
 * and remote functions without pulling in the full function definition (i.e.
 * dependencies).
 *
 * These references can be invoked in the same manner as a regular
 * `InngestFunction`.
 *
 * To create a reference function, use the {@link referenceFunction} helper.
 *
 * @public
 */ class InngestFunctionReference {
    constructor(opts){
        this.opts = opts;
    }
}
exports.InngestFunctionReference = InngestFunctionReference;
/**
 * Create a reference to an `InngestFunction` that can be used to represent both
 * local and remote functions without pulling in the full function definition
 * (i.e. dependencies).
 *
 * These references can be invoked in the same manner as a regular
 * `InngestFunction`.
 *
 * @public
 */ const referenceFunction = ({ functionId, appId })=>{
    return new InngestFunctionReference({
        functionId,
        appId
    });
};
exports.referenceFunction = referenceFunction; //# sourceMappingURL=InngestFunctionReference.js.map
}}),
"[project]/node_modules/inngest/components/InngestStepTools.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function(o, v) {
    Object.defineProperty(o, "default", {
        enumerable: true,
        value: v
    });
} : function(o, v) {
    o["default"] = v;
});
var __importStar = this && this.__importStar || function() {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function(o) {
            var ar = [];
            for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function(mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) {
            for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== "default") __createBinding(result, mod, k[i]);
        }
        __setModuleDefault(result, mod);
        return result;
    };
}();
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.invokePayloadSchema = exports.gatewaySymbol = exports.createStepTools = exports.STEP_INDEXING_SUFFIX = exports.getStepOptions = void 0;
const ai_1 = __turbopack_context__.r("[project]/node_modules/@inngest/ai/dist/index.js [app-route] (ecmascript)");
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
const Temporal = __importStar(__turbopack_context__.r("[project]/node_modules/inngest/helpers/temporal.js [app-route] (ecmascript)"));
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const Fetch_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/Fetch.js [app-route] (ecmascript)");
const InngestFunction_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestFunction.js [app-route] (ecmascript)");
const InngestFunctionReference_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestFunctionReference.js [app-route] (ecmascript)");
const getStepOptions = (options)=>{
    if (typeof options === "string") {
        return {
            id: options
        };
    }
    return options;
};
exports.getStepOptions = getStepOptions;
/**
 * Suffix used to namespace steps that are automatically indexed.
 */ exports.STEP_INDEXING_SUFFIX = ":";
/**
 * Create a new set of step function tools ready to be used in a step function.
 * This function should be run and a fresh set of tools provided every time a
 * function is run.
 *
 * An op stack (function state) is passed in as well as some mutable properties
 * that the tools can use to submit a new op.
 */ const createStepTools = (client, execution, stepHandler)=>{
    /**
     * A local helper used to create tools that can be used to submit an op.
     *
     * When using this function, a generic type should be provided which is the
     * function signature exposed to the user.
     */ // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const createTool = (/**
     * A function that returns an ID for this op. This is used to ensure that
     * the op stack is correctly filled, submitted, and retrieved with the same
     * ID.
     *
     * It is passed the arguments passed by the user.
     *
     * Most simple tools will likely only need to define this.
     */ matchOp, opts)=>{
        return async (...args)=>{
            const parsedArgs = args;
            return stepHandler({
                args: parsedArgs,
                matchOp,
                opts
            });
        };
    };
    /**
     * Create a new step run tool that can be used to run a step function using
     * `step.run()` as a shim.
     */ const createStepRun = (/**
     * The sub-type of this step tool, exposed via `opts.type` when the op is
     * reported.
     */ type)=>{
        return createTool(({ id, name }, _fn, ...input)=>{
            const opts = Object.assign(Object.assign({}, input.length ? {
                input
            } : {}), type ? {
                type
            } : {});
            return Object.assign({
                id,
                op: types_js_1.StepOpCode.StepPlanned,
                name: id,
                displayName: name !== null && name !== void 0 ? name : id
            }, Object.keys(opts).length ? {
                opts
            } : {});
        }, {
            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
            fn: (_, fn, ...input)=>fn(...input)
        });
    };
    /**
     * Define the set of tools the user has access to for their step functions.
     *
     * Each key is the function name and is expected to run `createTool` and pass
     * a generic type for that function as it will appear in the user's code.
     */ const tools = {
        /**
         * Send one or many events to Inngest. Should always be used in place of
         * `inngest.send()` to ensure that the event send is successfully retried
         * and not sent multiple times due to memoisation.
         *
         * @example
         * ```ts
         * await step.sendEvent("emit-user-creation", {
         *   name: "app/user.created",
         *   data: { id: 123 },
         * });
         *
         * await step.sendEvent("emit-user-updates", [
         *   {
         *     name: "app/user.created",
         *     data: { id: 123 },
         *   },
         *   {
         *     name: "app/user.feed.created",
         *     data: { id: 123 },
         *   },
         * ]);
         * ```
         *
         * Returns a promise that will resolve once the event has been sent.
         */ sendEvent: createTool(({ id, name })=>{
            return {
                id,
                op: types_js_1.StepOpCode.StepPlanned,
                name: "sendEvent",
                displayName: name !== null && name !== void 0 ? name : id
            };
        }, {
            fn: (idOrOptions, payload)=>{
                return client["_send"]({
                    payload,
                    headers: execution["options"]["headers"]
                });
            }
        }),
        /**
         * EXPERIMENTAL: This API is not yet stable and may change in the future
         * without a major version bump.
         *
         * Wait for a particular signal to be received before continuing. When the
         * signal is received, its data will be returned.
         */ waitForSignal: createTool(({ id, name }, opts)=>{
            // TODO Should support Temporal.DurationLike, Temporal.InstantLike,
            // Temporal.ZonedDateTimeLike
            return {
                id,
                op: types_js_1.StepOpCode.WaitForSignal,
                name: opts.signal,
                displayName: name !== null && name !== void 0 ? name : id,
                opts: {
                    signal: opts.signal,
                    timeout: (0, strings_js_1.timeStr)(opts.timeout),
                    conflict: opts.onConflict
                }
            };
        }),
        /**
         * Send a Signal to Inngest.
         */ sendSignal: createTool(({ id, name }, opts)=>{
            return {
                id,
                op: types_js_1.StepOpCode.StepPlanned,
                name: "sendSignal",
                displayName: name !== null && name !== void 0 ? name : id,
                opts: {
                    type: "step.sendSignal",
                    signal: opts.signal
                }
            };
        }, {
            fn: (_idOrOptions, opts)=>{
                return client["_sendSignal"]({
                    signal: opts.signal,
                    data: opts.data,
                    headers: execution["options"]["headers"]
                });
            }
        }),
        /**
         * Wait for a particular event to be received before continuing. When the
         * event is received, it will be returned.
         *
         * You can also provide options to control the particular event that is
         * received, for example to ensure that a user ID matches between two
         * events, or to only wait a maximum amount of time before giving up and
         * returning `null` instead of any event data.
         */ waitForEvent: createTool(({ id, name }, /**
         * Options to control the event we're waiting for.
         */ opts)=>{
            const matchOpts = {
                timeout: (0, strings_js_1.timeStr)(typeof opts === "string" ? opts : opts.timeout)
            };
            if (typeof opts !== "string") {
                if (opts === null || opts === void 0 ? void 0 : opts.match) {
                    matchOpts.if = `event.${opts.match} == async.${opts.match}`;
                } else if (opts === null || opts === void 0 ? void 0 : opts.if) {
                    matchOpts.if = opts.if;
                }
            }
            return {
                id,
                op: types_js_1.StepOpCode.WaitForEvent,
                name: opts.event,
                opts: matchOpts,
                displayName: name !== null && name !== void 0 ? name : id
            };
        }),
        /**
         * Use this tool to run business logic. Each call to `run` will be retried
         * individually, meaning you can compose complex workflows that safely
         * retry dependent asynchronous actions.
         *
         * The function you pass to `run` will be called only when this "step" is to
         * be executed and can be synchronous or asynchronous.
         *
         * In either case, the return value of the function will be the return value
         * of the `run` tool, meaning you can return and reason about return data
         * for next steps.
         */ run: createStepRun(),
        /**
         * AI tooling for running AI models and other AI-related tasks.
         */ ai: {
            /**
             * Use this tool to have Inngest make your AI calls. Useful for agentic workflows.
             *
             * Input is also tracked for this tool, meaning you can pass input to the
             * function and it will be displayed and editable in the UI.
             */ infer: createTool(({ id, name }, options)=>{
                var _a, _b;
                const modelCopy = Object.assign({}, options.model);
                // Allow the model to mutate options and body for this call
                (_b = (_a = options.model).onCall) === null || _b === void 0 ? void 0 : _b.call(_a, modelCopy, options.body);
                return {
                    id,
                    op: types_js_1.StepOpCode.AiGateway,
                    displayName: name !== null && name !== void 0 ? name : id,
                    opts: {
                        type: "step.ai.infer",
                        url: modelCopy.url,
                        headers: modelCopy.headers,
                        auth_key: modelCopy.authKey,
                        format: modelCopy.format,
                        body: options.body
                    }
                };
            }),
            /**
             * Use this tool to wrap AI models and other AI-related tasks. Each call
             * to `wrap` will be retried individually, meaning you can compose complex
             * workflows that safely retry dependent asynchronous actions.
             *
             * Input is also tracked for this tool, meaning you can pass input to the
             * function and it will be displayed and editable in the UI.
             */ wrap: createStepRun("step.ai.wrap"),
            /**
             * Models for AI inference and other AI-related tasks.
             */ models: Object.assign({}, ai_1.models)
        },
        /**
         * Wait a specified amount of time before continuing.
         *
         * The time to wait can be specified using a `number` of milliseconds or an
         * `ms`-compatible time string like `"1 hour"`, `"30 mins"`, or `"2.5d"`.
         *
         * {@link https://npm.im/ms}
         *
         * To wait until a particular date, use `sleepUntil` instead.
         */ sleep: createTool(({ id, name }, time)=>{
            /**
             * The presence of this operation in the returned stack indicates that the
             * sleep is over and we should continue execution.
             */ const msTimeStr = (0, strings_js_1.timeStr)(Temporal.isTemporalDuration(time) ? time.total({
                unit: "milliseconds"
            }) : time);
            return {
                id,
                op: types_js_1.StepOpCode.Sleep,
                name: msTimeStr,
                displayName: name !== null && name !== void 0 ? name : id
            };
        }),
        /**
         * Wait until a particular date before continuing by passing a `Date`.
         *
         * To wait for a particular amount of time from now, always use `sleep`
         * instead.
         */ sleepUntil: createTool(({ id, name }, time)=>{
            try {
                const iso = Temporal.getISOString(time);
                /**
                 * The presence of this operation in the returned stack indicates that the
                 * sleep is over and we should continue execution.
                 */ return {
                    id,
                    op: types_js_1.StepOpCode.Sleep,
                    name: iso,
                    displayName: name !== null && name !== void 0 ? name : id
                };
            } catch (err) {
                /**
                 * If we're here, it's because the date is invalid. We'll throw a custom
                 * error here to standardise this response.
                 */ // TODO PrettyError
                console.warn("Invalid `Date`, date string, `Temporal.Instant`, or `Temporal.ZonedDateTime` passed to sleepUntil;", err);
                // TODO PrettyError
                throw new Error(`Invalid \`Date\`, date string, \`Temporal.Instant\`, or \`Temporal.ZonedDateTime\` passed to sleepUntil: ${// eslint-disable-next-line @typescript-eslint/no-explicit-any
                time}`);
            }
        }),
        /**
         * Invoke a passed Inngest `function` with the given `data`. Returns the
         * result of the returned value of the function or `null` if the function
         * does not return a value.
         *
         * A string ID can also be passed to reference functions outside of the
         * current app.
         */ invoke: createTool(({ id, name }, invokeOpts)=>{
            // Create a discriminated union to operate on based on the input types
            // available for this tool.
            const optsSchema = exports.invokePayloadSchema.extend({
                timeout: zod_1.z.union([
                    zod_1.z.number(),
                    zod_1.z.string(),
                    zod_1.z.date()
                ]).optional()
            });
            const parsedFnOpts = optsSchema.extend({
                _type: zod_1.z.literal("fullId").optional().default("fullId"),
                function: zod_1.z.string().min(1)
            }).or(optsSchema.extend({
                _type: zod_1.z.literal("fnInstance").optional().default("fnInstance"),
                function: zod_1.z.instanceof(InngestFunction_js_1.InngestFunction)
            })).or(optsSchema.extend({
                _type: zod_1.z.literal("refInstance").optional().default("refInstance"),
                function: zod_1.z.instanceof(InngestFunctionReference_js_1.InngestFunctionReference)
            })).safeParse(invokeOpts);
            if (!parsedFnOpts.success) {
                throw new Error(`Invalid invocation options passed to invoke; must include either a function or functionId.`);
            }
            const { _type, function: fn, data, user, v, timeout } = parsedFnOpts.data;
            const payload = {
                data,
                user,
                v
            };
            const opts = {
                payload,
                function_id: "",
                timeout: typeof timeout === "undefined" ? undefined : (0, strings_js_1.timeStr)(timeout)
            };
            switch(_type){
                case "fnInstance":
                    opts.function_id = fn.id(fn["client"].id);
                    break;
                case "fullId":
                    console.warn(`${consts_js_1.logPrefix} Invoking function with \`function: string\` is deprecated and will be removed in v4.0.0; use an imported function or \`referenceFunction()\` instead. See https://innge.st/ts-referencing-functions`);
                    opts.function_id = fn;
                    break;
                case "refInstance":
                    opts.function_id = [
                        fn.opts.appId || client.id,
                        fn.opts.functionId
                    ].filter(Boolean).join("-");
                    break;
            }
            return {
                id,
                op: types_js_1.StepOpCode.InvokeFunction,
                displayName: name !== null && name !== void 0 ? name : id,
                opts
            };
        }),
        /**
         * `step.fetch` is a Fetch-API-compatible function that can be used to make
         * any HTTP code durable if it's called within an Inngest function.
         *
         * It will gracefully fall back to the global `fetch` if called outside of
         * this context, and a custom fallback can be set using the `config` method.
         */ fetch: Fetch_js_1.fetch
    };
    // Add an uptyped gateway
    tools[exports.gatewaySymbol] = createTool(({ id, name }, input, init)=>{
        var _a;
        const url = input instanceof Request ? input.url : input.toString();
        const headers = {};
        if (input instanceof Request) {
            input.headers.forEach((value, key)=>headers[key] = value);
        } else if (init === null || init === void 0 ? void 0 : init.headers) {
            const h = new Headers(init.headers);
            h.forEach((value, key)=>headers[key] = value);
        }
        return {
            id,
            op: types_js_1.StepOpCode.Gateway,
            displayName: name !== null && name !== void 0 ? name : id,
            opts: {
                url,
                method: (_a = init === null || init === void 0 ? void 0 : init.method) !== null && _a !== void 0 ? _a : "GET",
                headers,
                body: init === null || init === void 0 ? void 0 : init.body
            }
        };
    });
    return tools;
};
exports.createStepTools = createStepTools;
exports.gatewaySymbol = Symbol.for("inngest.step.gateway");
/**
 * The event payload portion of the options for `step.invoke()`. This does not
 * include non-payload options like `timeout` or the function to invoke.
 */ exports.invokePayloadSchema = zod_1.z.object({
    data: zod_1.z.record(zod_1.z.any()).optional(),
    user: zod_1.z.record(zod_1.z.any()).optional(),
    v: zod_1.z.string().optional()
}); //# sourceMappingURL=InngestStepTools.js.map
}}),
"[project]/node_modules/inngest/components/Fetch.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
var __importDefault = this && this.__importDefault || function(mod) {
    return mod && mod.__esModule ? mod : {
        "default": mod
    };
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.fetch = void 0;
const debug_1 = __importDefault(__turbopack_context__.r("[project]/node_modules/debug/src/index.js [app-route] (ecmascript)"));
const als_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/execution/als.js [app-route] (ecmascript)");
const InngestStepTools_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestStepTools.js [app-route] (ecmascript)");
const globalFetch = globalThis.fetch;
const debug = (0, debug_1.default)("inngest:fetch");
const createFetchShim = ()=>{
    // eslint-disable-next-line prefer-const
    let stepFetch;
    const fetch = async (input, init)=>{
        const ctx = await (0, als_js_1.getAsyncCtx)();
        if (!ctx) {
            // Not in a function run
            if (!stepFetch.fallback) {
                // TODO Tell the user how to solve
                throw new Error("step.fetch() called outside of a function and had no fallback set");
            }
            debug("step.fetch() called outside of a function; falling back to global fetch");
            return stepFetch.fallback(input, init);
        }
        // In a function run
        if (ctx.executingStep) {
            // Inside a step
            if (!stepFetch.fallback) {
                // TODO Tell the user how to solve
                throw new Error(`step.fetch() called inside step "${ctx.executingStep.id}" and had no fallback set`);
            }
            debug(`step.fetch() called inside step "${ctx.executingStep.id}"; falling back to global fetch`);
            return stepFetch.fallback(input, init);
        }
        const targetUrl = new URL(input instanceof Request ? input.url : input.toString());
        debug("step.fetch() shimming request to", targetUrl.hostname);
        // Purposefully do not try/cacth this; if it throws then we treat that as a
        // regular `fetch()` throw, which also would not return a `Response`.
        const jsonRes = await ctx.ctx.step[InngestStepTools_js_1.gatewaySymbol](`step.fetch: ${targetUrl.hostname}`, input, init);
        return new Response(jsonRes.body, {
            headers: jsonRes.headers,
            status: jsonRes.status
        });
    };
    const optionsRef = {
        fallback: globalFetch
    };
    const extras = Object.assign({
        config: (options)=>{
            Object.assign(optionsRef, options);
            Object.assign(stepFetch, optionsRef);
            return stepFetch;
        }
    }, optionsRef);
    stepFetch = Object.assign(fetch, extras);
    return stepFetch;
};
/**
 * `fetch` is a Fetch API-compatible function that can be used to make any HTTP
 * code durable if it's called within an Inngest function.
 *
 * It will gracefully fall back to the global `fetch` if called outside of this
 * context, and a custom fallback can be set using the `config` method.
 *
 * @example Basic usage
 * ```ts
 * import { fetch } from "inngest";
 *
 * const api = new MyProductApi({ fetch });
 * ```
 *
 * @example Setting a custom fallback
 * ```ts
 * import { fetch } from "inngest";
 *
 * const api = new MyProductApi({
 *            fetch: fetch.config({ fallback: myCustomFetch }),
 * });
 * ```
 *
 * @example Do not allow fallback
 * ```ts
 * import { fetch } from "inngest";
 *
 * const api = new MyProductApi({
 *            fetch: fetch.config({ fallback: undefined }),
 * });
 * ```
 */ exports.fetch = createFetchShim(); //# sourceMappingURL=Fetch.js.map
}}),
"[project]/node_modules/inngest/api/api.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.InngestApi = void 0;
const zod_1 = __turbopack_context__.r("[project]/node_modules/zod/lib/index.js [app-route] (ecmascript)");
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const devserver_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/devserver.js [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
const net_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/net.js [app-route] (ecmascript)");
const strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const schema_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/api/schema.js [app-route] (ecmascript)");
const realtimeSubscriptionTokenSchema = zod_1.z.object({
    jwt: zod_1.z.string()
});
const sendSignalSuccessResponseSchema = zod_1.z.object({
    data: zod_1.z.object({
        run_id: zod_1.z.string().min(1)
    })
});
class InngestApi {
    constructor({ baseUrl, signingKey, signingKeyFallback, fetch, mode }){
        this.apiBaseUrl = baseUrl;
        this.signingKey = signingKey;
        this.signingKeyFallback = signingKeyFallback;
        this.fetch = fetch;
        this.mode = mode;
    }
    get hashedKey() {
        return (0, strings_js_1.hashSigningKey)(this.signingKey);
    }
    get hashedFallbackKey() {
        if (!this.signingKeyFallback) {
            return;
        }
        return (0, strings_js_1.hashSigningKey)(this.signingKeyFallback);
    }
    // set the signing key in case it was not instantiated previously
    setSigningKey(key) {
        if (typeof key === "string" && this.signingKey === "") {
            this.signingKey = key;
        }
    }
    setSigningKeyFallback(key) {
        if (typeof key === "string" && !this.signingKeyFallback) {
            this.signingKeyFallback = key;
        }
    }
    async getTargetUrl(path) {
        if (this.apiBaseUrl) {
            return new URL(path, this.apiBaseUrl);
        }
        let url = new URL(path, consts_js_1.defaultInngestApiBaseUrl);
        if (this.mode.isDev && this.mode.isInferred && !this.apiBaseUrl) {
            const devAvailable = await (0, devserver_js_1.devServerAvailable)(consts_js_1.defaultDevServerHost, this.fetch);
            if (devAvailable) {
                url = new URL(path, consts_js_1.defaultDevServerHost);
            }
        }
        return url;
    }
    async getRunSteps(runId, version) {
        return (0, net_js_1.fetchWithAuthFallback)({
            authToken: this.hashedKey,
            authTokenFallback: this.hashedFallbackKey,
            fetch: this.fetch,
            url: await this.getTargetUrl(`/v0/runs/${runId}/actions`)
        }).then(async (resp)=>{
            const data = await resp.json();
            if (resp.ok) {
                return (0, types_js_1.ok)(schema_js_1.stepsSchemas[version].parse(data));
            } else {
                return (0, types_js_1.err)(schema_js_1.errorSchema.parse(data));
            }
        }).catch((error)=>{
            return (0, types_js_1.err)({
                error: (0, errors_js_1.getErrorMessage)(error, "Unknown error retrieving step data"),
                status: 500
            });
        });
    }
    async getRunBatch(runId) {
        return (0, net_js_1.fetchWithAuthFallback)({
            authToken: this.hashedKey,
            authTokenFallback: this.hashedFallbackKey,
            fetch: this.fetch,
            url: await this.getTargetUrl(`/v0/runs/${runId}/batch`)
        }).then(async (resp)=>{
            const data = await resp.json();
            if (resp.ok) {
                return (0, types_js_1.ok)(schema_js_1.batchSchema.parse(data));
            } else {
                return (0, types_js_1.err)(schema_js_1.errorSchema.parse(data));
            }
        }).catch((error)=>{
            return (0, types_js_1.err)({
                error: (0, errors_js_1.getErrorMessage)(error, "Unknown error retrieving event batch"),
                status: 500
            });
        });
    }
    async publish(publishOptions, // eslint-disable-next-line @typescript-eslint/no-explicit-any
    data) {
        // todo it may not be a "text/stream"
        const isStream = data instanceof ReadableStream;
        const url = await this.getTargetUrl("/v1/realtime/publish");
        url.searchParams.set("channel", publishOptions.channel || "");
        if (publishOptions.runId) {
            url.searchParams.set("run_id", publishOptions.runId);
        }
        publishOptions.topics.forEach((topic)=>{
            url.searchParams.append("topic", topic);
        });
        return (0, net_js_1.fetchWithAuthFallback)({
            authToken: this.hashedKey,
            authTokenFallback: this.hashedFallbackKey,
            fetch: this.fetch,
            url,
            options: Object.assign({
                method: "POST",
                body: isStream ? data : typeof data === "string" ? data : JSON.stringify(data),
                headers: {
                    "Content-Type": isStream ? "text/stream" : "application/json"
                }
            }, isStream ? {
                duplex: "half"
            } : {})
        }).then((res)=>{
            if (!res.ok) {
                throw new Error(`Failed to publish event: ${res.status} ${res.statusText}`);
            }
            return (0, types_js_1.ok)(undefined);
        }).catch((error)=>{
            return (0, types_js_1.err)({
                error: (0, errors_js_1.getErrorMessage)(error, "Unknown error publishing event"),
                status: 500
            });
        });
    }
    async sendSignal(signalOptions, options) {
        const url = await this.getTargetUrl("/v1/signals");
        const body = {
            signal: signalOptions.signal,
            data: signalOptions.data
        };
        return (0, net_js_1.fetchWithAuthFallback)({
            authToken: this.hashedKey,
            authTokenFallback: this.hashedFallbackKey,
            fetch: this.fetch,
            url,
            options: {
                method: "POST",
                body: JSON.stringify(body),
                headers: Object.assign({
                    "Content-Type": "application/json"
                }, options === null || options === void 0 ? void 0 : options.headers)
            }
        }).then(async (res)=>{
            // A 404 is valid if the signal was not found.
            if (res.status === 404) {
                return (0, types_js_1.ok)({
                    runId: undefined
                });
            }
            // Save a clone of the response we can use to get the text of if we fail
            // to parse the JSON.
            const resClone = res.clone();
            // JSON!
            let json;
            try {
                json = await res.json();
            } catch (error) {
                // res.json() failed so not a valid JSON response
                return (0, types_js_1.err)({
                    error: `Failed to send signal: ${res.status} ${res.statusText} - ${await resClone.text()}`,
                    status: res.status
                });
            }
            // If we're not 2xx, something went wrong.
            if (!res.ok) {
                try {
                    return (0, types_js_1.err)(schema_js_1.errorSchema.parse(json));
                } catch (_a) {
                    // schema parse failed
                    return (0, types_js_1.err)({
                        error: `Failed to send signal: ${res.status} ${res.statusText} - ${await res.text()}`,
                        status: res.status
                    });
                }
            }
            // If we are 2xx, we should have a run_id.
            const parseRes = sendSignalSuccessResponseSchema.safeParse(json);
            if (!parseRes.success) {
                return (0, types_js_1.err)({
                    error: `Successfully sent signal, but response parsing failed: ${res.status} ${res.statusText} - ${await resClone.text()}`,
                    status: res.status
                });
            }
            return (0, types_js_1.ok)({
                runId: parseRes.data.data.run_id
            });
        }).catch((error)=>{
            // Catch-all if various things go wrong
            return (0, types_js_1.err)({
                error: (0, errors_js_1.getErrorMessage)(error, "Unknown error sending signal"),
                status: 500
            });
        });
    }
    async getSubscriptionToken(channel, topics) {
        const url = await this.getTargetUrl("/v1/realtime/token");
        const body = topics.map((topic)=>({
                channel,
                name: topic,
                kind: "run"
            }));
        return (0, net_js_1.fetchWithAuthFallback)({
            authToken: this.hashedKey,
            authTokenFallback: this.hashedFallbackKey,
            fetch: this.fetch,
            url,
            options: {
                method: "POST",
                body: JSON.stringify(body),
                headers: {
                    "Content-Type": "application/json"
                }
            }
        }).then(async (res)=>{
            if (!res.ok) {
                throw new Error(`Failed to get subscription token: ${res.status} ${res.statusText} - ${await res.text()}`);
            }
            const data = realtimeSubscriptionTokenSchema.parse(await res.json());
            return data.jwt;
        }).catch((error)=>{
            throw new Error((0, errors_js_1.getErrorMessage)(error, "Unknown error getting subscription token"));
        });
    }
}
exports.InngestApi = InngestApi; //# sourceMappingURL=api.js.map
}}),
"[project]/node_modules/inngest/helpers/crypto.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.createEntropy = createEntropy;
/**
 * Create a cryptographically secure random value.
 *
 * @throws {Error} If the crypto module is not available.
 */ function createEntropy(byteLength) {
    const bytes = new Uint8Array(byteLength);
    // https://developer.mozilla.org/en-US/docs/Web/API/Crypto#browser_compatibility
    const { crypto } = globalThis;
    if (!crypto) {
        // This should only happen in Node <19.
        throw new Error("missing crypto module");
    }
    if (!crypto.getRandomValues) {
        throw new Error("missing crypto.getRandomValues");
    }
    crypto.getRandomValues(bytes);
    return bytes;
} //# sourceMappingURL=crypto.js.map
}}),
"[project]/node_modules/inngest/middleware/logger.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.ProxyLogger = exports.DefaultLogger = void 0;
class DefaultLogger {
    info(...args) {
        console.info(...args);
    }
    warn(...args) {
        console.warn(...args);
    }
    error(...args) {
        console.error(...args);
    }
    debug(...args) {
        console.debug(...args);
    }
}
exports.DefaultLogger = DefaultLogger;
/**
 * ProxyLogger aims to provide a thin wrapper on user's provided logger.
 * It's expected to be turned on and off based on the function execution
 * context, so it doesn't result in duplicated logging.
 *
 * And also attempt to allow enough time for the logger to flush all logs.
 *
 * @public
 */ class ProxyLogger {
    constructor(logger){
        this.enabled = false;
        this.logger = logger;
        // Return a Proxy to forward arbitrary property access to the underlying
        // logger. For example, if the user provides a logger that has a `foo`
        // method, they can call `foo` on the ProxyLogger and it will call the
        // underlying logger's `foo` method.
        return new Proxy(this, {
            get (target, prop, receiver) {
                // Handle ProxyLogger's own methods/properties.
                if (prop in target) {
                    return Reflect.get(target, prop, receiver);
                }
                // Forward property access to the underlying logger.
                return Reflect.get(target.logger, prop, receiver);
            }
        });
    }
    info(...args) {
        if (!this.enabled) return;
        this.logger.info(...args);
    }
    warn(...args) {
        if (!this.enabled) return;
        this.logger.warn(...args);
    }
    error(...args) {
        if (!this.enabled) return;
        this.logger.error(...args);
    }
    debug(...args) {
        // there are loggers that don't implement "debug" by default
        if (!this.enabled || !(typeof this.logger.debug === "function")) return;
        this.logger.debug(...args);
    }
    enable() {
        this.enabled = true;
    }
    disable() {
        this.enabled = false;
    }
    async flush() {
        // Allow 1s for the provided logger to handle flushing since the ones that do
        // flushing usually has some kind of timeout of up to 1s.
        //
        // TODO:
        // This should only happen when using a serverless environment because it's very
        // costly from the compute perspective.
        // server runtimes should just let the logger do their thing since most of them
        // should have already figured what to do in those environments, be it threading or
        // something else.
        if (this.logger.constructor.name !== DefaultLogger.name) {
            await new Promise((resolve)=>{
                setTimeout(()=>resolve(null), 1000);
            });
        }
    }
}
exports.ProxyLogger = ProxyLogger; //# sourceMappingURL=logger.js.map
}}),
"[project]/node_modules/inngest/components/Inngest.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.builtInMiddleware = exports.Inngest = void 0;
const api_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/api/api.js [app-route] (ecmascript)");
const consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
const crypto_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/crypto.js [app-route] (ecmascript)");
const devserver_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/devserver.js [app-route] (ecmascript)");
const env_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/env.js [app-route] (ecmascript)");
const errors_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/errors.js [app-route] (ecmascript)");
const promises_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/promises.js [app-route] (ecmascript)");
const strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
const logger_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/middleware/logger.js [app-route] (ecmascript)");
const types_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/types.js [app-route] (ecmascript)");
const InngestFunction_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestFunction.js [app-route] (ecmascript)");
const InngestMiddleware_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestMiddleware.js [app-route] (ecmascript)");
/**
 * A client used to interact with the Inngest API by sending or reacting to
 * events.
 *
 * To provide event typing, see {@link EventSchemas}.
 *
 * ```ts
 * const inngest = new Inngest({ id: "my-app" });
 *
 * // or to provide event typing too
 * const inngest = new Inngest({
 *   id: "my-app",
 *   schemas: new EventSchemas().fromRecord<{
 *     "app/user.created": {
 *       data: { userId: string };
 *     };
 *   }>(),
 * });
 * ```
 *
 * @public
 */ class Inngest {
    get apiBaseUrl() {
        return this._apiBaseUrl;
    }
    get eventBaseUrl() {
        return this._eventBaseUrl;
    }
    get env() {
        var _a;
        return (_a = this.headers[consts_js_1.headerKeys.Environment]) !== null && _a !== void 0 ? _a : null;
    }
    get appVersion() {
        return this._appVersion;
    }
    /**
     * A client used to interact with the Inngest API by sending or reacting to
     * events.
     *
     * To provide event typing, see {@link EventSchemas}.
     *
     * ```ts
     * const inngest = new Inngest({ name: "My App" });
     *
     * // or to provide event typing too
     * const inngest = new Inngest({
     *   name: "My App",
     *   schemas: new EventSchemas().fromRecord<{
     *     "app/user.created": {
     *       data: { userId: string };
     *     };
     *   }>(),
     * });
     * ```
     */ constructor(options){
        /**
         * Inngest event key, used to send events to Inngest Cloud.
         */ this.eventKey = "";
        /**
         * The absolute URL of the Inngest Cloud API.
         */ this.sendEventUrl = new URL(`e/${this.eventKey}`, consts_js_1.defaultInngestEventBaseUrl);
        this.localFns = [];
        this.createFunction = (rawOptions, rawTrigger, handler)=>{
            const fn = this._createFunction(rawOptions, rawTrigger, handler);
            this.localFns.push(fn);
            return fn;
        };
        this._createFunction = (rawOptions, rawTrigger, handler)=>{
            const options = this.sanitizeOptions(rawOptions);
            const triggers = this.sanitizeTriggers(rawTrigger);
            return new InngestFunction_js_1.InngestFunction(this, Object.assign(Object.assign({}, options), {
                triggers
            }), handler);
        };
        this.options = options;
        const { id, fetch, logger = new logger_js_1.DefaultLogger(), middleware, isDev, schemas, appVersion } = this.options;
        if (!id) {
            // TODO PrettyError
            throw new Error("An `id` must be passed to create an Inngest instance.");
        }
        this.id = id;
        this._mode = (0, env_js_1.getMode)({
            explicitMode: typeof isDev === "boolean" ? isDev ? "dev" : "cloud" : undefined
        });
        this.fetch = (0, env_js_1.getFetch)(fetch);
        this.inngestApi = new api_js_1.InngestApi({
            baseUrl: this.apiBaseUrl,
            signingKey: (0, env_js_1.processEnv)(consts_js_1.envKeys.InngestSigningKey) || "",
            signingKeyFallback: (0, env_js_1.processEnv)(consts_js_1.envKeys.InngestSigningKeyFallback),
            fetch: this.fetch,
            mode: this.mode
        });
        this.schemas = schemas;
        this.loadModeEnvVars();
        this.logger = logger;
        this.middleware = this.initializeMiddleware([
            ...exports.builtInMiddleware,
            ...middleware || []
        ]);
        this._appVersion = appVersion;
    }
    /**
     * Returns a `Promise` that resolves when the app is ready and all middleware
     * has been initialized.
     */ get ready() {
        return this.middleware.then(()=>{});
    }
    /**
     * Set the environment variables for this client. This is useful if you are
     * passed environment variables at runtime instead of as globals and need to
     * update the client with those values as requests come in.
     */ setEnvVars(env = (0, env_js_1.allProcessEnv)()) {
        this.mode = (0, env_js_1.getMode)({
            env,
            client: this
        });
        return this;
    }
    loadModeEnvVars() {
        this._apiBaseUrl = this.options.baseUrl || this.mode["env"][consts_js_1.envKeys.InngestApiBaseUrl] || this.mode["env"][consts_js_1.envKeys.InngestBaseUrl] || this.mode.getExplicitUrl(consts_js_1.defaultInngestApiBaseUrl);
        this._eventBaseUrl = this.options.baseUrl || this.mode["env"][consts_js_1.envKeys.InngestEventApiBaseUrl] || this.mode["env"][consts_js_1.envKeys.InngestBaseUrl] || this.mode.getExplicitUrl(consts_js_1.defaultInngestEventBaseUrl);
        this.setEventKey(this.options.eventKey || this.mode["env"][consts_js_1.envKeys.InngestEventKey] || "");
        this.headers = (0, env_js_1.inngestHeaders)({
            inngestEnv: this.options.env,
            env: this.mode["env"]
        });
        this.inngestApi["mode"] = this.mode;
        this.inngestApi["apiBaseUrl"] = this._apiBaseUrl;
    }
    /**
     * Initialize all passed middleware, running the `register` function on each
     * in sequence and returning the requested hook registrations.
     */ async initializeMiddleware(middleware = [], opts) {
        var _a;
        /**
         * Wait for the prefix stack to run first; do not trigger ours before this
         * is complete.
         */ const prefix = await ((_a = opts === null || opts === void 0 ? void 0 : opts.prefixStack) !== null && _a !== void 0 ? _a : []);
        const stack = middleware.reduce(async (acc, m)=>{
            // Be explicit about waiting for the previous middleware to finish
            const prev = await acc;
            const next = await m.init(Object.assign({
                client: this
            }, opts === null || opts === void 0 ? void 0 : opts.registerInput));
            return [
                ...prev,
                next
            ];
        }, Promise.resolve([]));
        return [
            ...prefix,
            ...await stack
        ];
    }
    get mode() {
        return this._mode;
    }
    set mode(m) {
        this._mode = m;
        this.loadModeEnvVars();
    }
    /**
     * Given a response from Inngest, relay the error to the caller.
     */ async getResponseError(response, rawBody, foundErr = "Unknown error") {
        let errorMessage = foundErr;
        if (errorMessage === "Unknown error") {
            switch(response.status){
                case 401:
                    errorMessage = "Event key Not Found";
                    break;
                case 400:
                    errorMessage = "Cannot process event payload";
                    break;
                case 403:
                    errorMessage = "Forbidden";
                    break;
                case 404:
                    errorMessage = "Event key not found";
                    break;
                case 406:
                    errorMessage = `${JSON.stringify(await rawBody)}`;
                    break;
                case 409:
                case 412:
                    errorMessage = "Event transformation failed";
                    break;
                case 413:
                    errorMessage = "Event payload too large";
                    break;
                case 500:
                    errorMessage = "Internal server error";
                    break;
                default:
                    try {
                        errorMessage = await response.text();
                    } catch (err) {
                        errorMessage = `${JSON.stringify(await rawBody)}`;
                    }
                    break;
            }
        }
        return new Error(`Inngest API Error: ${response.status} ${errorMessage}`);
    }
    /**
     * Set the event key for this instance of Inngest. This is useful if for some
     * reason the key is not available at time of instantiation or present in the
     * `INNGEST_EVENT_KEY` environment variable.
     */ setEventKey(/**
     * Inngest event key, used to send events to Inngest Cloud. Use this is your
     * key is for some reason not available at time of instantiation or present
     * in the `INNGEST_EVENT_KEY` environment variable.
     */ eventKey) {
        this.eventKey = eventKey || consts_js_1.dummyEventKey;
        this.sendEventUrl = new URL(`e/${this.eventKey}`, this.eventBaseUrl || consts_js_1.defaultInngestEventBaseUrl);
    }
    eventKeySet() {
        return Boolean(this.eventKey) && this.eventKey !== consts_js_1.dummyEventKey;
    }
    /**
     * EXPERIMENTAL: This API is not yet stable and may change in the future
     * without a major version bump.
     *
     * Send a Signal to Inngest.
     */ async sendSignal({ signal, data, env }) {
        const headers = Object.assign({}, env ? {
            [consts_js_1.headerKeys.Environment]: env
        } : {});
        return this._sendSignal({
            signal,
            data,
            headers
        });
    }
    async _sendSignal({ signal, data, headers }) {
        var _a;
        const res = await this.inngestApi.sendSignal({
            signal,
            data
        }, Object.assign(Object.assign({}, this.headers), headers));
        if (res.ok) {
            return res.value;
        }
        throw new Error(`Failed to send signal: ${((_a = res.error) === null || _a === void 0 ? void 0 : _a.error) || "Unknown error"}`);
    }
    /**
     * Send one or many events to Inngest. Takes an entire payload (including
     * name) as each input.
     *
     * ```ts
     * await inngest.send({ name: "app/user.created", data: { id: 123 } });
     * ```
     *
     * Returns a promise that will resolve if the event(s) were sent successfully,
     * else throws with an error explaining what went wrong.
     *
     * If you wish to send an event with custom types (i.e. one that hasn't been
     * generated), make sure to add it when creating your Inngest instance, like
     * so:
     *
     * ```ts
     * const inngest = new Inngest({
     *   name: "My App",
     *   schemas: new EventSchemas().fromRecord<{
     *     "my/event": {
     *       name: "my/event";
     *       data: { bar: string };
     *     };
     *   }>(),
     * });
     * ```
     */ async send(payload, options) {
        const headers = Object.assign({}, (options === null || options === void 0 ? void 0 : options.env) ? {
            [consts_js_1.headerKeys.Environment]: options.env
        } : {});
        return this._send({
            payload,
            headers
        });
    }
    /**
     * Internal method for sending an event, used to allow Inngest internals to
     * further customize the request sent to an Inngest Server.
     */ async _send({ payload, headers }) {
        var _a;
        const nowMillis = new Date().getTime();
        let maxAttempts = 5;
        // Attempt to set the event ID seed header. If it fails then disable retries
        // (but we still want to send the event).
        try {
            const entropy = (0, crypto_js_1.createEntropy)(10);
            const entropyBase64 = Buffer.from(entropy).toString("base64");
            headers = Object.assign(Object.assign({}, headers), {
                [consts_js_1.headerKeys.EventIdSeed]: `${nowMillis},${entropyBase64}`
            });
        } catch (err) {
            let message = "Event-sending retries disabled";
            if (err instanceof Error) {
                message += `: ${err.message}`;
            }
            console.debug(message);
            // Disable retries.
            maxAttempts = 1;
        }
        const hooks = await (0, InngestMiddleware_js_1.getHookStack)(this.middleware, "onSendEvent", undefined, {
            transformInput: (prev, output)=>{
                return Object.assign(Object.assign({}, prev), output);
            },
            transformOutput (prev, output) {
                return {
                    result: Object.assign(Object.assign({}, prev.result), output === null || output === void 0 ? void 0 : output.result)
                };
            }
        });
        let payloads = Array.isArray(payload) ? payload : payload ? [
            payload
        ] : [];
        const inputChanges = await ((_a = hooks.transformInput) === null || _a === void 0 ? void 0 : _a.call(hooks, {
            payloads: [
                ...payloads
            ]
        }));
        if (inputChanges === null || inputChanges === void 0 ? void 0 : inputChanges.payloads) {
            payloads = [
                ...inputChanges.payloads
            ];
        }
        // Ensure that we always add "ts" and "data" fields to events. "ts" is auto-
        // filled by the event server so is safe, and adding here fixes Next.js
        // server action cache issues.
        payloads = payloads.map((p)=>{
            return Object.assign(Object.assign({}, p), {
                // Always generate an idempotency ID for an event for retries
                id: p.id,
                ts: p.ts || nowMillis,
                // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment
                data: p.data || {}
            });
        });
        const applyHookToOutput = async (arg)=>{
            var _a;
            const hookOutput = await ((_a = hooks.transformOutput) === null || _a === void 0 ? void 0 : _a.call(hooks, arg));
            return Object.assign(Object.assign({}, arg.result), hookOutput === null || hookOutput === void 0 ? void 0 : hookOutput.result);
        };
        /**
         * It can be valid for a user to send an empty list of events; if this
         * happens, show a warning that this may not be intended, but don't throw.
         */ if (!payloads.length) {
            console.warn((0, errors_js_1.prettyError)({
                type: "warn",
                whatHappened: "`inngest.send()` called with no events",
                reassurance: "This is not an error, but you may not have intended to do this.",
                consequences: "The returned promise will resolve, but no events have been sent to Inngest.",
                stack: true
            }));
            return await applyHookToOutput({
                result: {
                    ids: []
                }
            });
        }
        // When sending events, check if the dev server is available.  If so, use the
        // dev server.
        let url = this.sendEventUrl.href;
        /**
         * If in prod mode and key is not present, fail now.
         */ if (this.mode.isCloud && !this.eventKeySet()) {
            throw new Error((0, errors_js_1.prettyError)({
                whatHappened: "Failed to send event",
                consequences: "Your event or events were not sent to Inngest.",
                why: "We couldn't find an event key to use to send events to Inngest.",
                toFixNow: errors_js_1.fixEventKeyMissingSteps
            }));
        }
        /**
         * If dev mode has been inferred, try to hit the dev server first to see if
         * it exists. If it does, use it, otherwise fall back to whatever server we
         * have configured.
         *
         * `INNGEST_BASE_URL` is used to set both dev server and prod URLs, so if a
         * user has set this it means they have already chosen a URL to hit.
         */ if (this.mode.isDev && this.mode.isInferred && !this.eventBaseUrl) {
            const devAvailable = await (0, devserver_js_1.devServerAvailable)(consts_js_1.defaultDevServerHost, this.fetch);
            if (devAvailable) {
                url = (0, devserver_js_1.devServerUrl)(consts_js_1.defaultDevServerHost, `e/${this.eventKey}`).href;
            }
        }
        const body = await (0, promises_js_1.retryWithBackoff)(async ()=>{
            let rawBody;
            let body;
            // We don't need to do fallback auth here because this uses event keys and
            // not signing keys
            const response = await this.fetch(url, {
                method: "POST",
                body: (0, strings_js_1.stringify)(payloads),
                headers: Object.assign(Object.assign({}, this.headers), headers)
            });
            try {
                rawBody = await response.json();
                body = await types_js_1.sendEventResponseSchema.parseAsync(rawBody);
            } catch (err) {
                throw await this.getResponseError(response, rawBody);
            }
            if (body.status !== 200 || body.error) {
                throw await this.getResponseError(response, rawBody, body.error);
            }
            return body;
        }, {
            maxAttempts,
            baseDelay: 100
        });
        return await applyHookToOutput({
            result: {
                ids: body.ids
            }
        });
    }
    get funcs() {
        return this.localFns;
    }
    /**
     * Runtime-only validation.
     */ sanitizeOptions(options) {
        if (Object.prototype.hasOwnProperty.call(options, "fns")) {
            // v2 -> v3 migration warning
            console.warn(`${consts_js_1.logPrefix} InngestFunction: \`fns\` option has been deprecated in v3; use \`middleware\` instead. See https://www.inngest.com/docs/sdk/migration`);
        }
        if (typeof options === "string") {
            // v2 -> v3 runtime migraton warning
            console.warn(`${consts_js_1.logPrefix} InngestFunction: Creating a function with a string as the first argument has been deprecated in v3; pass an object instead. See https://www.inngest.com/docs/sdk/migration`);
            return {
                id: options
            };
        }
        return options;
    }
    /**
     * Runtime-only validation.
     */ sanitizeTriggers(triggers) {
        if (typeof triggers === "string") {
            // v2 -> v3 migration warning
            console.warn(`${consts_js_1.logPrefix} InngestFunction: Creating a function with a string as the second argument has been deprecated in v3; pass an object instead. See https://www.inngest.com/docs/sdk/migration`);
            return [
                {
                    event: triggers
                }
            ];
        }
        if (!Array.isArray(triggers)) {
            return [
                triggers
            ];
        }
        return triggers;
    }
}
exports.Inngest = Inngest;
/**
 * Default middleware that is included in every client, placed after the user's
 * middleware on the client but before function-level middleware.
 *
 * It is defined here to ensure that comments are included in the generated TS
 * definitions. Without this, we infer the stack of built-in middleware without
 * comments, losing a lot of value.
 *
 * If this is moved, please ensure that using this package in another project
 * can correctly access comments on mutated input and output.
 *
 * This return pattern mimics the output of a `satisfies` suffix; it's used as
 * we support versions of TypeScript prior to the introduction of `satisfies`.
 */ exports.builtInMiddleware = ((m)=>m)([
    new InngestMiddleware_js_1.InngestMiddleware({
        name: "Inngest: Logger",
        init ({ client }) {
            return {
                onFunctionRun (arg) {
                    const { ctx } = arg;
                    const metadata = {
                        runID: ctx.runId,
                        eventName: ctx.event.name,
                        functionName: arg.fn.name
                    };
                    let providedLogger = client["logger"];
                    // create a child logger if the provided logger has child logger implementation
                    try {
                        if ("child" in providedLogger) {
                            providedLogger = providedLogger.child(metadata);
                        }
                    } catch (err) {
                        console.error('failed to create "childLogger" with error: ', err);
                    // no-op
                    }
                    const logger = new logger_js_1.ProxyLogger(providedLogger);
                    return {
                        transformInput () {
                            return {
                                ctx: {
                                    /**
                                     * The passed in logger from the user.
                                     * Defaults to a console logger if not provided.
                                     */ logger: logger
                                }
                            };
                        },
                        beforeExecution () {
                            logger.enable();
                        },
                        transformOutput ({ result: { error } }) {
                            if (error) {
                                logger.error(error);
                            }
                        },
                        async beforeResponse () {
                            await logger.flush();
                        }
                    };
                }
            };
        }
    })
]); //# sourceMappingURL=Inngest.js.map
}}),
"[project]/node_modules/inngest/middleware/dependencyInjection.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.dependencyInjectionMiddleware = void 0;
const InngestMiddleware_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestMiddleware.js [app-route] (ecmascript)");
/**
 * Adds properties to the function input for every function created using this
 * app.
 */ // We can use `const` here yet due to TS constraints.
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const dependencyInjectionMiddleware = (/**
 * The context to inject into the function input.
 */ ctx)=>{
    return new InngestMiddleware_js_1.InngestMiddleware({
        name: "Inngest: Dependency Injection",
        init () {
            return {
                onFunctionRun () {
                    return {
                        transformInput () {
                            return {
                                ctx
                            };
                        }
                    };
                }
            };
        }
    });
};
exports.dependencyInjectionMiddleware = dependencyInjectionMiddleware; //# sourceMappingURL=dependencyInjection.js.map
}}),
"[project]/node_modules/inngest/index.js [app-route] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
"use strict";
/**
 * The primary entrypoint for the Inngest SDK. This provides all the necessary
 * exports to create, run, and trigger Inngest functions.
 *
 * Typical usage involves creating a new Inngest client with `Inngest`, and then
 * using the client to create functions, middleware, and other tools.
 *
 * See {@link https://www.inngest.com/docs} for more information.
 *
 * @example Create an Inngest client
 * ```ts
 * const inngest = new Inngest({
 *   id: "my-app-id",
 * });
 * ```
 *
 * @example Create an Inngest function
 * ```ts
 * const myFn = inngest.createFunction({
 *  id: "my-function",
 * }, {
 *   event: "user/created",
 * }, async ({ event, step }) => {
 *   console.log("User created:", event.data);
 * });
 * ```
 *
 * @example Send an event
 * ```ts
 * await inngest.send({
 *   name: "user/created",
 *   data: {
 *     id: "123",
 *   },
 * });
 * ```
 *
 * @module
 */ var __createBinding = this && this.__createBinding || (Object.create ? function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
        desc = {
            enumerable: true,
            get: function() {
                return m[k];
            }
        };
    }
    Object.defineProperty(o, k2, desc);
} : function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
});
var __exportStar = this && this.__exportStar || function(m, exports1) {
    for(var p in m)if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports1, p)) __createBinding(exports1, m, p);
};
Object.defineProperty(exports, "__esModule", {
    value: true
});
exports.version = exports.ProxyLogger = exports.dependencyInjectionMiddleware = exports.slugify = exports.queryKeys = exports.internalEvents = exports.headerKeys = exports.StepError = exports.RetryAfterError = exports.NonRetriableError = exports.InngestMiddleware = exports.referenceFunction = exports.InngestCommHandler = exports.Inngest = exports.EventSchemas = void 0;
__exportStar(__turbopack_context__.r("[project]/node_modules/@inngest/ai/dist/index.js [app-route] (ecmascript)"), exports);
var EventSchemas_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/EventSchemas.js [app-route] (ecmascript)");
Object.defineProperty(exports, "EventSchemas", {
    enumerable: true,
    get: function() {
        return EventSchemas_js_1.EventSchemas;
    }
});
var Fetch_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/Fetch.js [app-route] (ecmascript)");
Object.defineProperty(exports, "fetch", {
    enumerable: true,
    get: function() {
        return Fetch_js_1.fetch;
    }
});
var Inngest_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/Inngest.js [app-route] (ecmascript)");
Object.defineProperty(exports, "Inngest", {
    enumerable: true,
    get: function() {
        return Inngest_js_1.Inngest;
    }
});
var InngestCommHandler_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestCommHandler.js [app-route] (ecmascript)");
Object.defineProperty(exports, "InngestCommHandler", {
    enumerable: true,
    get: function() {
        return InngestCommHandler_js_1.InngestCommHandler;
    }
});
var InngestFunctionReference_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestFunctionReference.js [app-route] (ecmascript)");
Object.defineProperty(exports, "referenceFunction", {
    enumerable: true,
    get: function() {
        return InngestFunctionReference_js_1.referenceFunction;
    }
});
var InngestMiddleware_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/InngestMiddleware.js [app-route] (ecmascript)");
Object.defineProperty(exports, "InngestMiddleware", {
    enumerable: true,
    get: function() {
        return InngestMiddleware_js_1.InngestMiddleware;
    }
});
var NonRetriableError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/NonRetriableError.js [app-route] (ecmascript)");
Object.defineProperty(exports, "NonRetriableError", {
    enumerable: true,
    get: function() {
        return NonRetriableError_js_1.NonRetriableError;
    }
});
var RetryAfterError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/RetryAfterError.js [app-route] (ecmascript)");
Object.defineProperty(exports, "RetryAfterError", {
    enumerable: true,
    get: function() {
        return RetryAfterError_js_1.RetryAfterError;
    }
});
var StepError_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/components/StepError.js [app-route] (ecmascript)");
Object.defineProperty(exports, "StepError", {
    enumerable: true,
    get: function() {
        return StepError_js_1.StepError;
    }
});
var consts_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/consts.js [app-route] (ecmascript)");
Object.defineProperty(exports, "headerKeys", {
    enumerable: true,
    get: function() {
        return consts_js_1.headerKeys;
    }
});
Object.defineProperty(exports, "internalEvents", {
    enumerable: true,
    get: function() {
        return consts_js_1.internalEvents;
    }
});
Object.defineProperty(exports, "queryKeys", {
    enumerable: true,
    get: function() {
        return consts_js_1.queryKeys;
    }
});
var strings_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/helpers/strings.js [app-route] (ecmascript)");
Object.defineProperty(exports, "slugify", {
    enumerable: true,
    get: function() {
        return strings_js_1.slugify;
    }
});
var dependencyInjection_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/middleware/dependencyInjection.js [app-route] (ecmascript)");
Object.defineProperty(exports, "dependencyInjectionMiddleware", {
    enumerable: true,
    get: function() {
        return dependencyInjection_js_1.dependencyInjectionMiddleware;
    }
});
var logger_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/middleware/logger.js [app-route] (ecmascript)");
Object.defineProperty(exports, "ProxyLogger", {
    enumerable: true,
    get: function() {
        return logger_js_1.ProxyLogger;
    }
});
var version_js_1 = __turbopack_context__.r("[project]/node_modules/inngest/version.js [app-route] (ecmascript)");
Object.defineProperty(exports, "version", {
    enumerable: true,
    get: function() {
        return version_js_1.version;
    }
}); //# sourceMappingURL=index.js.map
}}),

};

//# sourceMappingURL=node_modules_inngest_4772aea5._.js.map