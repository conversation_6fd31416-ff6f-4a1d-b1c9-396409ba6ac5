import type { ReadableLogRecord, LogRecordExporter } from '@opentelemetry/sdk-logs';
import type { OTLPExporterNodeConfigBase } from '@opentelemetry/otlp-exporter-base';
import { OTLPExporterBase } from '@opentelemetry/otlp-exporter-base';
/**
 * Collector Logs Exporter for Node
 */
export declare class OTLPLogExporter extends OTLPExporterBase<ReadableLogRecord[]> implements LogRecordExporter {
    constructor(config?: OTLPExporterNodeConfigBase);
}
//# sourceMappingURL=OTLPLogExporter.d.ts.map