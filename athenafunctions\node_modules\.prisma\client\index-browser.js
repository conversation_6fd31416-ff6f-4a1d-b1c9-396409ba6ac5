
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('@prisma/client/runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.10.1
 * Query Engine version: 9b628578b3b7cae625e8c927178f15a170e74a9c
 */
Prisma.prismaVersion = {
  client: "6.10.1",
  engine: "9b628578b3b7cae625e8c927178f15a170e74a9c"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.UserScalarFieldEnum = {
  id: 'id',
  email: 'email',
  emailHash: 'emailHash',
  name: 'name',
  firstName: 'firstName',
  lastName: 'lastName',
  mobilePhone: 'mobilePhone',
  birthDate: 'birthDate',
  image: 'image',
  username: 'username',
  password: 'password',
  emailVerified: 'emailVerified',
  companyId: 'companyId',
  stripeCustomerId: 'stripeCustomerId',
  gender: 'gender',
  role: 'role',
  userType: 'userType',
  status: 'status',
  onboarded: 'onboarded',
  planSet: 'planSet',
  isTwoFactorEnabled: 'isTwoFactorEnabled',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.AccountScalarFieldEnum = {
  userId: 'userId',
  type: 'type',
  provider: 'provider',
  providerAccountId: 'providerAccountId',
  refresh_token: 'refresh_token',
  access_token: 'access_token',
  expires_at: 'expires_at',
  token_type: 'token_type',
  scope: 'scope',
  id_token: 'id_token',
  session_state: 'session_state',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyScalarFieldEnum = {
  id: 'id',
  name: 'name',
  location: 'location',
  address: 'address',
  about: 'about',
  description: 'description',
  email: 'email',
  phone: 'phone',
  logo: 'logo',
  website: 'website',
  xAccount: 'xAccount',
  linkedIn: 'linkedIn',
  tin: 'tin',
  benefits: 'benefits',
  tags: 'tags',
  foreignerRatio: 'foreignerRatio',
  englishUsageRatio: 'englishUsageRatio',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.CompanyLocationScalarFieldEnum = {
  id: 'id',
  companyId: 'companyId',
  locationType: 'locationType',
  firstLineAddress: 'firstLineAddress',
  secondLineAddress: 'secondLineAddress',
  thirdLineAddress: 'thirdLineAddress',
  city: 'city',
  province: 'province',
  countryCode: 'countryCode',
  zipcode: 'zipcode',
  addedById: 'addedById',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobSeekerScalarFieldEnum = {
  id: 'id',
  title: 'title',
  countryCode: 'countryCode',
  about: 'about',
  resumeFileId: 'resumeFileId',
  resumeId: 'resumeId',
  portfolio: 'portfolio',
  linkedin: 'linkedin',
  github: 'github',
  writing: 'writing',
  userId: 'userId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserFileScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  fileUse: 'fileUse',
  fileType: 'fileType',
  fileName: 'fileName',
  fileDescription: 'fileDescription',
  fileSize: 'fileSize',
  key: 'key',
  url: 'url',
  parseStatus: 'parseStatus',
  parseResult: 'parseResult',
  parseCount: 'parseCount',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.UserResumeScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  fileId: 'fileId',
  username: 'username',
  name: 'name',
  title: 'title',
  about: 'about',
  email: 'email',
  phone: 'phone',
  website: 'website',
  xaccount: 'xaccount',
  linkedin: 'linkedin',
  github: 'github',
  address: 'address',
  picture: 'picture',
  fileContent: 'fileContent',
  resumeData: 'resumeData',
  promptMarkdown: 'promptMarkdown',
  structured: 'structured',
  metadata: 'metadata',
  status: 'status',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastProcessedForFallbackMatchingAt: 'lastProcessedForFallbackMatchingAt'
};

exports.Prisma.UserResumeEvaluationScalarFieldEnum = {
  id: 'id',
  resumeId: 'resumeId',
  userId: 'userId',
  overallScore: 'overallScore',
  completenessScore: 'completenessScore',
  experienceQualityScore: 'experienceQualityScore',
  educationScore: 'educationScore',
  skillsScore: 'skillsScore',
  strengths: 'strengths',
  improvements: 'improvements',
  recommendations: 'recommendations',
  experienceEvaluation: 'experienceEvaluation',
  educationEvaluation: 'educationEvaluation',
  skillsEvaluation: 'skillsEvaluation',
  evaluationData: 'evaluationData',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobPostScalarFieldEnum = {
  id: 'id',
  jobTitle: 'jobTitle',
  employmentType: 'employmentType',
  experienceLevel: 'experienceLevel',
  country: 'country',
  location: 'location',
  department: 'department',
  salaryCurrency: 'salaryCurrency',
  salaryFrom: 'salaryFrom',
  salaryTo: 'salaryTo',
  jobDescription: 'jobDescription',
  listingDuration: 'listingDuration',
  interviewType: 'interviewType',
  localRemoteWork: 'localRemoteWork',
  overseasRemoteWork: 'overseasRemoteWork',
  skills: 'skills',
  languageRequirements: 'languageRequirements',
  tags: 'tags',
  status: 'status',
  rawText: 'rawText',
  promptMarkdown: 'promptMarkdown',
  structured: 'structured',
  metadata: 'metadata',
  companyId: 'companyId',
  fileId: 'fileId',
  userId: 'userId',
  expiresAt: 'expiresAt',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt',
  lastProcessedForFallbackMatchingAt: 'lastProcessedForFallbackMatchingAt'
};

exports.Prisma.UserResumeTempScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  fileId: 'fileId',
  rawText: 'rawText',
  resumeData: 'resumeData',
  createdAt: 'createdAt'
};

exports.Prisma.UserJobTempScalarFieldEnum = {
  id: 'id',
  userId: 'userId',
  companyId: 'companyId',
  rawText: 'rawText',
  jobData: 'jobData',
  createdAt: 'createdAt'
};

exports.Prisma.JobResumeMatchScalarFieldEnum = {
  id: 'id',
  jobId: 'jobId',
  resumeId: 'resumeId',
  criteria: 'criteria',
  overall_score: 'overall_score',
  skills_match: 'skills_match',
  experience_alignment: 'experience_alignment',
  education_fit: 'education_fit',
  match_analysis: 'match_analysis',
  candidate_strengths: 'candidate_strengths',
  matching_skills: 'matching_skills',
  missing_requirements: 'missing_requirements',
  experience_relevance: 'experience_relevance',
  matchResult: 'matchResult',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.JobShortlistScalarFieldEnum = {
  id: 'id',
  jobId: 'jobId',
  resumeId: 'resumeId',
  createdAt: 'createdAt',
  updatedAt: 'updatedAt'
};

exports.Prisma.SystemFlagScalarFieldEnum = {
  name: 'name',
  value: 'value'
};

exports.Prisma.CachedExchangeRateScalarFieldEnum = {
  id: 'id',
  baseCurrencyCode: 'baseCurrencyCode',
  targetCurrencyCode: 'targetCurrencyCode',
  exchangeRate: 'exchangeRate',
  currencySymbol: 'currencySymbol',
  currencyName: 'currencyName',
  locale: 'locale',
  countryCode: 'countryCode',
  country: 'country',
  TLD: 'TLD',
  lastFetchedAt: 'lastFetchedAt'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};
exports.Gender = exports.$Enums.Gender = {
  MALE: 'MALE',
  FEMALE: 'FEMALE'
};

exports.UserRole = exports.$Enums.UserRole = {
  SUPERADMIN: 'SUPERADMIN',
  ADMIN: 'ADMIN',
  SUPERVISOR: 'SUPERVISOR',
  USER: 'USER'
};

exports.UserType = exports.$Enums.UserType = {
  COMPANY: 'COMPANY',
  JOB_SEEKER: 'JOB_SEEKER',
  OWNER: 'OWNER',
  RECRUITER: 'RECRUITER',
  TENANT: 'TENANT'
};

exports.UserStatus = exports.$Enums.UserStatus = {
  ACTIVE: 'ACTIVE',
  INACTIVE: 'INACTIVE',
  BLOCKED: 'BLOCKED'
};

exports.PublishStatus = exports.$Enums.PublishStatus = {
  DRAFT: 'DRAFT',
  LIVE: 'LIVE'
};

exports.Prisma.ModelName = {
  User: 'User',
  Account: 'Account',
  Company: 'Company',
  CompanyLocation: 'CompanyLocation',
  JobSeeker: 'JobSeeker',
  UserFile: 'UserFile',
  UserResume: 'UserResume',
  UserResumeEvaluation: 'UserResumeEvaluation',
  JobPost: 'JobPost',
  UserResumeTemp: 'UserResumeTemp',
  UserJobTemp: 'UserJobTemp',
  JobResumeMatch: 'JobResumeMatch',
  JobShortlist: 'JobShortlist',
  SystemFlag: 'SystemFlag',
  CachedExchangeRate: 'CachedExchangeRate'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
