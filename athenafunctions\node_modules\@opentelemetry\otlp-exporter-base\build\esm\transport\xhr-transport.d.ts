import { IExporterTransport } from '../exporter-transport';
export interface XhrRequestParameters {
    url: string;
    headers: () => Record<string, string>;
}
/**
 * Creates an exporter transport that uses XHR to send the data
 * @param parameters applied to each request made by transport
 */
export declare function createXhrTransport(parameters: XhrRequestParameters): IExporterTransport;
//# sourceMappingURL=xhr-transport.d.ts.map