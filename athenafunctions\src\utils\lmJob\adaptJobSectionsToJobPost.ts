import { JobDescPost, ResumeSections } from "@/types/customTypes";
import { SegmentedJobText } from "./segmentJobMarkdown"; // Import the interface

/**
 * Adapts structured job sections and metadata from LLM processing
 * into a partial JobPost object.
 * @param structured The structured job data from LLM.
 * @param metadata Additional metadata extracted during LLM processing.
 * @returns A partial JobPost object with extracted fields.
 */
export function adaptJobSectionsToJobPost(
  structured: ResumeSections,
  metadata: SegmentedJobText['metadata'] // Change type here
): Partial<JobDescPost> {
  const jobPost: Partial<JobDescPost> = {};

  // Helper to safely get string value from structured section
  const getStringValue = (section: any) => {
    if (typeof section === "string") return section;
    if (Array.isArray(section) && section.length > 0) return String(section[0]);
    return undefined;
  };

  // Extract from structured sections
  jobPost.jobTitle = metadata.jobTitle || getStringValue(structured.JobTitle);
  jobPost.employmentType = metadata.employmentType || getStringValue(structured.EmploymentType);
  jobPost.experienceLevel = metadata.experienceLevel || getStringValue(structured.ExperienceLevel);

  // Prioritize country from metadata if available, otherwise from structured
  jobPost.country = metadata.country || getStringValue(structured.Country);

  jobPost.location = metadata.location || getStringValue(structured.Location);
  jobPost.department = metadata.department || getStringValue(structured.Department);

  // Salary parsing: This is a simplified example and might need more robust logic
  // based on the actual LLM output format for salary.
  // Prioritize salary from metadata if available
  if (metadata.salaryFrom !== undefined && metadata.salaryTo !== undefined) { // Now correctly typed as number | undefined
    jobPost.salaryCurrency = metadata.salaryCurrency; // This could still be string | undefined
    jobPost.salaryFrom = metadata.salaryFrom;
    jobPost.salaryTo = metadata.salaryTo;
  } else {
    const salaryString = getStringValue(structured.Salary);
    if (salaryString) {
      const match = salaryString.match(/([A-Z]{3})\s*(\d+)-(\d+)/); // e.g., "USD 50000-70000"
      if (match) {
        jobPost.salaryCurrency = match[1];
        jobPost.salaryFrom = parseInt(match[2], 10);
        jobPost.salaryFrom = isNaN(jobPost.salaryFrom) ? 0 : jobPost.salaryFrom; // Ensure it's a number
        jobPost.salaryTo = parseInt(match[3], 10);
        jobPost.salaryTo = isNaN(jobPost.salaryTo) ? 0 : jobPost.salaryTo; // Ensure it's a number
      } else {
        // Attempt to parse single number if no range, or just currency
        const singleMatch = salaryString.match(/([A-Z]{3})?\s*(\d+)/);
        if (singleMatch) {
          if (singleMatch[1]) jobPost.salaryCurrency = singleMatch[1];
          jobPost.salaryFrom = parseInt(singleMatch[2], 10);
          jobPost.salaryFrom = isNaN(jobPost.salaryFrom) ? 0 : jobPost.salaryFrom; // Ensure it's a number
          jobPost.salaryTo = parseInt(singleMatch[2], 10); // Assume single value is both from and to
          jobPost.salaryTo = isNaN(jobPost.salaryTo) ? 0 : jobPost.salaryTo; // Ensure it's a number
        }
      }
    }
  }

  // Job Description: Prioritize 'JobDescription' then 'General'
  jobPost.jobDescription = getStringValue(structured.JobDescription) || getStringValue(structured.General);

  // Skills, LanguageRequirements, Tags: assuming they are string arrays
  const getArrayValue = (section: any) => {
    if (Array.isArray(section)) return section.map(String);
    if (typeof section === 'string') return section.split(',').map(s => s.trim()).filter(s => s.length > 0); // Return empty array if no value or not parsable
  };

  jobPost.skills = getArrayValue(structured.Skills) || [];
  jobPost.languageRequirements = getArrayValue(structured.LanguageRequirements) || [];
  jobPost.tags = getArrayValue(structured.Tags) || [];

  jobPost.interviewType = metadata.interviewType || getStringValue(structured.InterviewType);

  // Remote work flags: assuming a 'RemoteWork' field that might be 'Local', 'Overseas', 'Both', 'None'
  if (metadata.localRemoteWork !== undefined && metadata.overseasRemoteWork !== undefined) {
    jobPost.localRemoteWork = metadata.localRemoteWork;
    jobPost.overseasRemoteWork = metadata.overseasRemoteWork;
  } else {
    const remoteWorkValue = getStringValue(structured.RemoteWork);
    if (remoteWorkValue) {
      const remoteWork = remoteWorkValue.toLowerCase();
      jobPost.localRemoteWork = remoteWork.includes('local') || remoteWork.includes('both');
      jobPost.overseasRemoteWork = remoteWork.includes('overseas') || remoteWork.includes('both');
    } else {
      jobPost.localRemoteWork = false;
      jobPost.overseasRemoteWork = false;
    }
  }

  // Default values for fields that might not be extracted or are fixed
  jobPost.listingDuration = jobPost.listingDuration || 30; // Example default
  jobPost.status = jobPost.status || "ACTIVE"; // Example default

  return jobPost;
}