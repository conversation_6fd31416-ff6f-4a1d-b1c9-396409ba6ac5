{"version": 3, "file": "send-beacon-transport.js", "sourceRoot": "", "sources": ["../../../src/transport/send-beacon-transport.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAIH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAU1C;IACE,6BAAoB,OAA6B;QAA7B,YAAO,GAAP,OAAO,CAAsB;IAAG,CAAC;IACrD,kCAAI,GAAJ,UAAK,IAAgB;QAArB,iBAoBC;QAnBC,OAAO,IAAI,OAAO,CAAiB,UAAA,OAAO;YACxC,IACE,SAAS,CAAC,UAAU,CAClB,KAAI,CAAC,OAAO,CAAC,GAAG,EAChB,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,EAAE,KAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC,CAClD,EACD;gBACA,sDAAsD;gBACtD,IAAI,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;gBACjC,OAAO,CAAC;oBACN,MAAM,EAAE,SAAS;iBAClB,CAAC,CAAC;aACJ;iBAAM;gBACL,OAAO,CAAC;oBACN,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,IAAI,KAAK,CAAC,mBAAmB,CAAC;iBACtC,CAAC,CAAC;aACJ;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,sCAAQ,GAAR;QACE,2CAA2C;IAC7C,CAAC;IACH,0BAAC;AAAD,CAAC,AA3BD,IA2BC;AAED,MAAM,UAAU,yBAAyB,CACvC,UAAgC;IAEhC,OAAO,IAAI,mBAAmB,CAAC,UAAU,CAAC,CAAC;AAC7C,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IExporterTransport } from '../exporter-transport';\nimport { ExportResponse } from '../export-response';\nimport { diag } from '@opentelemetry/api';\n\nexport interface SendBeaconParameters {\n  url: string;\n  /**\n   * for instance 'application/x-protobuf'\n   */\n  blobType: string;\n}\n\nclass SendBeaconTransport implements IExporterTransport {\n  constructor(private _params: SendBeaconParameters) {}\n  send(data: Uint8Array): Promise<ExportResponse> {\n    return new Promise<ExportResponse>(resolve => {\n      if (\n        navigator.sendBeacon(\n          this._params.url,\n          new Blob([data], { type: this._params.blobType })\n        )\n      ) {\n        // no way to signal retry, treat everything as success\n        diag.debug('SendBeacon success');\n        resolve({\n          status: 'success',\n        });\n      } else {\n        resolve({\n          status: 'failure',\n          error: new Error('SendBeacon failed'),\n        });\n      }\n    });\n  }\n\n  shutdown(): void {\n    // Intentionally left empty, nothing to do.\n  }\n}\n\nexport function createSendBeaconTransport(\n  parameters: SendBeaconParameters\n): IExporterTransport {\n  return new SendBeaconTransport(parameters);\n}\n"]}