version: '3.8'

services:
  redis:
    image: redis:7-alpine
    container_name: inngest_redis_prod
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  inngest:
    build:
      context: .
      dockerfile: ./inngest-runtime/Dockerfile
    container_name: inngest_athenafunctions_prod
    restart: unless-stopped
    ports:
      - "3000:3000"  # Next.js app (athenafunctions)
      - "8288:8288"  # Inngest runtime
    depends_on:
      redis:
        condition: service_healthy
    environment:
      REDIS_URL: redis://redis:6379
      NODE_ENV: production
      # Production optimizations
      INNGEST_LOG_LEVEL: info
      INNGEST_CONCURRENCY: 10
    env_file:
      - .env.production
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    volumes:
      - app_logs:/app/logs

volumes:
  redis_data:
    driver: local
  app_logs:
    driver: local
