!function(){"use strict";function n(n,e,o,i,u){return t(e,r(n,e),o,i,u)}function t(n,t,r,e,o,i){const u=E(t,r,e);if(o&&t!==u)throw new RangeError(ii(n,t,r,e,i));return u}function r(n,t){const r=n[t];if(void 0===r)throw new TypeError(ci(t));return r}function e(n){return null!==n&&/object|function/.test(typeof n)}function o(n,t=Map){const r=new t;return(t,...e)=>{if(r.has(t))return r.get(t);const o=n(t,...e);return r.set(t,o),o}}function i(n){return u({name:n},1)}function u(n,t){return f((n=>({value:n,configurable:1,writable:!t})),n)}function c(n){return f((n=>({get:n,configurable:1})),n)}function s(n){return{[Symbol.toStringTag]:{value:n,configurable:1}}}function a(n,t){const r={};let e=n.length;for(const o of t)r[n[--e]]=o;return r}function f(n,t,r){const e={};for(const o in t)e[o]=n(t[o],o,r);return e}function h(n,t,r){const e={};for(let o=0;o<t.length;o++){const i=t[o];e[i]=n(i,o,r)}return e}function l(n,t){const r={};for(const e of n)r[e]=t[e];return r}function d(n,t){const r={};for(const e in t)n.has(e)||(r[e]=t[e]);return r}function w(n){n={...n};const t=Object.keys(n);for(const r of t)void 0===n[r]&&delete n[r];return n}function g(n,t,r){for(const e of n)if(t[e]!==r[e])return 0;return 1}function m(n,t,r){const e={...r};for(let r=0;r<t;r++)e[n[r]]=0;return e}function y(n,...t){return(...r)=>n(...t,...r)}function v(n){return n[0].toUpperCase()+n.substring(1)}function b(n){return n.slice().sort()}function M(n,t){return String(t).padStart(n,"0")}function p(n,t){return Math.sign(n-t)}function E(n,t,r){return Math.min(Math.max(n,t),r)}function O(n,t){return[Math.floor(n/t),I(n,t)]}function I(n,t){return(n%t+t)%t}function T(n,t){return[R(n,t),S(n,t)]}function R(n,t){return Math.trunc(n/t)||0}function S(n,t){return n%t||0}function D(n){return.5===Math.abs(n%1)}function P(n,t,r){let e=0,o=0;for(let i=0;i<=t;i++){const t=n[r[i]],u=tu[i],c=nu/u,[s,a]=T(t,c);e+=a*u,o+=s}const[i,u]=T(e,nu);return[o+i,u]}function j(n,t,r){const e={};for(let o=t;o>=0;o--){const t=tu[o];e[r[o]]=R(n,t),n=S(n,t)}return e}function N(n,t){let[r,e]=T(t,nu),o=n+r;const i=Math.sign(o);return i&&i===-Math.sign(e)&&(o-=i,e+=i*nu),[o,e]}function Y(n,t,r=1){return N(n[0]+t[0]*r,n[1]+t[1]*r)}function F(n,t){return N(n[0],n[1]+t)}function C(n,t){return Y(t,n,-1)}function Z(n,t){return p(n[0],t[0])||p(n[1],t[1])}function $(n,t,r){return-1===Z(n,t)||1===Z(n,r)}function k(n,t=1){const r=BigInt(nu/t);return[Number(n/r),Number(n%r)*t]}function L(n,t=1){const r=nu/t,[e,o]=T(n,r);return[e,o*t]}function A(n,t=1){const[r,e]=n,o=Math.floor(e/t),i=nu/t;return BigInt(r)*BigInt(i)+BigInt(o)}function W(n,t=1,r){const[e,o]=n,[i,u]=T(o,t);return e*(nu/t)+(i+(r?u/t:0))}function B(n,t,r=O){const[e,o]=n,[i,u]=r(o,t);return[e*(nu/t)+i,u]}function U(n){if(void 0!==n)return J(n)}function q(n){return V(J(n))}function J(n){return G(ou(n))}function x(n){if(null==n)throw new TypeError("Cannot be null or undefined");return n}function H(n,t){if(null==t)throw new RangeError(ci(n));return t}function z(n){if(!e(n))throw new TypeError(oi);return n}function _(n,t,r=n){if(typeof t!==n)throw new TypeError(ui(r,t));return t}function G(n,t="number"){if(!Number.isInteger(n))throw new RangeError(Qo(t,n));return n||0}function V(n,t="number"){if(n<=0)throw new RangeError(Xo(t,n));return n}function K(n){if("symbol"==typeof n)throw new TypeError(ei);return String(n)}function Q(n,t){return e(n)?String(n):ru(n,t)}function X(n){if("string"==typeof n)return BigInt(n);if("bigint"!=typeof n)throw new TypeError(ri(n));return n}function nn(n,t="number"){if("bigint"==typeof n)throw new TypeError(ti(t));if(n=Number(n),!Number.isFinite(n))throw new RangeError(ni(t,n));return n}function tn(n,t){return Math.trunc(nn(n,t))||0}function rn(n,t){return G(nn(n,t),t)}function en(n,t){return V(tn(n,t),t)}function on(n,t){const r=n.formatToParts(t),e={};for(const n of r)e[n.type]=n.value;return e}function un(t){return n(t,"isoYear",Xu,Qu,1),t.isoYear===Xu?n(t,"isoMonth",4,12,1):t.isoYear===Qu&&n(t,"isoMonth",1,9,1),t}function cn(n){return sn({...n,...Hu,isoHour:12}),n}function sn(t){const r=n(t,"isoYear",Xu,Qu,1),e=r===Xu?1:r===Qu?-1:0;return e&&an(gn({...t,isoDay:t.isoDay+e,isoNanosecond:t.isoNanosecond-e})),t}function an(n){if(!n||$(n,Ku,Vu))throw new RangeError(Yi);return n}function fn(n){return P(n,5,Wu)[1]}function hn(n){const[t,r]=O(n,nu);return[j(r,5,Wu),t]}function ln(n){return dn(n)[0]}function dn(n){return B(n,Ki)}function wn(n){return vn(n.isoYear,n.isoMonth,n.isoDay,n.isoHour,n.isoMinute,n.isoSecond,n.isoMillisecond)}function gn(n){const t=wn(n);if(void 0!==t){const[r,e]=T(t,zi);return[r,e*Vi+(n.isoMicrosecond||0)*Gi+(n.isoNanosecond||0)]}}function mn(n,t){const[r,e]=hn(fn(n)-t);return an(gn({...n,isoDay:n.isoDay+e,...r}))}function yn(...n){return vn(...n)/_i}function vn(...n){const[t,r]=bn(...n),e=t.valueOf();if(!isNaN(e))return e-r*zi}function bn(n,t=1,r=1,e=0,o=0,i=0,u=0){const c=n===Xu?1:n===Qu?-1:0,s=new Date;return s.setUTCHours(e,o,i,u),s.setUTCFullYear(n,t-1,r+c),[s,c]}function Mn(n,t){let[r,e]=F(n,t);e<0&&(e+=nu,r-=1);const[o,i]=O(e,Vi),[u,c]=O(i,Gi);return pn(r*zi+o,u,c)}function pn(n,t=0,r=0){const e=Math.ceil(Math.max(0,Math.abs(n)-Gu)/zi)*Math.sign(n),o=new Date(n-e*zi);return a(Uu,[o.getUTCFullYear(),o.getUTCMonth()+1,o.getUTCDate()+e,o.getUTCHours(),o.getUTCMinutes(),o.getUTCSeconds(),o.getUTCMilliseconds(),t,r])}function En(n){return[n.isoYear,n.isoMonth,n.isoDay]}function On(){return rc}function In(n,t){switch(t){case 2:return Rn(n)?29:28;case 4:case 6:case 9:case 11:return 30}return 31}function Tn(n){return Rn(n)?366:365}function Rn(n){return n%4==0&&(n%100!=0||n%400==0)}function Sn(n){const[t,r]=bn(n.isoYear,n.isoMonth,n.isoDay);return I(t.getUTCDay()-r,7)||7}function Dn({isoYear:n}){return n<1?["bce",1-n]:["ce",n]}function Pn(n){return jn(n),Fn(n,1),n}function jn(n){return Yn(n,1),n}function Nn(n){return g(Bu,n,Yn(n))}function Yn(t,r){const{isoYear:e}=t,o=n(t,"isoMonth",1,On(),r);return{isoYear:e,isoMonth:o,isoDay:n(t,"isoDay",1,In(e,o),r)}}function Fn(t,r){return a(Wu,[n(t,"isoHour",0,23,r),n(t,"isoMinute",0,59,r),n(t,"isoSecond",0,59,r),n(t,"isoMillisecond",0,999,r),n(t,"isoMicrosecond",0,999,r),n(t,"isoNanosecond",0,999,r)])}function Cn(n){return o((t=>{const r=wn(t);return n(r)}),WeakMap)}function Zn(n){const t=n(0).year-nc;return o((r=>{let e,o=vn(r-t);const i=[],u=[];do{o+=400*zi}while((e=n(o)).year<=r);do{o+=(1-e.day)*zi,e.year===r&&(i.push(o),u.push(e.F)),o-=zi}while((e=n(o)).year>=r);return{k:i.reverse(),C:Ui(u.reverse())}}))}function $n(n,t){let r,e,o=kn(n);if(n.era){const i=$u[t];void 0!==i&&(r="islamic"===t?"ah":n.era.normalize("NFD").toLowerCase().replace(/[^a-z0-9]/g,""),"bc"===r||"b"===r?r="bce":"ad"!==r&&"a"!==r||(r="ce"),e=o,o=Xn(e,i[r]||0))}return{era:r,eraYear:e,year:o}}function kn(n){return parseInt(n.relatedYear||n.year)}function Ln(n){const{year:t,F:r,day:e}=this.O(n),{C:o}=this.B(t);return[t,o[r]+1,e]}function An(n,t=1,r=1){return this.B(n).k[t-1]+(r-1)*zi}function Wn(n){const t=Jn(this,n),r=Jn(this,n-1),e=t.length;if(e>r.length){const n=tt(this);if(n<0)return-n;for(let n=0;n<e;n++)if(t[n]!==r[n])return n+1}}function Bn(n){return Vr(An.call(this,n),An.call(this,n+1))}function Un(n,t){const{k:r}=this.B(n);let e=t+1,o=r;return e>r.length&&(e=1,o=this.B(n+1).k),Vr(r[t-1],o[e-1])}function qn(n){return this.B(n).k.length}function Jn(n,t){return Object.keys(n.B(t).C)}function xn(n){if((n=n.toLowerCase())!==Fu&&n!==Cu&&Hn(n)!==Hn(fc(n).resolvedOptions().calendar))throw new RangeError(Ti(n));return n}function Hn(n){return"islamicc"===n&&(n="islamic"),n.split("-")[0]}function zn(n){return this.R(n)[0]}function _n(n){return this.R(n)[1]}function Gn(n){const[t]=this.h(n);return Vr(this.q(t),wn(n))+1}function Vn(n){const t=hc.exec(n);if(!t)throw new RangeError(vi(n));return[parseInt(t[1]),Boolean(t[2])]}function Kn(n,t,r){return n+(t||r&&n>=r?1:0)}function Qn(n,t){return n-(t&&n>=t?1:0)}function Xn(n,t){return(t+n)*(Math.sign(t)||1)||0}function nt(n){return $u[rt(n)]}function tt(n){return ku[rt(n)]}function rt(n){return Hn(n.id||Fu)}function et(n){return void 0===n?0:Ec(z(n))}function ot(n,t=0){n=gt(n);const r=Oc(n),e=Ic(n,t);return[Ec(n),e,r]}function it(n){return Oc(gt(n))}function ut(n,t,r,e=9,o=0,i=4){t=gt(t);let u=Mc(t,e,o),c=lt(t),s=Dc(t,i);const a=bc(t,e,o,1);return null==u?u=Math.max(r,a):pt(u,a),c=dt(c,a,1),n&&(s=(n=>n<4?(n+2)%4:n)(s)),[u,a,c,s]}function ct(n,t=6,r){let e=lt(n=mt(n,lc));const o=Dc(n,7);let i=bc(n,t);return i=H(lc,i),e=dt(e,i,void 0,r),[i,e,o]}function st(n){return Tc(gt(n))}function at(n,t){return ft(gt(n),t)}function ft(n,t=4){const r=wt(n);return[Dc(n,4),...ht(bc(n,t),r)]}function ht(n,t){return null!=n?[tu[n],n<4?9-3*n:-1]:[void 0===t?1:10**(9-t),t]}function lt(n){const t=n[wc];return void 0===t?1:tn(t,wc)}function dt(n,r,e,o){const i=o?nu:tu[r+1];if(i){const e=tu[r];if(i%((n=t(wc,n,1,i/e-(o?0:1),1))*e))throw new RangeError(ui(wc,n))}else n=t(wc,n,1,e?10**9:1,1);return n}function wt(n){let r=n[gc];if(void 0!==r){if("number"!=typeof r){if("auto"===K(r))return;throw new RangeError(ui(gc,r))}r=t(gc,Math.floor(r),0,9,1)}return r}function gt(n){return void 0===n?{}:z(n)}function mt(n,t){return"string"==typeof n?{[t]:n}:z(n)}function yt(n){if(void 0!==n){if(e(n))return Object.assign(Object.create(null),n);throw new TypeError(oi)}}function vt(n,t){return n&&Object.assign(Object.create(null),n,{overflow:vc[t]})}function bt(n,r,e=9,o=0,i){let u=r[n];if(void 0===u)return i?o:void 0;if(u=K(u),"auto"===u)return i?o:null;let c=xi[u];if(void 0===c&&(c=hu[u]),void 0===c)throw new RangeError(hi(n,u,xi));return t(n,c,o,e,1,Hi),c}function Mt(n,t,r,e=0){const o=r[n];if(void 0===o)return e;const i=K(o),u=t[i];if(void 0===u)throw new RangeError(hi(n,i,t));return u}function pt(n,t){if(t>n)throw new RangeError(ki)}function Et(n){return{branding:Zc,epochNanoseconds:n}}function Ot(n,t,r){return{branding:Cc,calendar:r,timeZone:t,epochNanoseconds:n}}function It(n,t=n.calendar){return{branding:Yc,calendar:t,...l(xu,n)}}function Tt(n,t=n.calendar){return{branding:Nc,calendar:t,...l(qu,n)}}function Rt(n,t=n.calendar){return{branding:Pc,calendar:t,...l(qu,n)}}function St(n,t=n.calendar){return{branding:jc,calendar:t,...l(qu,n)}}function Dt(n){return{branding:Fc,...l(Ju,n)}}function Pt(n){return{branding:$c,sign:se(n),...l(cu,n)}}function jt(n){return B(n.epochNanoseconds,Vi)[0]}function Nt(n){return n.epochNanoseconds}function Yt(n){return"string"==typeof n?n:ru(n.id)}function Ft(n,t){return n===t||Yt(n)===Yt(t)}function Ct(n,t){return W(he(n),tu[t],1)}function Zt(n,t,r,e,o,i,u){const c=uu[r],s={...t,[c]:t[c]+e},a=u(n,o,t),f=u(n,o,s);return[i(a),i(f)]}function $t(n,t,r){const e=W(C(t,r));if(!e)throw new RangeError(Oi);return W(C(t,n))/e}function kt(n,t,r,e){return Lt(n,Bt(t,r),e)}function Lt(n,t,r){const[e,o]=At(n,t,r);return sn({...Zr(n,o),...e})}function At(n,t,r){return hn(_t(fn(n),t,r))}function Wt(n){return _t(n,Qi,7)}function Bt(n,t){return tu[n]*t}function Ut(n){const t=qt(n);return[t,Zr(t,1)]}function qt(n){return zu(6,n)}function Jt(n,t,r){const e=Math.min(ge(n),6);return le(zt(he(n,e),t,r),e)}function xt(n,t,r,e,o,i,u,c,s,a){if(0===e&&1===o)return n;const f=oe(e,c)?ee(c)&&e<6&&r>=6?Kt:Vt:Qt;let[h,l,d]=f(n,t,r,e,o,i,u,c,s,a);return d&&7!==e&&(h=((n,t,r,e,o,i,u,c)=>{const s=se(n);for(let a=e+1;a<=r;a++){if(7===a&&7!==r)continue;const e=wu(a,n);e[uu[a]]+=s;const f=W(C(u(c(o,i,e)),t));if(f&&Math.sign(f)!==s)break;n=e}return n})(h,l,r,Math.max(6,e),u,c,s,a)),h}function Ht(n,t,r,e,o){if(6===t){const t=(n=>n[0]+n[1]/nu)(n);return[_t(t,r,e),0]}return zt(n,Bt(t,r),e,o)}function zt(n,t,r,e){let[o,i]=n;e&&i<0&&(i+=nu,o-=1);const[u,c]=O(_t(i,t,r),nu);return N(o+u,c)}function _t(n,t,r){return Gt(n/t,r)*t}function Gt(n,t){return kc[t](n)}function Vt(n,t,r,e,o,i){const u=se(n),c=he(n),s=Ht(c,e,o,i),a=C(c,s),f=Math.sign(s[0]-c[0])===u,h=le(s,Math.min(r,6));return[{...n,...h},Y(t,a),f]}function Kt(n,t,r,e,o,i,u,c,s,a){const f=se(n),h=W(he(n,5)),l=Bt(e,o);let d=_t(h,l,i);const[w,g]=Zt(u,{...n,...du},6,f,c,s,a),m=d-W(C(w,g));let y=0;m&&Math.sign(m)!==f?t=F(w,d):(y+=f,d=_t(m,l,i),t=F(g,d));const v=de(d);return[{...n,...v,days:n.days+y},t,Boolean(y)]}function Qt(n,t,r,e,o,i,u,c,s,a){const f=se(n),h=uu[e],l=wu(e,n);7===e&&(n={...n,weeks:n.weeks+Math.trunc(n.days/7)});const d=R(n[h],o)*o;l[h]=d;const[w,g]=Zt(u,l,e,o*f,c,s,a),m=d+$t(t,w,g)*f*o,y=_t(m,o,i),v=Math.sign(y-m)===f;return l[h]=y,[l,v?g:w,v]}function Xt(n,t,r,e){const[o,i,u,c]=(n=>{const t=ft(n=gt(n));return[n.timeZone,...t]})(e),s=void 0!==o;return((n,t,r,e,o,i)=>{r=zt(r,o,e,1);const u=t.getOffsetNanosecondsFor(r);return ar(Mn(r,u),i)+(n?wr(Wt(u)):"Z")})(s,t(s?n(o):ic),r.epochNanoseconds,i,u,c)}function nr(n,t,r){const[e,o,i,u,c,s]=(n=>{n=gt(n);const t=Tc(n),r=wt(n),e=Sc(n),o=Dc(n,4),i=bc(n,4);return[t,Rc(n),e,o,...ht(i,r)]})(r);return((n,t,r,e,o,i,u,c,s,a)=>{e=zt(e,s,c,1);const f=n(r).getOffsetNanosecondsFor(e);return ar(Mn(e,f),a)+wr(Wt(f),u)+((n,t)=>1!==t?"["+(2===t?"!":"")+Yt(n)+"]":"")(r,i)+gr(t,o)})(n,t.calendar,t.timeZone,t.epochNanoseconds,e,o,i,u,c,s)}function tr(n,t){const[r,e,o,i]=(n=>(n=gt(n),[Tc(n),...ft(n)]))(t);return u=n.calendar,c=r,s=i,ar(Lt(n,o,e),s)+gr(u,c);var u,c,s}function rr(n,t){return r=n.calendar,e=n,o=st(t),fr(e)+gr(r,o);var r,e,o}function er(n,t){return cr(n.calendar,hr,n,st(t))}function or(n,t){return cr(n.calendar,lr,n,st(t))}function ir(n,t){const[r,e,o]=at(t);return i=o,dr(At(n,e,r)[0],i);var i}function ur(n,t){const[r,e,o]=at(t,3);return e>1&&(n={...n,...Jt(n,e,r)}),((n,t)=>{const{sign:r}=n,e=-1===r?ce(n):n,{hours:o,minutes:i}=e,[u,c]=B(he(e,3),Ki,T);fe(u);const s=yr(c,t),a=t>=0||!r||s;return(r<0?"-":"")+"P"+sr({Y:br(e.years),M:br(e.months),W:br(e.weeks),D:br(e.days)})+(o||i||u||a?"T"+sr({H:br(o),M:br(i),S:br(u,a)+s}):"")})(n,o)}function cr(n,t,r,e){const o=Yt(n),i=e>1||0===e&&o!==Fu;return 1===e?o===Fu?t(r):fr(r):i?fr(r)+mr(o,2===e):t(r)}function sr(n){const t=[];for(const r in n){const e=n[r];e&&t.push(e,r)}return t.join("")}function ar(n,t){return fr(n)+"T"+dr(n,t)}function fr(n){return hr(n)+"-"+Ji(n.isoDay)}function hr(n){const{isoYear:t}=n;return(t<0||t>9999?vr(t)+M(6,Math.abs(t)):M(4,t))+"-"+Ji(n.isoMonth)}function lr(n){return Ji(n.isoMonth)+"-"+Ji(n.isoDay)}function dr(n,t){const r=[Ji(n.isoHour),Ji(n.isoMinute)];return-1!==t&&r.push(Ji(n.isoSecond)+((n,t,r,e)=>yr(n*Vi+t*Gi+r,e))(n.isoMillisecond,n.isoMicrosecond,n.isoNanosecond,t)),r.join(":")}function wr(n,t=0){if(1===t)return"";const[r,e]=O(Math.abs(n),Xi),[o,i]=O(e,Qi),[u,c]=O(i,Ki);return vr(n)+Ji(r)+":"+Ji(o)+(u||c?":"+Ji(u)+yr(c):"")}function gr(n,t){if(1!==t){const r=Yt(n);if(t>1||0===t&&r!==Fu)return mr(r,2===t)}return""}function mr(n,t){return"["+(t?"!":"")+"u-ca="+n+"]"}function yr(n,t){let r=M(9,n);return r=void 0===t?r.replace(Lc,""):r.slice(0,t),r?"."+r:""}function vr(n){return n<0?"-":"+"}function br(n,t){return n||t?n.toLocaleString("fullwide",{useGrouping:0}):""}function Mr(n,t,r,e=0,o=0,i,u){if(void 0!==r&&1===e&&(1===e||u))return mn(t,r);const c=n.getPossibleInstantsFor(t);if(void 0!==r&&3!==e){const n=((n,t,r,e)=>{const o=gn(t);e&&(r=Wt(r));for(const t of n){let n=W(C(t,o));if(e&&(n=Wt(n)),n===r)return t}})(c,t,r,i);if(void 0!==n)return n;if(0===e)throw new RangeError(ji)}return u?gn(t):pr(n,t,o,c)}function pr(n,t,r=0,e=n.getPossibleInstantsFor(t)){if(1===e.length)return e[0];if(1===r)throw new RangeError(Ni);if(e.length)return e[3===r?1:0];const o=gn(t),i=((n,t)=>{const r=n.getOffsetNanosecondsFor(F(t,-nu));return Or(n.getOffsetNanosecondsFor(F(t,nu))-r)})(n,o),u=i*(2===r?-1:1);return(e=n.getPossibleInstantsFor(Mn(o,u)))[2===r?0:e.length-1]}function Er(n){if(Math.abs(n)>=nu)throw new RangeError(Di);return n}function Or(n){if(n>nu)throw new RangeError(Pi);return n}function Ir(n,t,r){return Et(an(Y(t.epochNanoseconds,(n=>{if(we(n))throw new RangeError($i);return he(n,5)})(n?ce(r):r))))}function Tr(n,t,r,e,o,i=Object.create(null)){const u=t(e.timeZone),c=n(e.calendar);return{...e,...jr(u,c,e,r?ce(o):o,i)}}function Rr(n,t,r,e,o=Object.create(null)){const{calendar:i}=r;return It(Nr(n(i),r,t?ce(e):e,o),i)}function Sr(n,t,r,e,o){const{calendar:i}=r;return Tt(Yr(n(i),r,t?ce(e):e,o),i)}function Dr(n,t,r,e,o=Object.create(null)){const i=r.calendar,u=n(i);let c=Fr(u,r);t&&(e=ue(e)),e.sign<0&&(c=u.dateAdd(c,{...lu,months:1}),c=Zr(c,-1));const s=u.dateAdd(c,e,o);return Rt(Fr(u,s),i)}function Pr(n,t,r){return Dt(Cr(t,n?ce(r):r)[0])}function jr(n,t,r,e,o){const i=he(e,5);let u=r.epochNanoseconds;if(we(e)){const c=Ac(r,n);u=Y(pr(n,{...Yr(t,c,{...e,...du},o),...l(Wu,c)}),i)}else u=Y(u,i),et(o);return{epochNanoseconds:an(u)}}function Nr(n,t,r,e){const[o,i]=Cr(t,r);return sn({...Yr(n,t,{...r,...du,days:r.days+i},e),...o})}function Yr(n,t,r,e){if(r.years||r.months||r.weeks)return n.dateAdd(t,r,e);et(e);const o=r.days+he(r,5)[0];return o?cn(Zr(t,o)):t}function Fr(n,t,r=1){return Zr(t,r-n.day(t))}function Cr(n,t){const[r,e]=he(t,5),[o,i]=hn(fn(n)+e);return[o,r+i]}function Zr(n,t){return t?{...n,...pn(wn(n)+t*zi)}:n}function $r(n,t,r,e){const o=ut(n,yt(e),3,5),i=zr(t.epochNanoseconds,r.epochNanoseconds,...o);return Pt(n?ce(i):i)}function kr(n,t,r,e,o,i){const u=Qr(e.calendar,o.calendar),c=yt(i),[s,a,f,h]=ut(r,c,5),l=e.epochNanoseconds,d=o.epochNanoseconds,w=Z(d,l);let g;if(w)if(s<6)g=zr(l,d,s,a,f,h);else{const r=t(((n,t)=>{if(!Je(n,t))throw new RangeError(Ri);return n})(e.timeZone,o.timeZone)),i=n(u);g=xr(i,r,e,o,w,s,c),g=xt(g,d,s,a,f,h,i,e,Nt,y(jr,r))}else g=lu;return Pt(r?ce(g):g)}function Lr(n,t,r,e,o){const i=Qr(r.calendar,e.calendar),u=yt(o),[c,s,a,f]=ut(t,u,6),h=gn(r),l=gn(e),d=Z(l,h);let w;if(d)if(c<=6)w=zr(h,l,c,s,a,f);else{const t=n(i);w=Hr(t,r,e,d,c,u),w=xt(w,l,c,s,a,f,t,r,gn,Nr)}else w=lu;return Pt(t?ce(w):w)}function Ar(n,t,r,e,o){const i=Qr(r.calendar,e.calendar),u=yt(o);return Br(t,(()=>n(i)),r,e,...ut(t,u,6,9,6),u)}function Wr(n,t,r,e,o){const i=Qr(r.calendar,e.calendar),u=yt(o),c=ut(t,u,9,9,8),s=n(i);return Br(t,(()=>s),Fr(s,r),Fr(s,e),...c,u)}function Br(n,t,r,e,o,i,u,c,s){const a=gn(r),f=gn(e);let h;if(Z(f,a))if(6===o)h=zr(a,f,o,i,u,c);else{const n=t();h=n.dateUntil(r,e,o,s),6===i&&1===u||(h=xt(h,f,o,i,u,c,n,r,gn,Yr))}else h=lu;return Pt(n?ce(h):h)}function Ur(n,t,r,e){const o=yt(e),[i,u,c,s]=ut(n,o,5,5),a=_t(Kr(t,r),Bt(u,c),s),f={...lu,...de(a,i)};return Pt(n?ce(f):f)}function qr(n,t,r,e,o,i){const u=Z(e.epochNanoseconds,r.epochNanoseconds);return u?o<6?_r(r.epochNanoseconds,e.epochNanoseconds,o):xr(t,n,r,e,u,o,i):lu}function Jr(n,t,r,e,o){const i=gn(t),u=gn(r),c=Z(u,i);return c?e<=6?_r(i,u,e):Hr(n,t,r,c,e,o):lu}function xr(n,t,r,e,o,i,u){const[c,s,a]=((n,t,r,e)=>{function o(){return h={...Zr(c,a++*-e),...u},d=pr(n,h),Z(s,d)===-e}const i=Ac(t,n),u=l(Wu,i),c=Ac(r,n),s=r.epochNanoseconds;let a=0;const f=Kr(i,c);let h,d;if(Math.sign(f)===-e&&a++,o()&&(-1===e||o()))throw new RangeError(Oi);const w=W(C(d,s));return[i,h,w]})(t,r,e,o);var f,h;return{...6===i?(f=c,h=s,{...lu,days:Gr(f,h)}):n.dateUntil(c,s,i,u),...de(a)}}function Hr(n,t,r,e,o,i){const[u,c,s]=((n,t,r)=>{let e=t,o=Kr(n,t);return Math.sign(o)===-r&&(e=Zr(t,-r),o+=nu*r),[n,e,o]})(t,r,e);return{...n.dateUntil(u,c,o,i),...de(s)}}function zr(n,t,r,e,o,i){return{...lu,...le(Ht(C(n,t),e,o,i),r)}}function _r(n,t,r){return{...lu,...le(C(n,t),r)}}function Gr(n,t){return Vr(wn(n),wn(t))}function Vr(n,t){return Math.trunc((t-n)/zi)}function Kr(n,t){return fn(t)-fn(n)}function Qr(n,t){if(!Ft(n,t))throw new RangeError(Ii);return n}function Xr(n,t,r){const e=n(r.calendar);return ee(r)?[r,e,t(r.timeZone)]:[{...r,...Hu},e]}function ne(n){return n?Nt:gn}function te(n){return n?y(jr,n):Nr}function re(n){return n?y(qr,n):Jr}function ee(n){return n&&n.epochNanoseconds}function oe(n,t){return n<=6-(ee(t)?1:0)}function ie(n,t,r,e,o,i,u){const c=n(gt(u).relativeTo),s=Math.max(ge(o),ge(i));if(oe(s,c))return Pt(ae(((n,t,r,e)=>{const o=Y(he(n),he(t),e?-1:1);if(!Number.isFinite(o[0]))throw new RangeError(Yi);return{...lu,...le(o,r)}})(o,i,s,e)));if(!c)throw new RangeError(Zi);e&&(i=ce(i));const[a,f,h]=Xr(t,r,c),l=te(h),d=re(h),w=l(f,a,o);return Pt(d(f,a,l(f,w,i),s))}function ue(n){return Pt(ce(n))}function ce(n){const t={};for(const r of uu)t[r]=-1*n[r]||0;return t}function se(n,t=uu){let r=0;for(const e of t){const t=Math.sign(n[e]);if(t){if(r&&r!==t)throw new RangeError(Ci);r=t}}return r}function ae(n){for(const r of fu)t(r,n[r],-Wc,Wc,1);return fe(W(he(n),Ki)),n}function fe(n){if(!Number.isSafeInteger(n))throw new RangeError(Fi)}function he(n,t=6){return P(n,t,uu)}function le(n,t=6){const[r,e]=n,o=j(e,t,uu);if(o[uu[t]]+=r*(nu/tu[t]),!Number.isFinite(o[uu[t]]))throw new RangeError(Yi);return o}function de(n,t=5){return j(n,t,uu)}function we(n){return Boolean(se(n,au))}function ge(n){let t=9;for(;t>0&&!n[uu[t]];t--);return t}function me(n,t){return[n,t]}function ye(n){const t=Math.floor(n/uc)*uc;return[t,t+uc]}function ve(n){const t=De(n);if(void 0===t)throw new RangeError(Li(n));return t}function be(n){const t=Te(ru(n));if(!t||t.m)throw new RangeError(Li(n));return Tt(t.p?Ee(t):Oe(t))}function Me(n){if(n.calendar!==Fu)throw new RangeError(Ai(n.calendar))}function pe(n,t,r=0,e=0){const o=$e(n.timeZone),i=Bc(o);return Ot(Mr(i,Pn(n),t,r,e,!i.v,n.m),o,xn(n.calendar))}function Ee(n){return Ie(sn(Pn(n)))}function Oe(n){return Ie(cn(jn(n)))}function Ie(n){return{...n,calendar:xn(n.calendar)}}function Te(n){const t=ns.exec(n);return t?(n=>{const t=n[10],r="Z"===(t||"").toUpperCase();return{isoYear:Pe(n),isoMonth:parseInt(n[4]),isoDay:parseInt(n[5]),...je(n.slice(5)),...Ne(n[16]),p:Boolean(n[6]),m:r,offset:r?void 0:t}})(t):void 0}function Re(n){const t=Qc.exec(n);return t?(n=>({isoYear:Pe(n),isoMonth:parseInt(n[4]),isoDay:1,...Ne(n[5])}))(t):void 0}function Se(n){const t=Xc.exec(n);return t?(n=>({isoYear:tc,isoMonth:parseInt(n[1]),isoDay:parseInt(n[2]),...Ne(n[3])}))(t):void 0}function De(n,t){const r=rs.exec(n);return r?((n,t)=>{const r=n[4]||n[5];if(t&&r)throw new RangeError(Ai(r));return Er((Ze(n[2])*Xi+Ze(n[3])*Qi+Ze(n[4])*Ki+Ye(n[5]||""))*Ce(n[1]))})(r,t):void 0}function Pe(n){const t=Ce(n[1]),r=parseInt(n[2]||n[3]);if(t<0&&!r)throw new RangeError(Ai(-0));return t*r}function je(n){const t=Ze(n[3]);return{...hn(Ye(n[4]||""))[0],isoHour:Ze(n[1]),isoMinute:Ze(n[2]),isoSecond:60===t?59:t}}function Ne(n){let t,r;const e=[];if(n.replace(es,((n,o,i)=>{const u=Boolean(o),[c,s]=i.split("=").reverse();if(s){if("u-ca"===s)e.push(c),t||(t=u);else if(u||/[A-Z]/.test(s))throw new RangeError(Ai(n))}else{if(r)throw new RangeError(Ai(n));r=c}return""})),e.length>1&&t)throw new RangeError(Ai(n));return{timeZone:r,calendar:e[0]||Fu}}function Ye(n){return parseInt(n.padEnd(9,"0"))}function Fe(n){return new RegExp(`^${n}$`,"i")}function Ce(n){return n&&"+"!==n?-1:1}function Ze(n){return void 0===n?0:parseInt(n)}function $e(n){const t=Le(n);return"number"==typeof t?wr(t):t?(n=>{if(us.test(n))throw new RangeError(Si);return n.toLowerCase().split("/").map(((n,t)=>(n.length<=3||/\d/.test(n))&&!/etc|yap/.test(n)?n.toUpperCase():n.replace(/baja|dumont|[a-z]+/g,((n,r)=>n.length<=2&&!t||"in"===n||"chat"===n?n.toUpperCase():n.length>2||!r?v(n).replace(/island|noronha|murdo|rivadavia|urville/,v):n)))).join("/")})(n):ic}function ke(n){const t=Le(n);return"number"==typeof t?t:t?t.resolvedOptions().timeZone:ic}function Le(n){const t=De(n=n.toUpperCase(),1);return void 0!==t?t:n!==ic?is(n):void 0}function Ae(n,t){return Z(n.epochNanoseconds,t.epochNanoseconds)}function We(n,t){return Z(n.epochNanoseconds,t.epochNanoseconds)}function Be(n,t){return Ue(n,t)||qe(n,t)}function Ue(n,t){return p(wn(n),wn(t))}function qe(n,t){return p(fn(n),fn(t))}function Je(n,t){if(n===t)return 1;const r=Yt(n),e=Yt(t);if(r===e)return 1;try{return ke(r)===ke(e)}catch(n){}}function xe(n,t,r,e=[]){const o=_e(n,t,Pu,e);return n.dateFromFields(o,r)}function He(n,t,r,e){const o=_e(n,t,Su,e);return n.yearMonthFromFields(o,r)}function ze(n,t,r,e,o=[]){const i=_e(n,r,Pu,o);return t&&void 0!==i.month&&void 0===i.monthCode&&void 0===i.year&&(i.year=tc),n.monthDayFromFields(i,e)}function _e(n,t,r,e=[],o=[]){return Ge(t,[...n.fields(r),...o].sort(),e)}function Ge(n,t,r,e=!r){const o={};let i,u=0;for(const e of t){if(e===i)throw new RangeError(ai(e));if("constructor"===e||"__proto__"===e)throw new RangeError(si(e));let t=n[e];if(void 0!==t)u=1,cs[e]&&(t=cs[e](t,e)),o[e]=t;else if(r){if(r.includes(e))throw new TypeError(ci(e));o[e]=Yu[e]}i=e}if(e&&!u)throw new TypeError(fi(t));return o}function Ve(n,t){return Fn(ss({...Yu,...n}),t)}function Ke(n,t,r,e,o=[],i=[]){const u=[...n.fields(e),...o].sort();let c=Ge(t,u,i);const s=Ge(r,u);return c=n.mergeFields(c,s),Ge(c,u,[])}function Qe(n,t){const r=_e(n,t,Nu);return n.monthDayFromFields(r)}function Xe(n,t,r){const e=_e(n,t,Du);return n.yearMonthFromFields(e,r)}function no(n,t,r,e,o){t=l(r=n.fields(r),t),e=Ge(e,o=n.fields(o),[]);let i=n.mergeFields(t,e);return i=Ge(i,[...r,...o].sort(),[]),n.dateFromFields(i)}function to(n,t){let{era:r,eraYear:e,year:o}=t;const i=nt(n);if(void 0!==r||void 0!==e){if(void 0===r||void 0===e)throw new TypeError(wi);if(!i)throw new RangeError(di);const n=i[r];if(void 0===n)throw new RangeError(mi(r));const t=Xn(e,n);if(void 0!==o&&o!==t)throw new RangeError(gi);o=t}else if(void 0===o)throw new TypeError(yi(i));return o}function ro(n,r,e,o){let{month:i,monthCode:u}=r;if(void 0!==u){const t=((n,t,r,e)=>{const o=n.U(r),[i,u]=Vn(t);let c=Kn(i,u,o);if(u){const t=tt(n);if(void 0===t)throw new RangeError(Ei);if(t>0){if(c>t)throw new RangeError(Ei);if(void 0===o){if(1===e)throw new RangeError(Ei);c--}}else{if(c!==-t)throw new RangeError(Ei);if(void 0===o&&1===e)throw new RangeError(Ei)}}return c})(n,u,e,o);if(void 0!==i&&i!==t)throw new RangeError(bi);i=t,o=1}else if(void 0===i)throw new TypeError(Mi);return t("month",i,1,n.L(e),o)}function eo(t,r,e,o,i){return n(r,"day",1,t.j(o,e),i)}function oo(n,t,r,e){let o=0;const i=[];for(const n of r)void 0!==t[n]?o=1:i.push(n);if(Object.assign(n,t),o)for(const t of e||i)delete n[t]}function io(n,t,r=Fu){return Ot(n.epochNanoseconds,t,r)}function uo(n,t){return It(Ac(t,n))}function co(n,t){return Tt(Ac(t,n))}function so(n,t){return Dt(Ac(t,n))}function ao(n,t){return{...n,calendar:t}}function fo(n,t){if(n===t)return n;const r=Yt(n),e=Yt(t);if(r===e||r===Fu)return t;if(e===Fu)return n;throw new RangeError(Ii)}function ho(n,t,r){const e=new Set(r);return o=>(((n,t)=>{for(const r of t)if(r in n)return 1;return 0})(o=d(e,o),n)||Object.assign(o,t),r&&(o.timeZone=ic,["full","long"].includes(o.timeStyle)&&(o.timeStyle="medium")),o)}function lo(n,t=wo){const[r,,,e]=n;return(o,i=zs,...u)=>{const c=t(e&&e(...u),o,i,r),s=c.resolvedOptions();return[c,...go(n,s,u)]}}function wo(n,t,r,e){if(r=e(r),n){if(void 0!==r.timeZone)throw new TypeError(Bi);r.timeZone=n}return new Lu(t,r)}function go(n,t,r){const[,e,o]=n;return r.map((n=>(n.calendar&&((n,t,r)=>{if((r||n!==Fu)&&n!==t)throw new RangeError(Ii)})(Yt(n.calendar),t.calendar,o),e(n,t))))}function mo(n,t,r,e,o){function a(...n){if(!(this instanceof a))throw new TypeError(li);ma(this,t(...n))}function h(n,t){return Object.defineProperties((function(...t){return n.call(this,l(this),...t)}),i(t))}function l(t){const r=ga(t);if(!r||r.branding!==n)throw new TypeError(li);return r}return Object.defineProperties(a.prototype,{...c(f(h,r)),...u(f(h,e)),...s("Temporal."+n)}),Object.defineProperties(a,{...u(o),...i(n)}),[a,n=>{const t=Object.create(a.prototype);return ma(t,n),t},l]}function yo(n){return n=n.concat("id").sort(),t=>{if(!((n,t)=>{for(const r of t)if(!(r in n))return 0;return 1})(t,n))throw new TypeError("Invalid protocol");return t}}function vo(n){if(ga(n)||void 0!==n.calendar||void 0!==n.timeZone)throw new TypeError("Invalid bag");return n}function bo(n,t){const r={};for(const e in n)r[e]=({o:n},r)=>{const o=ga(r)||{},{branding:i}=o,u=i===Nc||t.includes(i)?o:ko(r);return n[e](u)};return r}function Mo(n){const t={};for(const r in n)t[r]=n=>{const{calendar:t}=n;return(e=t,"string"==typeof e?ws(e):(o=e,Object.assign(Object.create(Ea),{i:o})))[r](n);var e,o};return t}function po(){throw new TypeError("Cannot use valueOf")}function Eo({calendar:n}){return"string"==typeof n?new Mf(n):n}function Oo(n,t){if(t=yt(t),e(n)){const r=ga(n);if(r&&r.branding===jc)return et(t),r;const e=qo(n);return ze(sf(e||Fu),!e,n,t)}const r=((n,t)=>{const r=Se(ru(t));if(r)return Me(r),St(jn(r));const e=be(t),{calendar:o}=e,i=n(o),[u,c,s]=i.h(e),[a,f]=i.I(u,c),[h,l]=i.N(a,f,s);return St(cn(i.P(h,l,s)),o)})(ws,n);return et(t),r}function Io(n,t,r){return Er(J(t.call(n,Of(Et(r)))))}function To(n,t=ja){const r=Object.keys(t).sort(),e={};for(const o of r)e[o]=y(t[o],n,iu(n[o]));return e}function Ro(n,t,r){const e=t.l(xo(r).epochNanoseconds,n);return e?Of(Et(e)):null}function So(n){return e(n)?(ga(n)||{}).timeZone||Ca(n):(n=>$e((n=>{const t=Te(n);return t&&(t.timeZone||t.m&&ic||t.offset)||n})(ru(n))))(n)}function Do(n,t){return"string"==typeof n?Bc(n):To(n,t)}function Po(n){return Do(n,Na)}function jo(n,t){if(t=yt(t),e(n)){const r=ga(n);return r&&r.branding===Pc?(et(t),r):He(uf(Uo(n)),n,t)}const r=((n,t)=>{const r=Re(ru(t));if(r)return Me(r),Rt(un(jn(r)));const e=be(t);return Rt(Fr(n(e.calendar),e))})(ws,n);return et(t),r}function No(n,t){if(t=yt(t),e(n)){const r=ga(n);if(r&&r.branding===Cc)return ot(t),r;const e=Uo(n);return((n,t,r,e,o,i)=>{const u=_e(r,o,Pu,vu,Mu),c=n(u.timeZone),[s,a,f]=ot(i),h=r.dateFromFields(u,vt(i,s)),l=Ve(u,s);return Ot(Mr(t(c),{...h,...l},void 0!==u.offset?ve(u.offset):void 0,a,f),c,e)})(So,Do,cf(e),e,n,t)}return((n,t)=>{const r=Te(ru(n));if(!r||!r.timeZone)throw new RangeError(Li(n));const{offset:e}=r,o=e?ve(e):void 0,[,i,u]=ot(t);return pe(r,o,i,u)})(n,t)}function Yo(n){return f((n=>t=>n(Fo(t))),n)}function Fo(n){return Ac(n,Po)}function Co(n,t){if(e(n)){const r=ga(n)||{};switch(r.branding){case Fc:return et(t),r;case Yc:return et(t),Dt(r);case Cc:return et(t),so(Po,r)}return((n,t)=>{const r=et(t);return Dt(Ve(Ge(n,mu,[],1),r))})(n,t)}return et(t),(n=>{let t,r=(n=>{const t=ts.exec(n);return t?(Ne(t[10]),je(t)):void 0})(ru(n));if(!r){if(r=Te(n),!r)throw new RangeError(Li(n));if(!r.p)throw new RangeError(Li(n));if(r.m)throw new RangeError(Ai("Z"));Me(r)}if((t=Re(n))&&Nn(t))throw new RangeError(Li(n));if((t=Se(n))&&Nn(t))throw new RangeError(Li(n));return Dt(Fn(r,1))})(n)}function Zo(n){return void 0===n?void 0:Co(n)}function $o(n,t){if(t=yt(t),e(n)){const r=ga(n)||{};switch(r.branding){case Yc:return et(t),r;case Nc:return et(t),It({...r,...Hu});case Cc:return et(t),uo(Po,r)}return((n,t,r)=>{const e=_e(n,t,Pu,[],gu),o=et(r);return It(sn({...n.dateFromFields(e,vt(r,o)),...Ve(e,o)}))})(cf(Uo(n)),n,t)}const r=(n=>{const t=Te(ru(n));if(!t||t.m)throw new RangeError(Li(n));return It(Ee(t))})(n);return et(t),r}function ko(n,t){if(t=yt(t),e(n)){const r=ga(n)||{};switch(r.branding){case Nc:return et(t),r;case Yc:return et(t),Tt(r);case Cc:return et(t),co(Po,r)}return xe(cf(Uo(n)),n,t)}const r=be(n);return et(t),r}function Lo(n,t,r){return q(t.call(n,xa(Tt(r,n))))}function Ao(n){return t=>"string"==typeof t?ws(t):((n,t)=>{const r=Object.keys(t).sort(),e={};for(const o of r)e[o]=y(t[o],n,n[o]);return e})(t,n)}function Wo(n){if(e(n)){const t=ga(n);return t&&t.branding===$c?t:(n=>{const t=Ge(n,cu);return Pt(ae({...lu,...t}))})(n)}return(n=>{const t=(n=>{const t=os.exec(n);return t?(n=>{function t(n,t,i){let u=0,c=0;if(i&&([u,o]=O(o,tu[i])),void 0!==n){if(e)throw new RangeError(Ai(n));c=(n=>{const t=parseInt(n);if(!Number.isFinite(t))throw new RangeError(Ai(n));return t})(n),r=1,t&&(o=Ye(t)*(tu[i]/Ki),e=1)}return u+c}let r=0,e=0,o=0,i={...a(uu,[t(n[2]),t(n[3]),t(n[4]),t(n[5]),t(n[6],n[7],5),t(n[8],n[9],4),t(n[10],n[11],3)]),...j(o,2,uu)};if(!r)throw new RangeError(fi(uu));return Ce(n[1])<0&&(i=ce(i)),i})(t):void 0})(ru(n));if(!t)throw new RangeError(Li(n));return Pt(ae(t))})(n)}function Bo(n){if(void 0!==n){if(e(n)){const t=ga(n)||{};switch(t.branding){case Cc:case Nc:return t;case Yc:return Tt(t)}const r=Uo(n);return{...((n,t,r,e)=>{const o=_e(r,e,Pu,[],Mu);if(void 0!==o.timeZone){const e=r.dateFromFields(o),i=Ve(o),u=n(o.timeZone);return{epochNanoseconds:Mr(t(u),{...e,...i},void 0!==o.offset?ve(o.offset):void 0),timeZone:u}}return{...r.dateFromFields(o),...Hu}})(So,Do,cf(r),n),calendar:r}}return(n=>{const t=Te(ru(n));if(!t)throw new RangeError(Li(n));if(t.timeZone)return pe(t,t.offset?ve(t.offset):void 0);if(t.m)throw new RangeError(Li(n));return Oe(t)})(n)}}function Uo(n){return qo(n)||Fu}function qo(n){const{calendar:t}=n;if(void 0!==t)return Jo(t)}function Jo(n){return e(n)?(ga(n)||{}).calendar||pf(n):(n=>xn((n=>{const t=Te(n)||Re(n)||Se(n);return t?t.calendar:n})(ru(n))))(n)}function xo(n){if(e(n)){const t=ga(n);if(t)switch(t.branding){case Zc:return t;case Cc:return Et(t.epochNanoseconds)}}return(n=>{const t=Te(n=Q(n));if(!t)throw new RangeError(Li(n));let r;if(t.m)r=0;else{if(!t.offset)throw new RangeError(Li(n));r=ve(t.offset)}return t.timeZone&&De(t.timeZone,1),Et(mn(Pn(t),r))})(n)}function Ho(n){return function(...t){const r=Rf.get(this),[e,...o]=r(...t);return e[n](...o)}}function zo(n){return function(...t){return Rf.get(this).u[n](...t)}}function _o(n){const t=ta[n];if(!t)throw new TypeError(Wi(n));return lo(t,o(wo))}function Go(n){const t=Vo();return Mn(t,n.getOffsetNanosecondsFor(t))}function Vo(){return L(Date.now(),Vi)}function Ko(){return Sf||(Sf=(new Lu).resolvedOptions().timeZone)}const Qo=(n,t)=>`Non-integer ${n}: ${t}`,Xo=(n,t)=>`Non-positive ${n}: ${t}`,ni=(n,t)=>`Non-finite ${n}: ${t}`,ti=n=>`Cannot convert bigint to ${n}`,ri=n=>`Invalid bigint: ${n}`,ei="Cannot convert Symbol to string",oi="Invalid object",ii=(n,t,r,e,o)=>o?ii(n,o[t],o[r],o[e]):ui(n,t)+`; must be between ${r}-${e}`,ui=(n,t)=>`Invalid ${n}: ${t}`,ci=n=>`Missing ${n}`,si=n=>`Invalid field ${n}`,ai=n=>`Duplicate field ${n}`,fi=n=>"No valid fields: "+n.join(),hi=(n,t,r)=>ui(n,t)+"; must be "+Object.keys(r).join(),li="Invalid calling context",di="Forbidden era/eraYear",wi="Mismatching era/eraYear",gi="Mismatching year/eraYear",mi=n=>`Invalid era: ${n}`,yi=n=>"Missing year"+(n?"/era/eraYear":""),vi=n=>`Invalid monthCode: ${n}`,bi="Mismatching month/monthCode",Mi="Missing month/monthCode",pi="Cannot guess year",Ei="Invalid leap month",Oi="Invalid protocol results",Ii="Mismatching Calendars",Ti=n=>`Invalid Calendar: ${n}`,Ri="Mismatching TimeZones",Si="Forbidden ICU TimeZone",Di="Out-of-bounds offset",Pi="Out-of-bounds TimeZone gap",ji="Invalid TimeZone offset",Ni="Ambiguous offset",Yi="Out-of-bounds date",Fi="Out-of-bounds duration",Ci="Cannot mix duration signs",Zi="Missing relativeTo",$i="Cannot use large units",ki="smallestUnit > largestUnit",Li=n=>`Cannot parse: ${n}`,Ai=n=>`Invalid substring: ${n}`,Wi=n=>`Cannot format ${n}`,Bi="Cannot specify TimeZone",Ui=y(h,((n,t)=>t)),qi=y(h,((n,t,r)=>r)),Ji=y(M,2),xi={nanosecond:0,microsecond:1,millisecond:2,second:3,minute:4,hour:5,day:6,week:7,month:8,year:9},Hi=Object.keys(xi),zi=864e5,_i=1e3,Gi=1e3,Vi=1e6,Ki=1e9,Qi=6e10,Xi=36e11,nu=864e11,tu=[1,Gi,Vi,Ki,Qi,Xi,nu],ru=y(_,"string"),eu=y(_,"boolean"),ou=y(_,"number"),iu=y(_,"function"),uu=Hi.map((n=>n+"s")),cu=b(uu),su=uu.slice(0,6),au=uu.slice(6),fu=au.slice(1),hu=Ui(uu),lu=qi(uu,0),du=qi(su,0),wu=y(m,uu),gu=Hi.slice(0,6),mu=b(gu),yu=["offset"],vu=["timeZone"],bu=[...gu,...yu],Mu=[...bu,...vu],pu=["era","eraYear"],Eu=[...pu,"year"],Ou=["year"],Iu=["monthCode"],Tu=["month",...Iu],Ru=["day"],Su=[...Tu,...Ou],Du=[...Iu,...Ou],Pu=[...Ru,...Su],ju=[...Ru,...Tu],Nu=[...Ru,...Iu],Yu=qi(gu,0),Fu="iso8601",Cu="gregory",Zu="japanese",$u={[Cu]:{bce:-1,ce:0},[Zu]:{bce:-1,ce:0,meiji:1867,taisho:1911,showa:1925,heisei:1988,reiwa:2018},ethioaa:{era0:0},ethiopic:{era0:0,era1:5500},coptic:{era0:-1,era1:0},roc:{beforeroc:-1,minguo:0},buddhist:{be:0},islamic:{ah:0},indian:{saka:0},persian:{ap:0}},ku={chinese:13,dangi:13,hebrew:-6},Lu=Intl.DateTimeFormat,Au="en-GB",Wu=["isoNanosecond","isoMicrosecond","isoMillisecond","isoSecond","isoMinute","isoHour"],Bu=["isoDay","isoMonth","isoYear"],Uu=[...Wu,...Bu],qu=b(Bu),Ju=b(Wu),xu=b(Uu),Hu=qi(Ju,0),zu=y(m,Uu),_u=1e8,Gu=_u*zi,Vu=[_u,0],Ku=[-_u,0],Qu=275760,Xu=-271821,nc=1970,tc=1972,rc=12,ec=vn(1868,9,8),oc=o((n=>{const t=wn(n);if(t<ec)return Dn(n);const r=on(fc(Zu),t),{era:e,eraYear:o}=$n(r,Zu);return[e,o]}),WeakMap),ic="UTC",uc=5184e3,cc=yn(1847),sc=yn((new Date).getUTCFullYear()+10),ac=o((n=>{function t(n){return((n,t)=>({...$n(n,t),F:n.month,day:parseInt(n.day)}))(on(r,n),e)}const r=fc(n),e=Hn(n);return{id:n,O:Cn(t),B:Zn(t)}})),fc=o((n=>new Lu(Au,{calendar:n,timeZone:ic,era:"short",year:"numeric",month:"short",day:"numeric"}))),hc=/^M(\d{2})(L?)$/,lc="smallestUnit",dc="unit",wc="roundingIncrement",gc="fractionalSecondDigits",mc="relativeTo",yc={constrain:0,reject:1},vc=Object.keys(yc),bc=y(bt,lc),Mc=y(bt,"largestUnit"),pc=y(bt,dc),Ec=y(Mt,"overflow",yc),Oc=y(Mt,"disambiguation",{compatible:0,reject:1,earlier:2,later:3}),Ic=y(Mt,"offset",{reject:0,use:1,prefer:2,ignore:3}),Tc=y(Mt,"calendarName",{auto:0,never:1,critical:2,always:3}),Rc=y(Mt,"timeZoneName",{auto:0,never:1,critical:2}),Sc=y(Mt,"offset",{auto:0,never:1}),Dc=y(Mt,"roundingMode",{floor:0,halfFloor:1,ceil:2,halfCeil:3,trunc:4,halfTrunc:5,expand:6,halfExpand:7,halfEven:8}),Pc="PlainYearMonth",jc="PlainMonthDay",Nc="PlainDate",Yc="PlainDateTime",Fc="PlainTime",Cc="ZonedDateTime",Zc="Instant",$c="Duration",kc=[Math.floor,n=>D(n)?Math.floor(n):Math.round(n),Math.ceil,n=>D(n)?Math.ceil(n):Math.round(n),Math.trunc,n=>D(n)?Math.trunc(n)||0:Math.round(n),n=>n<0?Math.floor(n):Math.ceil(n),n=>Math.sign(n)*Math.round(Math.abs(n))||0,n=>D(n)?(n=Math.trunc(n)||0)+n%2:Math.round(n)],Lc=/0+$/,Ac=o(((n,t)=>{const{epochNanoseconds:r}=n,e=(t.getOffsetNanosecondsFor?t:t(n.timeZone)).getOffsetNanosecondsFor(r),o=Mn(r,e);return{calendar:n.calendar,...o,offsetNanoseconds:e}}),WeakMap),Wc=2**32-1,Bc=o((n=>{const t=Le(n);return"object"==typeof t?new qc(t):new Uc(t||0)}));class Uc{constructor(n){this.v=n}getOffsetNanosecondsFor(){return this.v}getPossibleInstantsFor(n){return[mn(n,this.v)]}l(){}}class qc{constructor(n){this.$=(n=>{function t(n){const t=E(n,u,c),[o,s]=ye(t),a=e(o),f=e(s);return a===f?a:r(i(o,s),a,f,n)}function r(t,r,e,o){let i,u;for(;(void 0===o||void 0===(i=o<t[0]?r:o>=t[1]?e:void 0))&&(u=t[1]-t[0]);){const r=t[0]+Math.floor(u/2);n(r)===e?t[1]=r:t[0]=r+1}return i}const e=o(n),i=o(me);let u=cc,c=sc;return{G(n){const r=t(n-86400),e=t(n+86400),o=n-r,i=n-e;if(r===e)return[o];const u=t(o);return u===t(i)?[n-u]:r>e?[o,i]:[]},V:t,l(n,t){const o=E(n,u,c);let[s,a]=ye(o);const f=uc*t,h=t<0?()=>a>u||(u=o,0):()=>s<c||(c=o,0);for(;h();){const o=e(s),u=e(a);if(o!==u){const e=i(s,a);r(e,o,u);const c=e[0];if((p(c,n)||1)===t)return c}s+=f,a+=f}}}})((n=>t=>{const r=on(n,t*_i);return yn(kn(r),parseInt(r.month),parseInt(r.day),parseInt(r.hour),parseInt(r.minute),parseInt(r.second))-t})(n))}getOffsetNanosecondsFor(n){return this.$.V(ln(n))*Ki}getPossibleInstantsFor(n){const[t,r]=[yn((e=n).isoYear,e.isoMonth,e.isoDay,e.isoHour,e.isoMinute,e.isoSecond),e.isoMillisecond*Vi+e.isoMicrosecond*Gi+e.isoNanosecond];var e;return this.$.G(t).map((n=>an(F(L(n,Ki),r))))}l(n,t){const[r,e]=dn(n),o=this.$.l(r+(t>0||e?1:0),t);if(void 0!==o)return L(o,Ki)}}const Jc="([+−-])",xc="(?:[.,](\\d{1,9}))?",Hc=`(?:(?:${Jc}(\\d{6}))|(\\d{4}))-?(\\d{2})`,zc="(\\d{2})(?::?(\\d{2})(?::?(\\d{2})"+xc+")?)?",_c=Jc+zc,Gc=Hc+"-?(\\d{2})(?:[T ]"+zc+"(Z|"+_c+")?)?",Vc="\\[(!?)([^\\]]*)\\]",Kc=`((?:${Vc}){0,9})`,Qc=Fe(Hc+Kc),Xc=Fe("(?:--)?(\\d{2})-?(\\d{2})"+Kc),ns=Fe(Gc+Kc),ts=Fe("T?"+zc+"(?:"+_c+")?"+Kc),rs=Fe(_c),es=new RegExp(Vc,"g"),os=Fe(`${Jc}?P(\\d+Y)?(\\d+M)?(\\d+W)?(\\d+D)?(?:T(?:(\\d+)${xc}H)?(?:(\\d+)${xc}M)?(?:(\\d+)${xc}S)?)?`),is=o((n=>new Lu(Au,{timeZone:n,era:"short",year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}))),us=/^(AC|AE|AG|AR|AS|BE|BS|CA|CN|CS|CT|EA|EC|IE|IS|JS|MI|NE|NS|PL|PN|PR|PS|SS|VS)T$/,cs={era:Q,eraYear:tn,year:tn,month:en,monthCode:Q,day:en,...qi(gu,tn),...qi(uu,rn),offset:Q},ss=y(((n,t,r)=>{const e={};for(let o=0;o<n.length;o++)e[t[o]]=r[n[o]];return e}),gu,Wu),as={dateAdd(n,r,e){const o=et(e);let i,{years:u,months:c,weeks:s,days:a}=r;if(a+=he(r,5)[0],u||c)i=((n,r,e,o,i)=>{let[u,c,s]=n.h(r);if(e){const[r,o]=n.I(u,c);u+=e,c=Kn(r,o,n.U(u)),c=t("month",c,1,n.L(u),i)}return o&&([u,c]=n._(u,c,o)),s=t("day",s,1,n.j(u,c),i),n.q(u,c,s)})(this,n,u,c,o);else{if(!s&&!a)return n;i=wn(n)}return i+=(7*s+a)*zi,cn(pn(i))},dateUntil(n,t,r){if(r<=7){let e=0,o=Gr({...n,...Hu},{...t,...Hu});return 7===r&&([e,o]=T(o,7)),{...lu,weeks:e,days:o}}const e=this.h(n),o=this.h(t);let[i,u,c]=((n,t,r,e,o,i,u)=>{let c=o-t,s=i-r,a=u-e;if(c||s){const f=Math.sign(c||s);let h=n.j(o,i),l=0;if(Math.sign(a)===-f){const e=h;[o,i]=n._(o,i,-f),c=o-t,s=i-r,h=n.j(o,i),l=f<0?-e:h}if(a=u-Math.min(e,h)+l,c){const[e,u]=n.I(t,r),[a,h]=n.I(o,i);if(s=a-e||Number(h)-Number(u),Math.sign(s)===-f){const r=f<0&&-n.L(o);c=(o-=f)-t,s=i-Kn(e,u,n.U(o))+(r||n.L(o))}}}return[c,s,a]})(this,...e,...o);return 8===r&&(u+=this.J(i,e[0]),i=0),{...lu,years:i,months:u,days:c}},dateFromFields(n,t){const r=et(t),e=to(this,n),o=ro(this,n,e,r),i=eo(this,n,o,e,r);return Tt(cn(this.P(e,o,i)),this.id||Fu)},yearMonthFromFields(n,t){const r=et(t),e=to(this,n),o=ro(this,n,e,r);return Rt(un(this.P(e,o,1)),this.id||Fu)},monthDayFromFields(n,e){const o=et(e),i=!this.id,{monthCode:u,year:c,month:s}=n;let a,f,h,l,d;if(void 0!==u){[a,f]=Vn(u),d=r(n,"day");const e=this.N(a,f,d);if(!e)throw new RangeError(pi);if([h,l]=e,void 0!==s&&s!==l)throw new RangeError(bi);i&&(l=t("month",l,1,rc,1),d=t("day",d,1,In(void 0!==c?c:h,l),o))}else{h=void 0===c&&i?tc:to(this,n),l=ro(this,n,h,o),d=eo(this,n,l,h,o);const t=this.U(h);f=l===t,a=Qn(l,t);const r=this.N(a,f,d);if(!r)throw new RangeError(pi);[h,l]=r}return St(cn(this.P(h,l,d)),this.id||Fu)},fields(n){return nt(this)&&n.includes("year")?[...n,...pu]:n},mergeFields(n,t){const r=Object.assign(Object.create(null),n);return oo(r,t,Tu),nt(this)&&(oo(r,t,Eu),this.id===Zu&&oo(r,t,ju,pu)),r},inLeapYear(n){const[t]=this.h(n);return this.K(t)},monthsInYear(n){const[t]=this.h(n);return this.L(t)},daysInMonth(n){const[t,r]=this.h(n);return this.j(t,r)},daysInYear(n){const[t]=this.h(n);return this.X(t)},dayOfYear:Gn,era(n){return this.ee(n)[0]},eraYear(n){return this.ee(n)[1]},monthCode(n){const[t,r]=this.h(n),[e,o]=this.I(t,r);return((n,t)=>"M"+Ji(n)+(t?"L":""))(e,o)},dayOfWeek:Sn,daysInWeek(){return 7}},fs={dayOfYear:Gn,h:En,q:vn,weekOfYear:zn,yearOfWeek:_n,R(n){function t(n){return(7-n<e?7:0)-n}function r(n){const r=Tn(l+n),e=n||1,o=t(I(s+r*e,7));return f=(r+(o-a)*e)/7}const e=this.id?1:4,o=Sn(n),i=this.dayOfYear(n),u=I(o-1,7),c=i-1,s=I(u-c,7),a=t(s);let f,h=Math.floor((c-a)/7)+1,l=n.isoYear;return h?h>r(0)&&(h=1,l++):(h=r(-1),l--),[h,l,f]}},hs={...as,...fs,h:En,ee(n){return this.id===Cu?Dn(n):this.id===Zu?oc(n):[]},I(n,t){return[t,0]},N(n,t){if(!t)return[tc,n]},K:Rn,U(){},L:On,J:n=>n*rc,j:In,X:Tn,P(n,t,r){return{isoYear:n,isoMonth:t,isoDay:r}},q:vn,_(n,t,r){return n+=R(r,rc),(t+=S(r,rc))<1?(n--,t+=rc):t>rc&&(n++,t-=rc),[n,t]},year(n){return n.isoYear},month(n){return n.isoMonth},day:n=>n.isoDay},ls={dayOfYear:Gn,h:Ln,q:An,weekOfYear:zn,yearOfWeek:_n,R(){return[]}},ds={...as,...ls,h:Ln,ee(n){const t=this.O(n);return[t.era,t.eraYear]},I(n,t){const r=Wn.call(this,n);return[Qn(t,r),r===t]},N(n,t,r){let[e,o,i]=Ln.call(this,{isoYear:tc,isoMonth:rc,isoDay:31});const u=Wn.call(this,e),c=o===u;1===(p(n,Qn(o,u))||p(Number(t),Number(c))||p(r,i))&&e--;for(let o=0;o<100;o++){const i=e-o,u=Wn.call(this,i),c=Kn(n,t,u);if(t===(c===u)&&r<=Un.call(this,i,c))return[i,c]}},K(n){const t=Bn.call(this,n);return t>Bn.call(this,n-1)&&t>Bn.call(this,n+1)},U:Wn,L:qn,J(n,t){const r=t+n,e=Math.sign(n),o=e<0?-1:0;let i=0;for(let n=t;n!==r;n+=e)i+=qn.call(this,n+o);return i},j:Un,X:Bn,P(n,t,r){return pn(An.call(this,n,t,r))},q:An,_(n,t,r){if(r){if(t+=r,!Number.isSafeInteger(t))throw new RangeError(Yi);if(r<0)for(;t<1;)t+=qn.call(this,--n);else{let r;for(;t>(r=qn.call(this,n));)t-=r,n++}}return[n,t]},year(n){return this.O(n).year},month(n){const{year:t,F:r}=this.O(n),{C:e}=this.B(t);return e[r]+1},day(n){return this.O(n).day}},ws=(gs=hs,ms=ds,n=>n===Fu?gs:n===Cu||n===Zu?Object.assign(Object.create(gs),{id:n}):Object.assign(Object.create(ms),ac(n)));var gs,ms;const ys="numeric",vs=["timeZoneName"],bs={month:ys,day:ys},Ms={year:ys,month:ys},ps={...Ms,day:ys},Es={hour:ys,minute:ys,second:ys},Os={...ps,...Es},Is={...Os,timeZoneName:"short"},Ts=Object.keys(Ms),Rs=Object.keys(bs),Ss=Object.keys(ps),Ds=Object.keys(Es),Ps=["dateStyle"],js=[...Ts,...Ps],Ns=[...Rs,...Ps],Ys=[...Ss,...Ps,"weekday"],Fs=[...Ds,"dayPeriod","timeStyle"],Cs=[...Ys,...Fs],Zs=[...Cs,...vs],$s=[...vs,...Fs],ks=[...vs,...Ys],Ls=[...vs,"day","weekday",...Fs],As=[...vs,"year","weekday",...Fs],Ws=ho(Cs,Os),Bs=ho(Zs,Is),Us=ho(Cs,Os,vs),qs=ho(Ys,ps,$s),Js=ho(Fs,Es,ks),xs=ho(js,Ms,Ls),Hs=ho(Ns,bs,As),zs={},_s=[Ws,jt],Gs=[Bs,jt,0,(n,t)=>{const r=Yt(n.timeZone);if(t&&Yt(t.timeZone)!==r)throw new RangeError(Ri);return r}],Vs=[Us,wn],Ks=[qs,wn],Qs=[Js,n=>fn(n)/Vi],Xs=[xs,wn,1],na=[Hs,wn,1],ta={Instant:_s,PlainDateTime:Vs,PlainDate:Ks,PlainTime:Qs,PlainYearMonth:Xs,PlainMonthDay:na},ra=lo(_s),ea=lo(Gs),oa=lo(Vs),ia=lo(Ks),ua=lo(Qs),ca=lo(Xs),sa=lo(na),aa={era(n){if(void 0!==n)return ru(n)},eraYear:U,year:J,month:q,daysInMonth:q,daysInYear:q,inLeapYear:eu,monthsInYear:q},fa={monthCode:ru},ha={day:q},la={dayOfWeek:q,dayOfYear:q,weekOfYear(n){if(void 0!==n)return q(n)},yearOfWeek:U,daysInWeek:q},da={...aa,...fa,...ha,...la},wa=new WeakMap,ga=wa.get.bind(wa),ma=wa.set.bind(wa),ya={...bo(aa,[Pc]),...bo(la,[]),...bo(fa,[Pc,jc]),...bo(ha,[jc])},va=Mo(da),ba=Mo({...aa,...fa}),Ma=Mo({...fa,...ha}),pa={calendarId:n=>Yt(n.calendar)},Ea=f(((n,t)=>function(r){const{i:e}=this;return n(e[t](xa(Tt(r,e))))}),da),Oa=h((n=>t=>t[n]),uu.concat("sign")),Ia=h(((n,t)=>n=>n[Wu[t]]),gu),Ta={epochSeconds(n){return ln(n.epochNanoseconds)},epochMilliseconds:jt,epochMicroseconds(n){return A(n.epochNanoseconds,Gi)},epochNanoseconds(n){return A(n.epochNanoseconds)}},Ra=y(d,new Set(["branding"])),[Sa,Da,Pa]=mo(jc,y(((n,t,r,e=Fu,o=tc)=>{const i=tn(t),u=tn(r),c=n(e);return St(cn(jn({isoYear:tn(o),isoMonth:i,isoDay:u})),c)}),Jo),{...pa,...Ma},{getISOFields:Ra,getCalendar:Eo,with(n,t,r){return Da(((n,t,r,e,o)=>{const i=yt(o);return((n,t,r,e)=>{const o=Ke(n,t,r,Pu);return n.monthDayFromFields(o,e)})(n(t.calendar),r,e,i)})(hf,n,this,vo(t),r))},equals(n,t){return!Ue(r=n,e=Oo(t))&&Ft(r.calendar,e.calendar);var r,e},toPlainDate(n,t){return xa(((n,t,r,e)=>((n,t,r)=>no(n,t,Nu,z(r),Ou))(n(t.calendar),r,e))(ff,n,this,t))},toLocaleString(n,t,r){const[e,o]=sa(t,r,n);return e.format(o)},toString:or,toJSON:n=>or(n),valueOf:po},{from:(n,t)=>Da(Oo(n,t))}),ja={getOffsetNanosecondsFor:Io,getPossibleInstantsFor(n,t,r){const e=[...t.call(n,qa(It(r,Fu)))].map((n=>If(n).epochNanoseconds)),o=e.length;return o>1&&(e.sort(Z),Or(W(C(e[0],e[o-1])))),e}},Na={getOffsetNanosecondsFor:Io},[Ya,Fa]=mo("TimeZone",(n=>{const t=(n=>$e(ru(n)))(n);return{branding:"TimeZone",id:t,o:Bc(t)}}),{id:n=>n.id},{getPossibleInstantsFor:({o:n},t)=>n.getPossibleInstantsFor($o(t)).map((n=>Of(Et(n)))),getOffsetNanosecondsFor:({o:n},t)=>n.getOffsetNanosecondsFor(xo(t).epochNanoseconds),getOffsetStringFor(n,t){const r=xo(t).epochNanoseconds;return wr(To(this,Na).getOffsetNanosecondsFor(r))},getPlainDateTimeFor(n,t,r=Fu){const e=xo(t).epochNanoseconds,o=To(this,Na).getOffsetNanosecondsFor(e);return qa(It(Mn(e,o),Jo(r)))},getInstantFor(n,t,r){const e=$o(t),o=it(r),i=To(this);return Of(Et(pr(i,e,o)))},getNextTransition:({o:n},t)=>Ro(1,n,t),getPreviousTransition:({o:n},t)=>Ro(-1,n,t),equals(n,t){return!!Je(this,So(t))},toString:n=>n.id,toJSON:n=>n.id},{from(n){const t=So(n);return"string"==typeof t?new Ya(t):t}}),Ca=yo(Object.keys(ja)),[Za,$a,ka]=mo(Pc,y(((n,t,r,e=Fu,o=1)=>{const i=tn(t),u=tn(r),c=n(e);return Rt(un(jn({isoYear:i,isoMonth:u,isoDay:tn(o)})),c)}),Jo),{...pa,...ba},{getISOFields:Ra,getCalendar:Eo,with(n,t,r){return $a(((n,t,r,e,o)=>{const i=yt(o);return Rt(((n,t,r,e)=>{const o=Ke(n,t,r,Su);return n.yearMonthFromFields(o,e)})(n(t.calendar),r,e,i))})(af,n,this,vo(t),r))},add:(n,t,r)=>$a(Dr(wf,0,n,Wo(t),r)),subtract:(n,t,r)=>$a(Dr(wf,1,n,Wo(t),r)),until:(n,t,r)=>yf(Wr(gf,0,n,jo(t),r)),since:(n,t,r)=>yf(Wr(gf,1,n,jo(t),r)),equals(n,t){return!Ue(r=n,e=jo(t))&&Ft(r.calendar,e.calendar);var r,e},toPlainDate(n,t){return xa(((n,t,r,e)=>((n,t,r)=>no(n,t,Du,z(r),Ru))(n(t.calendar),r,e))(ff,n,this,t))},toLocaleString(n,t,r){const[e,o]=ca(t,r,n);return e.format(o)},toString:er,toJSON:n=>er(n),valueOf:po},{from:(n,t)=>$a(jo(n,t)),compare:(n,t)=>Ue(jo(n),jo(t))}),[La,Aa]=mo(Cc,y(((n,t,r,e,o=Fu)=>Ot(an(k(X(r))),t(e),n(o))),Jo,So),{...Ta,...pa,...Yo(va),...Yo(Ia),offset:n=>wr(Fo(n).offsetNanoseconds),offsetNanoseconds:n=>Fo(n).offsetNanoseconds,timeZoneId:n=>Yt(n.timeZone),hoursInDay:n=>((n,t)=>{const r=n(t.timeZone),e=Ac(t,r),[o,i]=Ut(e),u=W(C(pr(r,o),pr(r,i)),Xi,1);if(u<=0)throw new RangeError(Oi);return u})(Do,n)},{getISOFields:n=>((n,t)=>{const r=Ac(t,n);return{calendar:t.calendar,...l(xu,r),offset:wr(r.offsetNanoseconds),timeZone:t.timeZone}})(Po,n),getCalendar:Eo,getTimeZone:({timeZone:n})=>"string"==typeof n?new Ya(n):n,with(n,t,r){return Aa(((n,t,r,e,o,i)=>{const u=yt(i),{calendar:c,timeZone:s}=r;return Ot(((n,t,r,e,o)=>{const i=Ke(n,r,e,Pu,bu,yu),[u,c,s]=ot(o,2);return Mr(t,{...n.dateFromFields(i,vt(o,u)),...Ve(i,u)},ve(i.offset),c,s)})(n(c),t(s),e,o,u),s,c)})(ff,Do,n,this,vo(t),r))},withCalendar:(n,t)=>Aa(ao(n,Jo(t))),withTimeZone:(n,t)=>Aa(((n,t)=>({...n,timeZone:t}))(n,So(t))),withPlainDate:(n,t)=>Aa(((n,t,r)=>{const e=t.timeZone,o=n(e),i={...Ac(t,o),...r},u=fo(t.calendar,r.calendar);return Ot(Mr(o,i,i.offsetNanoseconds,2),e,u)})(Do,n,ko(t))),withPlainTime:(n,t)=>Aa(((n,t,r=Hu)=>{const e=t.timeZone,o=n(e),i={...Ac(t,o),...r};return Ot(Mr(o,i,i.offsetNanoseconds,2),e,t.calendar)})(Do,n,Zo(t))),add:(n,t,r)=>Aa(Tr(lf,Do,0,n,Wo(t),r)),subtract:(n,t,r)=>Aa(Tr(lf,Do,1,n,Wo(t),r)),until:(n,t,r)=>yf(Pt(kr(df,Do,0,n,No(t),r))),since:(n,t,r)=>yf(Pt(kr(df,Do,1,n,No(t),r))),round:(n,t)=>Aa(((n,t,r)=>{let{epochNanoseconds:e,timeZone:o,calendar:i}=t;const[u,c,s]=ct(r);if(0===u&&1===c)return t;const a=n(o);if(6===u)e=((n,t,r,e)=>{const o=Ac(r,t),[i,u]=n(o),c=r.epochNanoseconds,s=pr(t,i),a=pr(t,u);if($(c,s,a))throw new RangeError(Oi);return Gt($t(c,s,a),e)?a:s})(Ut,a,t,s);else{const n=a.getOffsetNanosecondsFor(e);e=Mr(a,kt(Mn(e,n),u,c,s),n,2,0,1)}return Ot(e,o,i)})(Do,n,t)),startOfDay:n=>Aa(((n,t)=>{const{timeZone:r,calendar:e}=t,o=((n,t,r)=>pr(t,n(Ac(r,t))))(qt,n(r),t);return Ot(o,r,e)})(Do,n)),equals(n,t){return!We(r=n,e=No(t))&&!!Je(r.timeZone,e.timeZone)&&Ft(r.calendar,e.calendar);var r,e},toInstant:n=>Of(Et(n.epochNanoseconds)),toPlainDateTime:n=>qa(uo(Po,n)),toPlainDate:n=>xa(co(Po,n)),toPlainTime:n=>Ba(so(Po,n)),toPlainYearMonth(n){return $a(Xe(uf(n.calendar),this))},toPlainMonthDay(n){return Da(Qe(sf(n.calendar),this))},toLocaleString(n,t,r={}){const[e,o]=ea(t,r,n);return e.format(o)},toString:(n,t)=>nr(Po,n,t),toJSON:n=>nr(Po,n),valueOf:po},{from:(n,t)=>Aa(No(n,t)),compare:(n,t)=>We(No(n),No(t))}),[Wa,Ba]=mo(Fc,((n=0,t=0,r=0,e=0,o=0,i=0)=>Dt(Fn(f(tn,a(Wu,[n,t,r,e,o,i])),1))),Ia,{getISOFields:Ra,with(n,t,r){return Ba(((n,t,r)=>Dt(((n,t,r)=>{const e=et(r);return Ve({...l(mu,n),...Ge(t,mu)},e)})(n,t,r)))(this,vo(t),r))},add:(n,t)=>Ba(Pr(0,n,Wo(t))),subtract:(n,t)=>Ba(Pr(1,n,Wo(t))),until:(n,t,r)=>yf(Ur(0,n,Co(t),r)),since:(n,t,r)=>yf(Ur(1,n,Co(t),r)),round:(n,t)=>Ba(((n,t)=>{const[r,e,o]=ct(t,5);var i;return Dt((i=o,At(n,Bt(r,e),i)[0]))})(n,t)),equals(n,t){return!qe(n,Co(t))},toZonedDateTime:(n,t)=>Aa(((n,t,r,e,o)=>{const i=z(o),u=t(i.plainDate),c=n(i.timeZone);return Ot(pr(r(c),{...u,...e}),c,u.calendar)})(So,ko,Do,n,t)),toPlainDateTime(n,t){return qa((r=n,e=ko(t),It(sn({...r,...e}))));var r,e},toLocaleString(n,t,r){const[e,o]=ua(t,r,n);return e.format(o)},toString:ir,toJSON:n=>ir(n),valueOf:po},{from:(n,t)=>Ba(Co(n,t)),compare:(n,t)=>qe(Co(n),Co(t))}),[Ua,qa]=mo(Yc,y(((n,t,r,e,o=0,i=0,u=0,c=0,s=0,h=0,l=Fu)=>It(sn(Pn(f(tn,a(Uu,[t,r,e,o,i,u,c,s,h])))),n(l))),Jo),{...pa,...va,...Ia},{getISOFields:Ra,getCalendar:Eo,with(n,t,r){return qa(((n,t,r,e,o)=>{const i=yt(o);return It(((n,t,r,e)=>{const o=Ke(n,t,r,Pu,gu),i=et(e);return sn({...n.dateFromFields(o,vt(e,i)),...Ve(o,i)})})(n(t.calendar),r,e,i))})(ff,n,this,vo(t),r))},withCalendar:(n,t)=>qa(ao(n,Jo(t))),withPlainDate(n,t){return qa((r=n,e=ko(t),It({...r,...e},fo(r.calendar,e.calendar))));var r,e},withPlainTime:(n,t)=>qa(((n,t=Hu)=>It({...n,...t}))(n,Zo(t))),add:(n,t,r)=>qa(Rr(lf,0,n,Wo(t),r)),subtract:(n,t,r)=>qa(Rr(lf,1,n,Wo(t),r)),until:(n,t,r)=>yf(Lr(df,0,n,$o(t),r)),since:(n,t,r)=>yf(Lr(df,1,n,$o(t),r)),round:(n,t)=>qa(((n,t)=>It(kt(n,...ct(t)),n.calendar))(n,t)),equals(n,t){return!Be(r=n,e=$o(t))&&Ft(r.calendar,e.calendar);var r,e},toZonedDateTime:(n,t,r)=>Aa(((n,t,r,e)=>{const o=((n,t,r,e)=>{const o=it(e);return pr(n(t),r,o)})(n,r,t,e);return Ot(an(o),r,t.calendar)})(Do,n,So(t),r)),toPlainDate:n=>xa(Tt(n)),toPlainTime:n=>Ba(Dt(n)),toPlainYearMonth(n){return $a(((n,t,r)=>{const e=n(t.calendar);return Rt({...t,...Xe(e,r)})})(uf,n,this))},toPlainMonthDay(n){return Da(Qe(sf(n.calendar),this))},toLocaleString(n,t,r){const[e,o]=oa(t,r,n);return e.format(o)},toString:tr,toJSON:n=>tr(n),valueOf:po},{from:(n,t)=>qa($o(n,t)),compare:(n,t)=>Be($o(n),$o(t))}),[Ja,xa,Ha]=mo(Nc,y(((n,t,r,e,o=Fu)=>Tt(cn(jn(f(tn,{isoYear:t,isoMonth:r,isoDay:e}))),n(o))),Jo),{...pa,...va},{getISOFields:Ra,getCalendar:Eo,with(n,t,r){return xa(((n,t,r,e,o)=>{const i=yt(o);return((n,t,r,e)=>{const o=Ke(n,t,r,Pu);return n.dateFromFields(o,e)})(n(t.calendar),r,e,i)})(ff,n,this,vo(t),r))},withCalendar:(n,t)=>xa(ao(n,Jo(t))),add:(n,t,r)=>xa(Sr(lf,0,n,Wo(t),r)),subtract:(n,t,r)=>xa(Sr(lf,1,n,Wo(t),r)),until:(n,t,r)=>yf(Ar(df,0,n,ko(t),r)),since:(n,t,r)=>yf(Ar(df,1,n,ko(t),r)),equals(n,t){return!Ue(r=n,e=ko(t))&&Ft(r.calendar,e.calendar);var r,e},toZonedDateTime(n,t){const r=!e(t)||t instanceof Ya?{timeZone:t}:t;return Aa(((n,t,r,e,o)=>{const i=n(o.timeZone),u=o.plainTime,c=void 0!==u?t(u):Hu;return Ot(pr(r(i),{...e,...c}),i,e.calendar)})(So,Co,Do,n,r))},toPlainDateTime:(n,t)=>qa(((n,t=Hu)=>It(sn({...n,...t})))(n,Zo(t))),toPlainYearMonth(n){return $a(Xe(uf(n.calendar),this))},toPlainMonthDay(n){return Da(Qe(sf(n.calendar),this))},toLocaleString(n,t,r){const[e,o]=ia(t,r,n);return e.format(o)},toString:rr,toJSON:n=>rr(n),valueOf:po},{from:(n,t)=>xa(ko(n,t)),compare:(n,t)=>Ue(ko(n),ko(t))}),za={fields(n,t,r){return[...t.call(n,r)]}},_a={dateFromFields(n,t,r,e){return Ha(t.call(n,Object.assign(Object.create(null),r),e))},...za},Ga={yearMonthFromFields(n,t,r,e){return ka(t.call(n,Object.assign(Object.create(null),r),e))},...za},Va={monthDayFromFields(n,t,r,e){return Pa(t.call(n,Object.assign(Object.create(null),r),e))},...za},Ka={mergeFields(n,t,r,e){return z(t.call(n,Object.assign(Object.create(null),r),Object.assign(Object.create(null),e)))}},Qa={..._a,...Ka},Xa={...Ga,...Ka},nf={...Va,...Ka},tf={dateAdd(n,t,r,e,o){return Ha(t.call(n,xa(Tt(r,n)),yf(Pt(e)),o))}},rf={...tf,dateUntil(n,t,r,e,o,i){return vf(t.call(n,xa(Tt(r,n)),xa(Tt(e,n)),Object.assign(Object.create(null),i,{largestUnit:Hi[o]})))}},ef={...tf,day:Lo},of={...rf,day:Lo},uf=Ao(Ga),cf=Ao(_a),sf=Ao(Va),af=Ao(Xa),ff=Ao(Qa),hf=Ao(nf),lf=Ao(tf),df=Ao(rf),wf=Ao(ef),gf=Ao(of),[mf,yf,vf]=mo($c,((n=0,t=0,r=0,e=0,o=0,i=0,u=0,c=0,s=0,h=0)=>Pt(ae(f(rn,a(uu,[n,t,r,e,o,i,u,c,s,h]))))),{...Oa,blank(n){return!n.sign}},{with:(n,t)=>yf(((n,t)=>{return Pt((r=n,e=t,ae({...r,...Ge(e,cu)})));var r,e})(n,t)),negated:n=>yf(ue(n)),abs:n=>yf((n=>-1===n.sign?ue(n):n)(n)),add:(n,t,r)=>yf(ie(Bo,df,Do,0,n,Wo(t),r)),subtract:(n,t,r)=>yf(ie(Bo,df,Do,1,n,Wo(t),r)),round:(n,t)=>yf(((n,t,r,e,o)=>{const i=ge(e),[u,c,s,a,f]=((n,t,r)=>{n=mt(n,lc);let e=Mc(n);const o=r(n[mc]);let i=lt(n);const u=Dc(n,7);let c=bc(n);if(void 0===e&&void 0===c)throw new RangeError("Required smallestUnit or largestUnit");return null==c&&(c=0),null==e&&(e=Math.max(c,t)),pt(e,c),i=dt(i,c,1),[e,c,i,u,o]})(o,i,n),h=Math.max(i,u);if(!ee(f)&&h<=6)return Pt(ae(((n,t,r,e,o)=>{const i=Ht(he(n),r,e,o);return{...lu,...le(i,t)}})(e,u,c,s,a)));if(!f)throw new RangeError(Zi);const[l,d,w]=Xr(t,r,f),g=ne(w),m=te(w),y=re(w),v=m(d,l,e);let b=y(d,l,v,u);const M=e.sign,p=se(b);if(M&&p&&M!==p)throw new RangeError(Oi);return p&&(b=xt(b,g(v),u,c,s,a,d,l,g,m)),Pt(b)})(Bo,df,Do,n,t)),total:(n,t)=>((n,t,r,e,o)=>{const i=ge(e),[u,c]=((n,t)=>{const r=t((n=mt(n,dc))[mc]);let e=pc(n);return e=H(dc,e),[e,r]})(o,n);if(oe(Math.max(u,i),c))return Ct(e,u);if(!c)throw new RangeError(Zi);const[s,a,f]=Xr(t,r,c),h=ne(f),l=te(f),d=re(f),w=l(a,s,e),g=d(a,s,w,u);return oe(u,c)?Ct(g,u):((n,t,r,e,o,i,u)=>{const c=se(n),[s,a]=Zt(e,wu(r,n),r,c,o,i,u),f=$t(t,s,a);return n[uu[r]]+f*c})(g,h(w),u,a,s,h,l)})(Bo,df,Do,n,t),toLocaleString(n,t,r){return Intl.DurationFormat?new Intl.DurationFormat(t,r).format(this):ur(n)},toString:ur,toJSON:n=>ur(n),valueOf:po},{from:n=>yf(Wo(n)),compare:(n,t,r)=>((n,t,r,e,o,i)=>{const u=n(gt(i).relativeTo),c=Math.max(ge(e),ge(o));if(g(uu,e,o))return 0;if(oe(c,u))return Z(he(e),he(o));if(!u)throw new RangeError(Zi);const[s,a,f]=Xr(t,r,u),h=ne(f),l=te(f);return Z(h(l(a,s,e)),h(l(a,s,o)))})(Bo,lf,Do,Wo(n),Wo(t),r)}),bf={toString:n=>n.id,toJSON:n=>n.id,...ya,dateAdd:({id:n,o:t},r,e,o)=>xa(Tt(t.dateAdd(ko(r),Wo(e),o),n)),dateUntil:({o:n},t,r,e)=>yf(Pt(n.dateUntil(ko(t),ko(r),(n=>(n=gt(n),Mc(n,9,6,1)))(e)))),dateFromFields:({id:n,o:t},r,e)=>xa(xe(t,r,e,n===Fu?["year","day"]:[])),yearMonthFromFields:({id:n,o:t},r,e)=>$a(He(t,r,e,n===Fu?Ou:[])),monthDayFromFields:({id:n,o:t},r,e)=>Da(ze(t,0,r,e,n===Fu?Ru:[])),fields({o:n},t){const r=new Set(Pu),e=[];for(const n of t){if(ru(n),!r.has(n))throw new RangeError(si(n));r.delete(n),e.push(n)}return n.fields(e)},mergeFields:({o:n},t,r)=>n.mergeFields(w(x(t)),w(x(r)))},[Mf]=mo("Calendar",(n=>{const t=(n=>xn(ru(n)))(n);return{branding:"Calendar",id:t,o:ws(t)}}),{id:n=>n.id},bf,{from(n){const t=Jo(n);return"string"==typeof t?new Mf(t):t}}),pf=yo(Object.keys(bf).slice(4)),[Ef,Of,If]=mo(Zc,(n=>Et(an(k(X(n))))),Ta,{add:(n,t)=>Of(Ir(0,n,Wo(t))),subtract:(n,t)=>Of(Ir(1,n,Wo(t))),until:(n,t,r)=>yf($r(0,n,xo(t),r)),since:(n,t,r)=>yf($r(1,n,xo(t),r)),round:(n,t)=>Of(((n,t)=>{const[r,e,o]=ct(t,5,1);return Et(Ht(n.epochNanoseconds,r,e,o,1))})(n,t)),equals(n,t){return!Ae(n,xo(t))},toZonedDateTime(n,t){const r=z(t);return Aa(io(n,So(r.timeZone),Jo(r.calendar)))},toZonedDateTimeISO:(n,t)=>Aa(io(n,So(t))),toLocaleString(n,t,r){const[e,o]=ra(t,r,n);return e.format(o)},toString:(n,t)=>Xt(So,Po,n,t),toJSON:n=>Xt(So,Po,n),valueOf:po},{from:n=>Of(xo(n)),fromEpochSeconds:n=>Of((n=>Et(an(L(n,Ki))))(n)),fromEpochMilliseconds:n=>Of((n=>Et(an(L(n,Vi))))(n)),fromEpochMicroseconds:n=>Of((n=>Et(an(k(X(n),Gi))))(n)),fromEpochNanoseconds:n=>Of((n=>Et(an(k(X(n)))))(n)),compare:(n,t)=>Ae(xo(n),xo(t))}),Tf=function(){const n=Lu.prototype,t=Object.getOwnPropertyDescriptors(n),r=Object.getOwnPropertyDescriptors(Lu),e=function(n,t={}){if(!(this instanceof e))return new e(n,t);Rf.set(this,((n,t={})=>{const r=new Lu(n,t),e=r.resolvedOptions(),i=e.locale,u=l(Object.keys(t),e),c=o(_o),s=(...n)=>{let t;const e=n.map(((n,r)=>{const e=ga(n),o=(e||{}).branding;if(r&&t&&t!==o)throw new TypeError("Mismatching types for formatting");return t=o,e}));return t?c(t)(i,u,...e):[r,...n]};return s.u=r,s})(n,t))};for(const n in t){const r=t[n],o=n.startsWith("format")&&Ho(n);"function"==typeof r.value?r.value="constructor"===n?e:o||zo(n):o&&(r.get=function(){return o.bind(this)})}return r.prototype.value=Object.create(n,t),Object.defineProperties(e,r),e}(),Rf=new WeakMap;let Sf;const Df=Object.defineProperties({},{...s("Temporal.Now"),...u({timeZoneId:()=>Ko(),instant:()=>Of(Et(Vo())),zonedDateTime:(n,t=Ko())=>Aa(Ot(Vo(),So(t),Jo(n))),zonedDateTimeISO:(n=Ko())=>Aa(Ot(Vo(),So(n),Fu)),plainDateTime:(n,t=Ko())=>qa(It(Go(Po(So(t))),Jo(n))),plainDateTimeISO:(n=Ko())=>qa(It(Go(Po(So(n))),Fu)),plainDate:(n,t=Ko())=>xa(Tt(Go(Po(So(t))),Jo(n))),plainDateISO:(n=Ko())=>xa(Tt(Go(Po(So(n))),Fu)),plainTimeISO:(n=Ko())=>Ba(Dt(Go(Po(So(n)))))})}),Pf=Object.defineProperties({},{...s("Temporal"),...u({PlainYearMonth:Za,PlainMonthDay:Sa,PlainDate:Ja,PlainTime:Wa,PlainDateTime:Ua,ZonedDateTime:La,Instant:Ef,Calendar:Mf,TimeZone:Ya,Duration:mf,Now:Df})});Object.defineProperties(globalThis,u({Temporal:Pf})),Object.defineProperties(Intl,u({DateTimeFormat:Tf})),Object.defineProperties(Date.prototype,u({toTemporalInstant(){return Of(Et(L(this.valueOf(),Vi)))}}))}();
