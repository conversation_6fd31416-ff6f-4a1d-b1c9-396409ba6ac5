import { decrypt<PERSON><PERSON><PERSON>uffer } from "./encryptionHelper";


/**
 * Safely decrypts a value that may be a string, Buffer, null, or undefined.
 * Returns null if value is falsy.
 */
export const safeDecrypt = (
    value: string | Buffer | Uint8Array | null | undefined
  ): string | null => {
    if (!value) return null;
  
    const bufferValue =
      Buffer.isBuffer(value) ? value : Buffer.from(value as Uint8Array);
  
    return decryptFromBuffer(bufferValue);
  };