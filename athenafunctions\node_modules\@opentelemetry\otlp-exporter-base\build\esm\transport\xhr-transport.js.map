{"version": 3, "file": "xhr-transport.js", "sourceRoot": "", "sources": ["../../../src/transport/xhr-transport.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;AAIH,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EACL,iBAAiB,EACjB,sBAAsB,GACvB,MAAM,wBAAwB,CAAC;AAOhC;IACE,sBAAoB,WAAiC;QAAjC,gBAAW,GAAX,WAAW,CAAsB;IAAG,CAAC;IAEzD,2BAAI,GAAJ,UAAK,IAAgB,EAAE,aAAqB;QAA5C,iBAqDC;QApDC,OAAO,IAAI,OAAO,CAAiB,UAAA,OAAO;YACxC,IAAM,GAAG,GAAG,IAAI,cAAc,EAAE,CAAC;YACjC,GAAG,CAAC,OAAO,GAAG,aAAa,CAAC;YAC5B,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,KAAI,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC;YACvC,IAAM,OAAO,GAAG,KAAI,CAAC,WAAW,CAAC,OAAO,EAAE,CAAC;YAC3C,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,OAAO,CAAC,UAAC,EAAM;oBAAN,KAAA,aAAM,EAAL,CAAC,QAAA,EAAE,CAAC,QAAA;gBACpC,GAAG,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;YAC7B,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,SAAS,GAAG,UAAA,CAAC;gBACf,OAAO,CAAC;oBACN,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,IAAI,KAAK,CAAC,uBAAuB,CAAC;iBAC1C,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,GAAG,CAAC,kBAAkB,GAAG;gBACvB,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,EAAE;oBAC1C,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;oBAC1B,OAAO,CAAC;wBACN,MAAM,EAAE,SAAS;qBAClB,CAAC,CAAC;iBACJ;qBAAM,IAAI,GAAG,CAAC,MAAM,IAAI,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE;oBACtD,OAAO,CAAC;wBACN,MAAM,EAAE,WAAW;wBACnB,aAAa,EAAE,sBAAsB,CACnC,GAAG,CAAC,iBAAiB,CAAC,aAAa,CAAC,CACrC;qBACF,CAAC,CAAC;iBACJ;qBAAM,IAAI,GAAG,CAAC,MAAM,KAAK,CAAC,EAAE;oBAC3B,OAAO,CAAC;wBACN,MAAM,EAAE,SAAS;wBACjB,KAAK,EAAE,IAAI,KAAK,CAAC,8CAA8C,CAAC;qBACjE,CAAC,CAAC;iBACJ;YACH,CAAC,CAAC;YAEF,GAAG,CAAC,OAAO,GAAG;gBACZ,OAAO,CAAC;oBACN,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,IAAI,KAAK,CAAC,qBAAqB,CAAC;iBACxC,CAAC,CAAC;YACL,CAAC,CAAC;YACF,GAAG,CAAC,OAAO,GAAG;gBACZ,OAAO,CAAC;oBACN,MAAM,EAAE,SAAS;oBACjB,KAAK,EAAE,IAAI,KAAK,CAAC,qBAAqB,CAAC;iBACxC,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACjB,CAAC,CAAC,CAAC;IACL,CAAC;IAED,+BAAQ,GAAR;QACE,2CAA2C;IAC7C,CAAC;IACH,mBAAC;AAAD,CAAC,AA7DD,IA6DC;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB,CAChC,UAAgC;IAEhC,OAAO,IAAI,YAAY,CAAC,UAAU,CAAC,CAAC;AACtC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { IExporterTransport } from '../exporter-transport';\nimport { ExportResponse } from '../export-response';\nimport { diag } from '@opentelemetry/api';\nimport {\n  isExportRetryable,\n  parseRetryAfterToMills,\n} from '../is-export-retryable';\n\nexport interface XhrRequestParameters {\n  url: string;\n  headers: () => Record<string, string>;\n}\n\nclass XhrTransport implements IExporterTransport {\n  constructor(private _parameters: XhrRequestParameters) {}\n\n  send(data: Uint8Array, timeoutMillis: number): Promise<ExportResponse> {\n    return new Promise<ExportResponse>(resolve => {\n      const xhr = new XMLHttpRequest();\n      xhr.timeout = timeoutMillis;\n      xhr.open('POST', this._parameters.url);\n      const headers = this._parameters.headers();\n      Object.entries(headers).forEach(([k, v]) => {\n        xhr.setRequestHeader(k, v);\n      });\n\n      xhr.ontimeout = _ => {\n        resolve({\n          status: 'failure',\n          error: new Error('XHR request timed out'),\n        });\n      };\n\n      xhr.onreadystatechange = () => {\n        if (xhr.status >= 200 && xhr.status <= 299) {\n          diag.debug('XHR success');\n          resolve({\n            status: 'success',\n          });\n        } else if (xhr.status && isExportRetryable(xhr.status)) {\n          resolve({\n            status: 'retryable',\n            retryInMillis: parseRetryAfterToMills(\n              xhr.getResponseHeader('Retry-After')\n            ),\n          });\n        } else if (xhr.status !== 0) {\n          resolve({\n            status: 'failure',\n            error: new Error('XHR request failed with non-retryable status'),\n          });\n        }\n      };\n\n      xhr.onabort = () => {\n        resolve({\n          status: 'failure',\n          error: new Error('XHR request aborted'),\n        });\n      };\n      xhr.onerror = () => {\n        resolve({\n          status: 'failure',\n          error: new Error('XHR request errored'),\n        });\n      };\n\n      xhr.send(data);\n    });\n  }\n\n  shutdown() {\n    // Intentionally left empty, nothing to do.\n  }\n}\n\n/**\n * Creates an exporter transport that uses XHR to send the data\n * @param parameters applied to each request made by transport\n */\nexport function createXhrTransport(\n  parameters: XhrRequestParameters\n): IExporterTransport {\n  return new XhrTransport(parameters);\n}\n"]}