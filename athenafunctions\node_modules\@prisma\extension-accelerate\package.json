{"name": "@prisma/extension-accelerate", "type": "module", "version": "2.0.1", "description": "Prisma Client extension for Accelerate", "sideEffects": false, "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "default": "./dist/index.cjs"}}}, "files": ["dist/**"], "devDependencies": {"@arethetypeswrong/cli": "^0.18.1", "@dotenvx/dotenvx": "^1.44.0", "@prisma/client": "^6.8.0-dev.47", "@prisma/dev": "^0.1.1", "@tsconfig/node-lts": "^22.0.1", "@tsconfig/node20": "^20.1.5", "@tsconfig/strictest": "^2.0.5", "@types/node": "^22.15.18", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^8.57.1", "execa": "^9.5.3", "prettier": "^3.5.3", "prisma": "^6.8.0-dev.47", "tsup": "^8.4.0", "tsx": "^4.19.4", "typescript": "^5.8.3"}, "peerDependencies": {"@prisma/client": ">=4.16.1"}, "engines": {"node": ">=18"}, "scripts": {"build": "tsup", "exports:check": "attw --pack .", "format": "prettier --write .", "format:check": "prettier --check .", "lint:check": "eslint src/** --max-warnings 0", "test": "dotenvx run -- node --import tsx --test test/*.test.ts", "types:check": "tsc --noEmit"}}