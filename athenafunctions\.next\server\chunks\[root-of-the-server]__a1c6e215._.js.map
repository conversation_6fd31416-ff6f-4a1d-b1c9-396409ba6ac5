{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/projects/EdisonAI/Code/JobSite/inngestapp/athenafunctions/src/inngest/client.ts"], "sourcesContent": ["import { Inngest } from \"inngest\";\r\n\r\n// Create a client to send and receive events\r\nexport const inngest = new Inngest({ id: \"athenafunctions\" });\r\n"], "names": [], "mappings": ";;;AAAA;;AAGO,MAAM,UAAU,IAAI,kIAAA,CAAA,UAAO,CAAC;IAAE,IAAI;AAAkB", "debugId": null}}, {"offset": {"line": 146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/projects/EdisonAI/Code/JobSite/inngestapp/athenafunctions/src/inngest/functions.ts"], "sourcesContent": ["import { inngest } from \"./client\";\r\n\r\nexport const helloWorld = inngest.createFunction(\r\n  { id: \"hello-world\" },\r\n  { event: \"test/hello.world\" },\r\n  async ({ event, step }) => {\r\n    await step.sleep(\"wait-a-moment\", \"1s\");\r\n    return { message: `Hello ${event.data.email}!` };\r\n  },\r\n);\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,MAAM,aAAa,0HAAA,CAAA,UAAO,CAAC,cAAc,CAC9C;IAAE,IAAI;AAAc,GACpB;IAAE,OAAO;AAAmB,GAC5B,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;IACpB,MAAM,KAAK,KAAK,CAAC,iBAAiB;IAClC,OAAO;QAAE,SAAS,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAAC;AACjD", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/projects/EdisonAI/Code/JobSite/inngestapp/athenafunctions/src/app/api/inngest/route.ts"], "sourcesContent": ["import { serve } from \"inngest/next\";\r\nimport { inngest } from \"../../../inngest/client\";\r\nimport { helloWorld } from \"@/inngest/functions\";\r\n\r\n// Create an API that serves zero functions\r\nexport const { GET, POST, PUT } = serve({\r\n  client: inngest,\r\n  functions: [\r\n    helloWorld\r\n  ],\r\n});\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAGO,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,QAAK,AAAD,EAAE;IACtC,QAAQ,0HAAA,CAAA,UAAO;IACf,WAAW;QACT,6HAAA,CAAA,aAAU;KACX;AACH", "debugId": null}}]}