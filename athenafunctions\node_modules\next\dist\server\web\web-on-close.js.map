{"version": 3, "sources": ["../../../src/server/web/web-on-close.ts"], "sourcesContent": ["/** Monitor when the consumer finishes reading the response body.\nthat's as close as we can get to `res.on('close')` using web APIs.\n*/\nexport function trackBodyConsumed(\n  body: string | ReadableStream,\n  onEnd: () => void\n): BodyInit {\n  if (typeof body === 'string') {\n    const generator = async function* generate() {\n      const encoder = new TextEncoder()\n      yield encoder.encode(body)\n      onEnd()\n    }\n    // @ts-expect-error BodyInit typings doesn't seem to include AsyncIterables even though it's supported in practice\n    return generator()\n  } else {\n    return trackStreamConsumed(body, onEnd)\n  }\n}\n\nexport function trackStreamConsumed<TChunk>(\n  stream: ReadableStream<TChunk>,\n  onEnd: () => void\n): ReadableStream<TChunk> {\n  // NOTE: This function must handle `stream` being aborted or cancelled,\n  // so it can't just be this:\n  //\n  //   return stream.pipeThrough(new TransformStream({ flush() { onEnd() } }))\n  //\n  // because that doesn't handle cancellations.\n  // (and cancellation handling via `Transformer.cancel` is only available in node >20)\n  const dest = new TransformStream()\n  const runOnEnd = () => onEnd()\n  stream.pipeTo(dest.writable).then(runOnEnd, runOnEnd)\n  return dest.readable\n}\n\nexport class CloseController {\n  private target = new EventTarget()\n  listeners = 0\n  isClosed = false\n\n  onClose(callback: () => void) {\n    if (this.isClosed) {\n      throw new Error('Cannot subscribe to a closed CloseController')\n    }\n\n    this.target.addEventListener('close', callback)\n    this.listeners++\n  }\n\n  dispatchClose() {\n    if (this.isClosed) {\n      throw new Error('Cannot close a CloseController multiple times')\n    }\n    if (this.listeners > 0) {\n      this.target.dispatchEvent(new Event('close'))\n    }\n    this.isClosed = true\n  }\n}\n"], "names": ["CloseController", "trackBodyConsumed", "trackStreamConsumed", "body", "onEnd", "generator", "generate", "encoder", "TextEncoder", "encode", "stream", "dest", "TransformStream", "runOnEnd", "pipeTo", "writable", "then", "readable", "onClose", "callback", "isClosed", "Error", "target", "addEventListener", "listeners", "dispatchClose", "dispatchEvent", "Event", "EventTarget"], "mappings": "AAAA;;AAEA;;;;;;;;;;;;;;;;IAmCaA,eAAe;eAAfA;;IAlCGC,iBAAiB;eAAjBA;;IAiBAC,mBAAmB;eAAnBA;;;AAjBT,SAASD,kBACdE,IAA6B,EAC7BC,KAAiB;IAEjB,IAAI,OAAOD,SAAS,UAAU;QAC5B,MAAME,YAAY,gBAAgBC;YAChC,MAAMC,UAAU,IAAIC;YACpB,MAAMD,QAAQE,MAAM,CAACN;YACrBC;QACF;QACA,kHAAkH;QAClH,OAAOC;IACT,OAAO;QACL,OAAOH,oBAAoBC,MAAMC;IACnC;AACF;AAEO,SAASF,oBACdQ,MAA8B,EAC9BN,KAAiB;IAEjB,uEAAuE;IACvE,4BAA4B;IAC5B,EAAE;IACF,4EAA4E;IAC5E,EAAE;IACF,6CAA6C;IAC7C,qFAAqF;IACrF,MAAMO,OAAO,IAAIC;IACjB,MAAMC,WAAW,IAAMT;IACvBM,OAAOI,MAAM,CAACH,KAAKI,QAAQ,EAAEC,IAAI,CAACH,UAAUA;IAC5C,OAAOF,KAAKM,QAAQ;AACtB;AAEO,MAAMjB;IAKXkB,QAAQC,QAAoB,EAAE;QAC5B,IAAI,IAAI,CAACC,QAAQ,EAAE;YACjB,MAAM,qBAAyD,CAAzD,IAAIC,MAAM,iDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAwD;QAChE;QAEA,IAAI,CAACC,MAAM,CAACC,gBAAgB,CAAC,SAASJ;QACtC,IAAI,CAACK,SAAS;IAChB;IAEAC,gBAAgB;QACd,IAAI,IAAI,CAACL,QAAQ,EAAE;YACjB,MAAM,qBAA0D,CAA1D,IAAIC,MAAM,kDAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QACA,IAAI,IAAI,CAACG,SAAS,GAAG,GAAG;YACtB,IAAI,CAACF,MAAM,CAACI,aAAa,CAAC,IAAIC,MAAM;QACtC;QACA,IAAI,CAACP,QAAQ,GAAG;IAClB;;aArBQE,SAAS,IAAIM;aACrBJ,YAAY;aACZJ,WAAW;;AAoBb"}