{"version": 3, "file": "Sum.js", "sourceRoot": "", "sources": ["../../../src/aggregator/Sum.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;AAEH,OAAO,EAEL,cAAc,GAIf,MAAM,SAAS,CAAC;AAEjB,OAAO,EACL,aAAa,GAGd,MAAM,sBAAsB,CAAC;AAI9B;IACE,yBACS,SAAiB,EACjB,SAAkB,EACjB,QAAoB,EACrB,KAAa;QADZ,yBAAA,EAAA,YAAoB;QACrB,sBAAA,EAAA,aAAa;QAHb,cAAS,GAAT,SAAS,CAAQ;QACjB,cAAS,GAAT,SAAS,CAAS;QACjB,aAAQ,GAAR,QAAQ,CAAY;QACrB,UAAK,GAAL,KAAK,CAAQ;IACnB,CAAC;IAEJ,gCAAM,GAAN,UAAO,KAAa;QAClB,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,GAAG,CAAC,EAAE;YAC/B,OAAO;SACR;QACD,IAAI,CAAC,QAAQ,IAAI,KAAK,CAAC;IACzB,CAAC;IAED,sCAAY,GAAZ,UAAa,SAAiB;QAC5B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;IAC7B,CAAC;IAED,sCAAY,GAAZ;QACE,OAAO,IAAI,CAAC,QAAQ,CAAC;IACvB,CAAC;IACH,sBAAC;AAAD,CAAC,AAtBD,IAsBC;;AAED,4EAA4E;AAC5E;IAGE,uBAAmB,SAAkB;QAAlB,cAAS,GAAT,SAAS,CAAS;QAF9B,SAAI,GAAuB,cAAc,CAAC,GAAG,CAAC;IAEb,CAAC;IAEzC,0CAAkB,GAAlB,UAAmB,SAAiB;QAClC,OAAO,IAAI,eAAe,CAAC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC;IACxD,CAAC;IAED;;OAEG;IACH,6BAAK,GAAL,UAAM,QAAyB,EAAE,KAAsB;QACrD,IAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACvC,IAAM,OAAO,GAAG,KAAK,CAAC,YAAY,EAAE,CAAC;QACrC,IAAI,KAAK,CAAC,KAAK,EAAE;YACf,OAAO,IAAI,eAAe,CACxB,KAAK,CAAC,SAAS,EACf,IAAI,CAAC,SAAS,EACd,OAAO,EACP,KAAK,CAAC,KAAK,CACZ,CAAC;SACH;QACD,OAAO,IAAI,eAAe,CACxB,QAAQ,CAAC,SAAS,EAClB,IAAI,CAAC,SAAS,EACd,MAAM,GAAG,OAAO,CACjB,CAAC;IACJ,CAAC;IAED;;OAEG;IACH,4BAAI,GAAJ,UAAK,QAAyB,EAAE,OAAwB;QACtD,IAAM,MAAM,GAAG,QAAQ,CAAC,YAAY,EAAE,CAAC;QACvC,IAAM,MAAM,GAAG,OAAO,CAAC,YAAY,EAAE,CAAC;QACtC;;;;WAIG;QACH,IAAI,IAAI,CAAC,SAAS,IAAI,MAAM,GAAG,MAAM,EAAE;YACrC,OAAO,IAAI,eAAe,CACxB,OAAO,CAAC,SAAS,EACjB,IAAI,CAAC,SAAS,EACd,MAAM,EACN,IAAI,CACL,CAAC;SACH;QACD,OAAO,IAAI,eAAe,CACxB,OAAO,CAAC,SAAS,EACjB,IAAI,CAAC,SAAS,EACd,MAAM,GAAG,MAAM,CAChB,CAAC;IACJ,CAAC;IAED,oCAAY,GAAZ,UACE,UAA4B,EAC5B,sBAA8C,EAC9C,wBAA+D,EAC/D,OAAe;QAEf,OAAO;YACL,UAAU,YAAA;YACV,sBAAsB,wBAAA;YACtB,aAAa,EAAE,aAAa,CAAC,GAAG;YAChC,UAAU,EAAE,wBAAwB,CAAC,GAAG,CAAC,UAAC,EAA0B;oBAA1B,KAAA,aAA0B,EAAzB,UAAU,QAAA,EAAE,YAAY,QAAA;gBACjE,OAAO;oBACL,UAAU,YAAA;oBACV,SAAS,EAAE,YAAY,CAAC,SAAS;oBACjC,OAAO,SAAA;oBACP,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE;iBACnC,CAAC;YACJ,CAAC,CAAC;YACF,WAAW,EAAE,IAAI,CAAC,SAAS;SAC5B,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AA7ED,IA6EC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Sum,\n  AggregatorKind,\n  Aggregator,\n  Accumulation,\n  AccumulationRecord,\n} from './types';\nimport { HrTime } from '@opentelemetry/api';\nimport {\n  DataPointType,\n  MetricDescriptor,\n  SumMetricData,\n} from '../export/MetricData';\nimport { Maybe } from '../utils';\nimport { AggregationTemporality } from '../export/AggregationTemporality';\n\nexport class SumAccumulation implements Accumulation {\n  constructor(\n    public startTime: HrTime,\n    public monotonic: boolean,\n    private _current: number = 0,\n    public reset = false\n  ) {}\n\n  record(value: number): void {\n    if (this.monotonic && value < 0) {\n      return;\n    }\n    this._current += value;\n  }\n\n  setStartTime(startTime: HrTime): void {\n    this.startTime = startTime;\n  }\n\n  toPointValue(): Sum {\n    return this._current;\n  }\n}\n\n/** Basic aggregator which calculates a Sum from individual measurements. */\nexport class SumAggregator implements Aggregator<SumAccumulation> {\n  public kind: AggregatorKind.SUM = AggregatorKind.SUM;\n\n  constructor(public monotonic: boolean) {}\n\n  createAccumulation(startTime: HrTime) {\n    return new SumAccumulation(startTime, this.monotonic);\n  }\n\n  /**\n   * Returns the result of the merge of the given accumulations.\n   */\n  merge(previous: SumAccumulation, delta: SumAccumulation): SumAccumulation {\n    const prevPv = previous.toPointValue();\n    const deltaPv = delta.toPointValue();\n    if (delta.reset) {\n      return new SumAccumulation(\n        delta.startTime,\n        this.monotonic,\n        deltaPv,\n        delta.reset\n      );\n    }\n    return new SumAccumulation(\n      previous.startTime,\n      this.monotonic,\n      prevPv + deltaPv\n    );\n  }\n\n  /**\n   * Returns a new DELTA aggregation by comparing two cumulative measurements.\n   */\n  diff(previous: SumAccumulation, current: SumAccumulation): SumAccumulation {\n    const prevPv = previous.toPointValue();\n    const currPv = current.toPointValue();\n    /**\n     * If the SumAggregator is a monotonic one and the previous point value is\n     * greater than the current one, a reset is deemed to be happened.\n     * Return the current point value to prevent the value from been reset.\n     */\n    if (this.monotonic && prevPv > currPv) {\n      return new SumAccumulation(\n        current.startTime,\n        this.monotonic,\n        currPv,\n        true\n      );\n    }\n    return new SumAccumulation(\n      current.startTime,\n      this.monotonic,\n      currPv - prevPv\n    );\n  }\n\n  toMetricData(\n    descriptor: MetricDescriptor,\n    aggregationTemporality: AggregationTemporality,\n    accumulationByAttributes: AccumulationRecord<SumAccumulation>[],\n    endTime: HrTime\n  ): Maybe<SumMetricData> {\n    return {\n      descriptor,\n      aggregationTemporality,\n      dataPointType: DataPointType.SUM,\n      dataPoints: accumulationByAttributes.map(([attributes, accumulation]) => {\n        return {\n          attributes,\n          startTime: accumulation.startTime,\n          endTime,\n          value: accumulation.toPointValue(),\n        };\n      }),\n      isMonotonic: this.monotonic,\n    };\n  }\n}\n"]}