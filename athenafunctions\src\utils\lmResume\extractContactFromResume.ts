export function extractContactFromResume(rawText: string): {
  name?: string;
  email?: string;
  phone?: string;
} {
  const lines = rawText.split('\n').map(line => line.trim()).filter(Boolean);
  const result: { name?: string; email?: string; phone?: string } = {};

  // 📧 Extract email
  const emailMatch = rawText.match(/[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-z]{2,}/i);
  if (emailMatch) result.email = emailMatch[0];

  // 📞 Extract phone
  const phoneMatch = rawText.match(/(\+?\d{1,2}[\s.-]?)?\(?\d{2,4}\)?[\s.-]?\d{3,4}[\s.-]?\d{4}/);
  if (phoneMatch) result.phone = phoneMatch[0];

  // 👤 Extract name (heuristic first)
  const maybeName = extractNameFromLines(lines);
  if (maybeName) {
    result.name = maybeName;
  } else {
    // const nerGuess = extractNEREntities(rawText); // Optional NER/LLM fallback
    // if (nerGuess) result.name = nerGuess;
  }

  return result;
}

function extractNameFromLines(lines: string[]): string | undefined {
  const clean = lines.filter(l => l.length <= 50 && /^[a-zA-Z\s'-]+$/.test(l));

  for (let i = 0; i < clean.length - 1; i++) {
    const first = clean[i];
    const second = clean[i + 1];
    if (
      /^[A-Z]{2,}$/.test(first) &&
      /^[A-Z]{2,}$/.test(second) &&
      first !== second
    ) {
      return capitalize(first) + ' ' + capitalize(second);
    }
  }

  const likelyName = clean.find(line => /^[A-Z][a-z]+ [A-Z][a-z]+$/.test(line));
  return likelyName || undefined;
}

function capitalize(s: string): string {
  return s.charAt(0).toUpperCase() + s.slice(1).toLowerCase();
}
