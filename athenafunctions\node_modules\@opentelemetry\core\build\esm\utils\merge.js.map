{"version": 3, "file": "merge.js", "sourceRoot": "", "sources": ["../../../src/utils/merge.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,uDAAuD;AAEvD,OAAO,EAAE,aAAa,EAAE,MAAM,gBAAgB,CAAC;AAE/C,IAAM,SAAS,GAAG,EAAE,CAAC;AAOrB;;;GAGG;AACH,MAAM,UAAU,KAAK;IAAC,cAAc;SAAd,UAAc,EAAd,qBAAc,EAAd,IAAc;QAAd,yBAAc;;IAClC,IAAI,MAAM,GAAQ,IAAI,CAAC,KAAK,EAAE,CAAC;IAC/B,IAAM,OAAO,GAA2C,IAAI,OAAO,EAGhE,CAAC;IACJ,OAAO,IAAI,CAAC,MAAM,GAAG,CAAC,EAAE;QACtB,MAAM,GAAG,eAAe,CAAC,MAAM,EAAE,IAAI,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;KAC5D;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED,SAAS,SAAS,CAAC,KAAU;IAC3B,IAAI,OAAO,CAAC,KAAK,CAAC,EAAE;QAClB,OAAO,KAAK,CAAC,KAAK,EAAE,CAAC;KACtB;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED;;;;;;;GAOG;AACH,SAAS,eAAe,CACtB,GAAQ,EACR,GAAQ,EACR,KAAS,EACT,OAAmC;IADnC,sBAAA,EAAA,SAAS;IAGT,IAAI,MAAW,CAAC;IAChB,IAAI,KAAK,GAAG,SAAS,EAAE;QACrB,OAAO,SAAS,CAAC;KAClB;IACD,KAAK,EAAE,CAAC;IACR,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,UAAU,CAAC,GAAG,CAAC,EAAE;QAC3D,MAAM,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC;KACzB;SAAM,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;QACvB,MAAM,GAAG,GAAG,CAAC,KAAK,EAAE,CAAC;QACrB,IAAI,OAAO,CAAC,GAAG,CAAC,EAAE;YAChB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC1C,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;aAChC;SACF;aAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACxB,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAC9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,CAAC,GAAG,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;aACnC;SACF;KACF;SAAM,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;QACxB,IAAI,QAAQ,CAAC,GAAG,CAAC,EAAE;YACjB,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE;gBAC1B,OAAO,GAAG,CAAC;aACZ;YACD,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC;YAChC,IAAM,IAAI,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;YAE9B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3C,IAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,IAAM,QAAQ,GAAG,GAAG,CAAC,GAAG,CAAC,CAAC;gBAE1B,IAAI,WAAW,CAAC,QAAQ,CAAC,EAAE;oBACzB,IAAI,OAAO,QAAQ,KAAK,WAAW,EAAE;wBACnC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpB;yBAAM;wBACL,qCAAqC;wBACrC,MAAM,CAAC,GAAG,CAAC,GAAG,QAAQ,CAAC;qBACxB;iBACF;qBAAM;oBACL,IAAM,IAAI,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC;oBACzB,IAAM,IAAI,GAAG,QAAQ,CAAC;oBAEtB,IACE,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC;wBACtC,mBAAmB,CAAC,GAAG,EAAE,GAAG,EAAE,OAAO,CAAC,EACtC;wBACA,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC;qBACpB;yBAAM;wBACL,IAAI,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE;4BACpC,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;4BACrC,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC;4BACrC,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;4BAC7B,IAAI,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,KAAA,EAAE,CAAC,CAAC;4BAC7B,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;4BACxB,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;yBACzB;wBAED,MAAM,CAAC,GAAG,CAAC,GAAG,eAAe,CAC3B,MAAM,CAAC,GAAG,CAAC,EACX,QAAQ,EACR,KAAK,EACL,OAAO,CACR,CAAC;qBACH;iBACF;aACF;SACF;aAAM;YACL,MAAM,GAAG,GAAG,CAAC;SACd;KACF;IAED,OAAO,MAAM,CAAC;AAChB,CAAC;AAED;;;;;GAKG;AACH,SAAS,mBAAmB,CAC1B,GAAQ,EACR,GAAW,EACX,OAAmC;IAEnC,IAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC;IACxC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;QAC1C,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,CAAC,CAAC;QACpB,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,IAAI,IAAI,CAAC,GAAG,KAAK,GAAG,EAAE;YACxC,OAAO,IAAI,CAAC;SACb;KACF;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AAED,SAAS,OAAO,CAAC,KAAU;IACzB,OAAO,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AAC9B,CAAC;AAED,SAAS,UAAU,CAAC,KAAU;IAC5B,OAAO,OAAO,KAAK,KAAK,UAAU,CAAC;AACrC,CAAC;AAED,SAAS,QAAQ,CAAC,KAAU;IAC1B,OAAO,CACL,CAAC,WAAW,CAAC,KAAK,CAAC;QACnB,CAAC,OAAO,CAAC,KAAK,CAAC;QACf,CAAC,UAAU,CAAC,KAAK,CAAC;QAClB,OAAO,KAAK,KAAK,QAAQ,CAC1B,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,KAAU;IAC7B,OAAO,CACL,OAAO,KAAK,KAAK,QAAQ;QACzB,OAAO,KAAK,KAAK,QAAQ;QACzB,OAAO,KAAK,KAAK,SAAS;QAC1B,OAAO,KAAK,KAAK,WAAW;QAC5B,KAAK,YAAY,IAAI;QACrB,KAAK,YAAY,MAAM;QACvB,KAAK,KAAK,IAAI,CACf,CAAC;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,GAAQ,EAAE,GAAQ;IACrC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,EAAE;QAC9C,OAAO,KAAK,CAAC;KACd;IAED,OAAO,IAAI,CAAC;AACd,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport { isPlainObject } from './lodash.merge';\n\nconst MAX_LEVEL = 20;\n\ninterface ObjectInto {\n  obj: any;\n  key: string;\n}\n\n/**\n * Merges objects together\n * @param args - objects / values to be merged\n */\nexport function merge(...args: any[]): any {\n  let result: any = args.shift();\n  const objects: WeakMap<any, ObjectInto[]> | undefined = new WeakMap<\n    any,\n    ObjectInto[]\n  >();\n  while (args.length > 0) {\n    result = mergeTwoObjects(result, args.shift(), 0, objects);\n  }\n\n  return result;\n}\n\nfunction takeValue(value: any): any {\n  if (isArray(value)) {\n    return value.slice();\n  }\n  return value;\n}\n\n/**\n * Merges two objects\n * @param one - first object\n * @param two - second object\n * @param level - current deep level\n * @param objects - objects holder that has been already referenced - to prevent\n * cyclic dependency\n */\nfunction mergeTwoObjects(\n  one: any,\n  two: any,\n  level = 0,\n  objects: WeakMap<any, ObjectInto[]>\n): any {\n  let result: any;\n  if (level > MAX_LEVEL) {\n    return undefined;\n  }\n  level++;\n  if (isPrimitive(one) || isPrimitive(two) || isFunction(two)) {\n    result = takeValue(two);\n  } else if (isArray(one)) {\n    result = one.slice();\n    if (isArray(two)) {\n      for (let i = 0, j = two.length; i < j; i++) {\n        result.push(takeValue(two[i]));\n      }\n    } else if (isObject(two)) {\n      const keys = Object.keys(two);\n      for (let i = 0, j = keys.length; i < j; i++) {\n        const key = keys[i];\n        result[key] = takeValue(two[key]);\n      }\n    }\n  } else if (isObject(one)) {\n    if (isObject(two)) {\n      if (!shouldMerge(one, two)) {\n        return two;\n      }\n      result = Object.assign({}, one);\n      const keys = Object.keys(two);\n\n      for (let i = 0, j = keys.length; i < j; i++) {\n        const key = keys[i];\n        const twoValue = two[key];\n\n        if (isPrimitive(twoValue)) {\n          if (typeof twoValue === 'undefined') {\n            delete result[key];\n          } else {\n            // result[key] = takeValue(twoValue);\n            result[key] = twoValue;\n          }\n        } else {\n          const obj1 = result[key];\n          const obj2 = twoValue;\n\n          if (\n            wasObjectReferenced(one, key, objects) ||\n            wasObjectReferenced(two, key, objects)\n          ) {\n            delete result[key];\n          } else {\n            if (isObject(obj1) && isObject(obj2)) {\n              const arr1 = objects.get(obj1) || [];\n              const arr2 = objects.get(obj2) || [];\n              arr1.push({ obj: one, key });\n              arr2.push({ obj: two, key });\n              objects.set(obj1, arr1);\n              objects.set(obj2, arr2);\n            }\n\n            result[key] = mergeTwoObjects(\n              result[key],\n              twoValue,\n              level,\n              objects\n            );\n          }\n        }\n      }\n    } else {\n      result = two;\n    }\n  }\n\n  return result;\n}\n\n/**\n * Function to check if object has been already reference\n * @param obj\n * @param key\n * @param objects\n */\nfunction wasObjectReferenced(\n  obj: any,\n  key: string,\n  objects: WeakMap<any, ObjectInto[]>\n): boolean {\n  const arr = objects.get(obj[key]) || [];\n  for (let i = 0, j = arr.length; i < j; i++) {\n    const info = arr[i];\n    if (info.key === key && info.obj === obj) {\n      return true;\n    }\n  }\n  return false;\n}\n\nfunction isArray(value: any): boolean {\n  return Array.isArray(value);\n}\n\nfunction isFunction(value: any): boolean {\n  return typeof value === 'function';\n}\n\nfunction isObject(value: any): boolean {\n  return (\n    !isPrimitive(value) &&\n    !isArray(value) &&\n    !isFunction(value) &&\n    typeof value === 'object'\n  );\n}\n\nfunction isPrimitive(value: any): boolean {\n  return (\n    typeof value === 'string' ||\n    typeof value === 'number' ||\n    typeof value === 'boolean' ||\n    typeof value === 'undefined' ||\n    value instanceof Date ||\n    value instanceof RegExp ||\n    value === null\n  );\n}\n\nfunction shouldMerge(one: any, two: any): boolean {\n  if (!isPlainObject(one) || !isPlainObject(two)) {\n    return false;\n  }\n\n  return true;\n}\n"]}