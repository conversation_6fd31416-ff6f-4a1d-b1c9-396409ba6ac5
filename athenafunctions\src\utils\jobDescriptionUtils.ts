export const transformJobDescriptionToTiptapJson = (
  jobDescriptionString: string
): object => {
  try {
    const parsedData = JSON.parse(jobDescriptionString);

    // Check if it's already a TipTap 'doc' format
    if (
      typeof parsedData === "object" &&
      parsedData !== null &&
      parsedData.type === "doc" &&
      Array.isArray(parsedData.content)
    ) {
      return parsedData; // Already in TipTap format, return as is
    }

    // Assume it's the "object with string arrays" format
    if (typeof parsedData === "object" && parsedData !== null) {
      const content: any[] = [];
      for (const key in parsedData) {
        if (Object.prototype.hasOwnProperty.call(parsedData, key)) {
          const items = parsedData[key];
          if (Array.isArray(items)) {
            content.push({
              type: "heading",
              attrs: { level: 3 },
              content: [{ type: "text", text: key }],
            });
            content.push({
              type: "bulletList",
              content: items.map((item) => ({
                type: "listItem",
                content: [
                  {
                    type: "paragraph",
                    content: [{ type: "text", text: String(item) }],
                  },
                ],
              })),
            });
          } else {
            content.push({
              type: "paragraph",
              content: [{ type: "text", text: `${key}: ${String(items)}` }],
            });
          }
        }
      }
      return { type: "doc", content: content };
    }
    // If it's a simple string or number after parsing, wrap it in a paragraph
    return {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [{ type: "text", text: String(parsedData) }],
        },
      ],
    };
  } catch (e) {
    console.error(
      "Failed to parse or transform jobDescription to TipTap JSON:",
      e
    );
    return {
      type: "doc",
      content: [
        {
          type: "paragraph",
          content: [
            {
              type: "text",
              text: "Error displaying job description. Please contact support.",
            },
          ],
        },
      ],
    };
  }
};
