export function refineFlatTextToCanonicalSections(flatText: string): Record<string, string> {
  const lines = flatText.split('\n').map(l => l.trim()).filter(Boolean);
  const sections: Record<string, string> = {};

  let currentSection = 'General';

  const keywords = [
    { trigger: /^department:/i, section: 'Department' },
    { trigger: /^employment:/i, section: 'Employment' },
    { trigger: /^salary:/i, section: 'Salary' },
    { trigger: /^(job )?description:?$/i, section: 'Job Description' },
    { trigger: /^what (we|you) (offer|get|can offer you)/i, section: 'Benefits' },
    { trigger: /^we believe.*fit here|requirements|qualifications/i, section: 'Requirements' },
  ];

  for (const line of lines) {
    const keyword = keywords.find(k => k.trigger.test(line));
    if (keyword) {
      currentSection = keyword.section;
      sections[currentSection] = '';
      continue;
    }

    sections[currentSection] = (sections[currentSection] ? sections[currentSection] + '\n' : '') + line;
  }

  return Object.fromEntries(
    Object.entries(sections).map(([section, text]) => [section, text.trim()])
  );
}
