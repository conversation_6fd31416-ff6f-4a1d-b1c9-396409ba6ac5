{"version": 3, "file": "SimpleLogRecordProcessor.js", "sourceRoot": "", "sources": ["../../../src/export/SimpleLogRecordProcessor.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGH,OAAO,EACL,cAAc,EACd,gBAAgB,EAChB,kBAAkB,EAClB,QAAQ,GACT,MAAM,qBAAqB,CAAC;AAK7B;IAIE,kCAA6B,SAA4B;QAA5B,cAAS,GAAT,SAAS,CAAmB;QACvD,IAAI,CAAC,aAAa,GAAG,IAAI,cAAc,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,CAAC;QAC9D,IAAI,CAAC,kBAAkB,GAAG,IAAI,GAAG,EAAiB,CAAC;IACrD,CAAC;IAEM,yCAAM,GAAb,UAAc,SAAoB;QAAlC,iBAuCC;;QAtCC,IAAI,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE;YAC/B,OAAO;SACR;QAED,IAAM,QAAQ,GAAG;YACf,OAAA,QAAQ;iBACL,OAAO,CAAC,KAAI,CAAC,SAAS,EAAE,CAAC,SAAS,CAAC,CAAC;iBACpC,IAAI,CAAC,UAAC,MAAoB;;gBACzB,IAAI,MAAM,CAAC,IAAI,KAAK,gBAAgB,CAAC,OAAO,EAAE;oBAC5C,kBAAkB,CAChB,MAAA,MAAM,CAAC,KAAK,mCACV,IAAI,KAAK,CACP,gEAA8D,MAAM,MAAG,CACxE,CACJ,CAAC;iBACH;YACH,CAAC,CAAC;iBACD,KAAK,CAAC,kBAAkB,CAAC;QAZ5B,CAY4B,CAAC;QAE/B,sFAAsF;QACtF,IAAI,SAAS,CAAC,QAAQ,CAAC,sBAAsB,EAAE;YAC7C,IAAM,eAAa,GAAG,MAAA,MAAA,SAAS,CAAC,QAAQ,EACrC,sBAAsB,mDACtB,IAAI,CAAC;gBACJ,uFAAuF;gBACvF,2EAA2E;gBAC3E,oEAAoE;gBACpE,KAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,eAAc,CAAC,CAAC;gBAC/C,OAAO,QAAQ,EAAE,CAAC;YACpB,CAAC,EAAE,kBAAkB,CAAC,CAAC;YAEzB,+BAA+B;YAC/B,IAAI,eAAa,IAAI,IAAI,EAAE;gBACzB,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,eAAa,CAAC,CAAC;aAC5C;SACF;aAAM;YACL,KAAK,QAAQ,EAAE,CAAC;SACjB;IACH,CAAC;IAEY,6CAAU,GAAvB;;;;;oBACE,8CAA8C;oBAC9C,qBAAM,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,CAAC,EAAA;;wBADtD,8CAA8C;wBAC9C,SAAsD,CAAC;;;;;KACxD;IAEM,2CAAQ,GAAf;QACE,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC;IACnC,CAAC;IAEO,4CAAS,GAAjB;QACE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;IACH,+BAAC;AAAD,CAAC,AA9DD,IA8DC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport type { ExportResult } from '@opentelemetry/core';\nimport {\n  BindOnceFuture,\n  ExportResultCode,\n  globalErrorHandler,\n  internal,\n} from '@opentelemetry/core';\nimport type { LogRecordExporter } from './LogRecordExporter';\nimport type { LogRecordProcessor } from '../LogRecordProcessor';\nimport type { LogRecord } from './../LogRecord';\n\nexport class SimpleLogRecordProcessor implements LogRecordProcessor {\n  private _shutdownOnce: BindOnceFuture<void>;\n  private _unresolvedExports: Set<Promise<void>>;\n\n  constructor(private readonly _exporter: LogRecordExporter) {\n    this._shutdownOnce = new BindOnceFuture(this._shutdown, this);\n    this._unresolvedExports = new Set<Promise<void>>();\n  }\n\n  public onEmit(logRecord: LogRecord): void {\n    if (this._shutdownOnce.isCalled) {\n      return;\n    }\n\n    const doExport = () =>\n      internal\n        ._export(this._exporter, [logRecord])\n        .then((result: ExportResult) => {\n          if (result.code !== ExportResultCode.SUCCESS) {\n            globalErrorHandler(\n              result.error ??\n                new Error(\n                  `SimpleLogRecordProcessor: log record export failed (status ${result})`\n                )\n            );\n          }\n        })\n        .catch(globalErrorHandler);\n\n    // Avoid scheduling a promise to make the behavior more predictable and easier to test\n    if (logRecord.resource.asyncAttributesPending) {\n      const exportPromise = logRecord.resource\n        .waitForAsyncAttributes?.()\n        .then(() => {\n          // Using TS Non-null assertion operator because exportPromise could not be null in here\n          // if waitForAsyncAttributes is not present this code will never be reached\n          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n          this._unresolvedExports.delete(exportPromise!);\n          return doExport();\n        }, globalErrorHandler);\n\n      // store the unresolved exports\n      if (exportPromise != null) {\n        this._unresolvedExports.add(exportPromise);\n      }\n    } else {\n      void doExport();\n    }\n  }\n\n  public async forceFlush(): Promise<void> {\n    // await unresolved resources before resolving\n    await Promise.all(Array.from(this._unresolvedExports));\n  }\n\n  public shutdown(): Promise<void> {\n    return this._shutdownOnce.call();\n  }\n\n  private _shutdown(): Promise<void> {\n    return this._exporter.shutdown();\n  }\n}\n"]}