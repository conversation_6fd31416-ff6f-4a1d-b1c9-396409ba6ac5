import { OTLPExporterBase, OTLPExporterNodeConfigBase } from '@opentelemetry/otlp-exporter-base';
import { ReadableLogRecord, LogRecordExporter } from '@opentelemetry/sdk-logs';
/**
 * OTLP Log Protobuf Exporter for Node.js
 */
export declare class OTLPLogExporter extends OTLPExporterBase<ReadableLogRecord[]> implements LogRecordExporter {
    constructor(config?: OTLPExporterNodeConfigBase);
}
//# sourceMappingURL=OTLPLogExporter.d.ts.map