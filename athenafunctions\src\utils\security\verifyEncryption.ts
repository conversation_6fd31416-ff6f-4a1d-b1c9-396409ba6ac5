import { prisma } from "@/lib/prisma/prismaClient";

export async function verifyUserEncryption(userId: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
  });

  if (!user) return { status: 'User not found' };

  const encryptionStatus = {
    email: Buffer.isBuffer(user.email),
    name: user.name ? Buffer.isBuffer(user.name) : 'field not set',
    firstName: user.firstName ? Buffer.isBuffer(user.firstName) : 'field not set',
    lastName: user.lastName ? Buffer.isBuffer(user.lastName) : 'field not set',
    image: user.image ? Buffer.isBuffer(user.image) : 'field not set',
  };

  return encryptionStatus;
}