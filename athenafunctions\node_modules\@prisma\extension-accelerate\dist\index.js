import l from"@prisma/client/scripts/default-index.js";function f(a,n){let[c=0,u=0,m=0]=a.split(".").map(Number),[o=0,h=0,i=0]=n.split(".").map(Number),r=o-c,e=h-u,t=i-m;return r||e||t}import w from"@prisma/client/scripts/default-index.js";function p(){let a=w.Prisma.prismaVersion;return[F(),`PrismaEngine/${a.engine}`,`PrismaClient/${a.client}`].join(" ")}function F(){return typeof navigator<"u"?navigator.userAgent:typeof process<"u"&&typeof process.versions<"u"?`Node/${process.versions.node} (${process.platform}; ${process.arch})`:"EdgeRuntime"in globalThis?"Vercel-Edge-Runtime":"UnknownRuntime"}var P="@prisma/extension-accelerate",x="Unable to connect to the Accelerate API. This may be due to a network or DNS issue. Please check your connection and the Accelerate connection string. For details, visit https://www.prisma.io/docs/accelerate/troubleshoot.";function b(a){let n=p(),c;return async u=>{let{args:m}=u,{cacheStrategy:o,__accelerateInfo:h=!1,...i}=m,r=null,{__internalParams:e,query:t}=u;return e.customDataProxyFetch=()=>async(s,d)=>{let A=new Array;typeof o?.ttl=="number"&&A.push(`max-age=${o.ttl}`),typeof o?.swr=="number"&&A.push(`stale-while-revalidate=${o.swr}`);let y=o?.tags?.join(",")??"";d.headers={...d.headers,"cache-control":A.length>0?A.join(","):"no-cache","user-agent":n,...y.length>0?{"accelerate-cache-tags":y}:{}},c&&(d.headers["accelerate-query-engine-jwt"]=c);try{let g=await a(s,d);return r={cacheStatus:g.headers.get("accelerate-cache-status"),lastModified:new Date(g.headers.get("last-modified")??""),region:g.headers.get("cf-ray")?.split("-")[1]??"unspecified",requestId:g.headers.get("cf-ray")??"unspecified",signature:g.headers.get("accelerate-signature")??"unspecified"},c=g.headers.get("accelerate-query-engine-jwt")??void 0,g}catch{throw new Error(x)}},h?{data:await t(i,e),info:r}:t(i,e)}}function T(a){let n=f("5.1.0",l.Prisma.prismaVersion.client)>=0;return l.Prisma.defineExtension(c=>{let{apiKeyPromise:u,baseURL:m}=S(c),o=b(a);async function h(r){let e=await u;if(!e)return{requestId:"unspecified"};let t;try{t=await a(new URL("/invalidate",m).href,{method:"POST",headers:{authorization:`Bearer ${e}`,"content-type":"application/json"},body:JSON.stringify(r)})}catch{throw new Error(x)}if(!t?.ok){let s=await t.text();throw new Error(`Failed to invalidate Accelerate cache. Response was ${t.status} ${t.statusText}. ${s}`)}return t.body?.cancel(),{requestId:t.headers.get("cf-ray")??"unspecified"}}let i=c.$extends({name:P,query:{$allModels:{$allOperations:o}}});return i.$extends({name:P,client:{$accelerate:{invalidate:r=>h(r),invalidateAll:()=>h({tags:"all"})}},model:{$allModels:{aggregate(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.aggregate(r);return Object.assign(s,{withAccelerateInfo(){return t.aggregate({...r,__accelerateInfo:!0})}})},count(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.count(r);return Object.assign(s,{withAccelerateInfo(){return t.count({...r,__accelerateInfo:!0})}})},findFirst(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.findFirst(r);return Object.assign(s,{withAccelerateInfo(){return t.findFirst({...r,__accelerateInfo:!0})}})},findFirstOrThrow(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.findFirstOrThrow(r);return Object.assign(s,{withAccelerateInfo(){return t.findFirstOrThrow({...r,__accelerateInfo:!0})}})},findMany(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.findMany(r);return Object.assign(s,{withAccelerateInfo(){return t.findMany({...r,__accelerateInfo:!0})}})},findUnique(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.findUnique(r);return Object.assign(s,{withAccelerateInfo(){return t.findUnique({...r,__accelerateInfo:!0})}})},findUniqueOrThrow(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.findUniqueOrThrow(r);return Object.assign(s,{withAccelerateInfo(){return t.findUniqueOrThrow({...r,__accelerateInfo:!0})}})},groupBy(r){let e=l.Prisma.getExtensionContext(this),t=n?e.$parent[e.$name]:i[e.name],s=t.groupBy(r);return Object.assign(s,{withAccelerateInfo(){return t.groupBy({...r,__accelerateInfo:!0})}})}}}})})}function S(a){let n=Reflect.get(a,"_accelerateEngineConfig");try{let{host:c,hostname:u,protocol:m,searchParams:o}=new URL(n?.accelerateUtils?.resolveDatasourceUrl?.(n));if(m==="prisma+postgres:"&&(u==="localhost"||u==="127.0.0.1"))return{apiKeyPromise:Promise.resolve(o.get("api_key")),baseURL:new URL(`http://${c}`)}}catch{}return{apiKeyPromise:a._engine.start().then(()=>a._engine.apiKey?.()??null),baseURL:new URL("https://accelerate.prisma-data.net")}}function k(a){let n=a?.fetch??fetch;return T(n)}export{x as FETCH_FAILURE_MESSAGE,T as makeAccelerateExtension,k as withAccelerate};
