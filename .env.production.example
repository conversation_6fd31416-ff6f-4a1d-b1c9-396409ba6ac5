# Production Environment Configuration

# Inngest Configuration (REQUIRED)
INNGEST_EVENT_KEY=your-production-event-key-here
INNGEST_SIGNING_KEY=your-production-signing-key-here

# Next.js Production Configuration
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com

# Inngest Production Settings
INNGEST_LOG_LEVEL=info
INNGEST_CONCURRENCY=10

# Redis Configuration (automatically set by docker-compose)
# REDIS_URL=redis://redis:6379

# Optional: Custom Redis settings
# REDIS_MAX_CONNECTIONS=100
# REDIS_TIMEOUT=5000

# Security (if needed)
# INNGEST_WEBHOOK_SECRET=your-webhook-secret
# API_SECRET_KEY=your-api-secret

# Monitoring (if using external services)
# SENTRY_DSN=your-sentry-dsn
# DATADOG_API_KEY=your-datadog-key
