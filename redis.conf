# Redis Production Configuration

# Persistence
save 900 1      # Save if at least 1 key changed in 900 seconds
save 300 10     # Save if at least 10 keys changed in 300 seconds  
save 60 10000   # Save if at least 10000 keys changed in 60 seconds

# AOF (Append Only File) for better durability
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# Memory management
maxmemory-policy allkeys-lru
maxmemory 256mb

# Security
protected-mode yes
bind 0.0.0.0
port 6379

# Logging
loglevel notice
logfile ""

# Performance
tcp-keepalive 300
timeout 0

# Disable dangerous commands in production
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""
rename-command CONFIG ""
rename-command SHUTDOWN SHUTDOWN_REDIS_SERVER

# Client connections
maxclients 10000
