"use server";

import bcrypt from "bcryptjs";
import { createHash } from 'crypto';

// The same input to always give the same output
export async function hashEmail(email: string): Promise<string> {
    // Synchronous operation wrapped in an async function
    return createHash('sha256').update(email).digest('hex');
}

// The same input to always give the same output
export async function hashUnhashString(str: string): Promise<string> {
    // Synchronous operation wrapped in an async function
    return createHash('sha256').update(str).digest('hex');
}
  
  // Using bcrypt.hash for hashing an email for lookup won't work because bcrypt is designed for password hashing and is non‑deterministic—it generates a unique salt each time, meaning the same input will produce a different hash every time.
  //For comparing emails deterministically, you need to use a hashing function that always produces the same output for the same input.
export async function hashString(unHashedString: string) {
    return await bcrypt.hash(unHashedString, 12);
}

export async function compareHashedString(unHashedString: string, hashedString: string) {
    const isValid: boolean = await bcrypt.compare(unHashedString, hashedString);
    return isValid;
}

