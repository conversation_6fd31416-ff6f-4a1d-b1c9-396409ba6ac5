import { OTLPMetricExporterOptions } from '../../OTLPMetricExporterOptions';
import { OTLPMetricExporterBase } from '../../OTLPMetricExporterBase';
import { OTLPExporterConfigBase } from '@opentelemetry/otlp-exporter-base';
/**
 * Collector Metric Exporter for Web
 */
export declare class OTLPMetricExporter extends OTLPMetricExporterBase {
    constructor(config?: OTLPExporterConfigBase & OTLPMetricExporterOptions);
}
//# sourceMappingURL=OTLPMetricExporter.d.ts.map