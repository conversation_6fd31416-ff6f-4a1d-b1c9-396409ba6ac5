"use strict";
/**
 * An adapter for Express to serve and register any declared functions with
 * Inngest, making them available to be triggered by events.
 *
 * @example
 * ```ts
 * import { serve } from "inngest/express";
 * import { inngest } from "./src/inngest/client";
 * import fnA from "./src/inngest/fnA"; // Your own function
 *
 * // Important:  ensure you add JSON middleware to process incoming JSON POST payloads.
 * app.use(express.json());
 * app.use(
 *   // Expose the middleware on our recommended path at `/api/inngest`.
 *   "/api/inngest",
 *   serve({ client: inngest, functions: [fnA] })
 * );
 * ```
 *
 * @module
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.serve = exports.frameworkName = void 0;
const InngestCommHandler_js_1 = require("./components/InngestCommHandler.js");
/**
 * The name of the framework, used to identify the framework in Inngest
 * dashboards and during testing.
 */
exports.frameworkName = "express";
/**
 * Serve and register any declared functions with Inngest, making them available
 * to be triggered by events.
 *
 * The return type is currently `any` to ensure there's no required type matches
 * between the `express` and `vercel` packages. This may change in the future to
 * appropriately infer.
 *
 * @example
 * ```ts
 * import { serve } from "inngest/express";
 * import { inngest } from "./src/inngest/client";
 * import fnA from "./src/inngest/fnA"; // Your own function
 *
 * // Important:  ensure you add JSON middleware to process incoming JSON POST payloads.
 * app.use(express.json());
 * app.use(
 *   // Expose the middleware on our recommended path at `/api/inngest`.
 *   "/api/inngest",
 *   serve({ client: inngest, functions: [fnA] })
 * );
 * ```
 *
 * @public
 */
// Has explicit return type to avoid JSR-defined "slow types"
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const serve = (options) => {
    const handler = new InngestCommHandler_js_1.InngestCommHandler(Object.assign(Object.assign({ frameworkName: exports.frameworkName }, options), { handler: (req, res) => {
            return {
                // eslint-disable-next-line @typescript-eslint/no-unsafe-return
                body: () => req.body,
                headers: (key) => {
                    const header = req.headers[key];
                    return Array.isArray(header) ? header[0] : header;
                },
                method: () => req.method || "GET",
                url: () => {
                    // `req.hostname` can filter out port numbers; beware!
                    const hostname = req.headers["host"] || (options === null || options === void 0 ? void 0 : options.serveHost);
                    const protocol = (hostname === null || hostname === void 0 ? void 0 : hostname.includes("://"))
                        ? ""
                        : `${req.protocol || "https"}://`;
                    const url = new URL(req.originalUrl || req.url || "", `${protocol}${hostname || ""}`);
                    return url;
                },
                queryString: (key) => {
                    const qs = req.query[key];
                    return Array.isArray(qs) ? qs[0] : qs;
                },
                transformResponse: ({ body, headers, status }) => {
                    for (const [name, value] of Object.entries(headers)) {
                        res.setHeader(name, value);
                    }
                    return res.status(status).send(body);
                },
                /**
                 * Express doesn't support a Web API `ReadableStream` being written as
                 * the response body (only `node:stream` `ReadableStream`s), so we
                 * manually read the stream we're given and call `res.write()` for each
                 * chunk.
                 *
                 * See {@link https://github.com/expressjs/discussions/issues/288}.
                 *
                 * Alternatively, we could pipe this through a transform and create a
                 * Node stream, but the feels more dangerous than just writing directly
                 * to the response.
                 */
                transformStreamingResponse: async ({ body, headers, status }) => {
                    for (const [name, value] of Object.entries(headers)) {
                        res.setHeader(name, value);
                    }
                    res.status(status);
                    const reader = body.getReader();
                    try {
                        // Keep writing from the stream until it's done
                        let done = false;
                        while (!done) {
                            const result = await reader.read();
                            done = result.done;
                            if (!done) {
                                res.write(result.value);
                            }
                        }
                        res.end();
                    }
                    catch (error) {
                        if (error instanceof Error) {
                            res.destroy(error);
                        }
                        else {
                            res.destroy(new Error(String(error)));
                        }
                    }
                },
            };
        } }));
    return handler.createHandler();
};
exports.serve = serve;
//# sourceMappingURL=express.js.map