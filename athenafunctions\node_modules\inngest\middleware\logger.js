"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProxyLogger = exports.DefaultLogger = void 0;
class DefaultLogger {
    info(...args) {
        console.info(...args);
    }
    warn(...args) {
        console.warn(...args);
    }
    error(...args) {
        console.error(...args);
    }
    debug(...args) {
        console.debug(...args);
    }
}
exports.DefaultLogger = DefaultLogger;
/**
 * ProxyLogger aims to provide a thin wrapper on user's provided logger.
 * It's expected to be turned on and off based on the function execution
 * context, so it doesn't result in duplicated logging.
 *
 * And also attempt to allow enough time for the logger to flush all logs.
 *
 * @public
 */
class ProxyLogger {
    constructor(logger) {
        this.enabled = false;
        this.logger = logger;
        // Return a Proxy to forward arbitrary property access to the underlying
        // logger. For example, if the user provides a logger that has a `foo`
        // method, they can call `foo` on the ProxyLogger and it will call the
        // underlying logger's `foo` method.
        return new Proxy(this, {
            get(target, prop, receiver) {
                // Handle ProxyLogger's own methods/properties.
                if (prop in target) {
                    return Reflect.get(target, prop, receiver);
                }
                // Forward property access to the underlying logger.
                return Reflect.get(target.logger, prop, receiver);
            },
        });
    }
    info(...args) {
        if (!this.enabled)
            return;
        this.logger.info(...args);
    }
    warn(...args) {
        if (!this.enabled)
            return;
        this.logger.warn(...args);
    }
    error(...args) {
        if (!this.enabled)
            return;
        this.logger.error(...args);
    }
    debug(...args) {
        // there are loggers that don't implement "debug" by default
        if (!this.enabled || !(typeof this.logger.debug === "function"))
            return;
        this.logger.debug(...args);
    }
    enable() {
        this.enabled = true;
    }
    disable() {
        this.enabled = false;
    }
    async flush() {
        // Allow 1s for the provided logger to handle flushing since the ones that do
        // flushing usually has some kind of timeout of up to 1s.
        //
        // TODO:
        // This should only happen when using a serverless environment because it's very
        // costly from the compute perspective.
        // server runtimes should just let the logger do their thing since most of them
        // should have already figured what to do in those environments, be it threading or
        // something else.
        if (this.logger.constructor.name !== DefaultLogger.name) {
            await new Promise((resolve) => {
                setTimeout(() => resolve(null), 1000);
            });
        }
    }
}
exports.ProxyLogger = ProxyLogger;
//# sourceMappingURL=logger.js.map