/// <reference types="node" />
import * as http from 'http';
import * as https from 'https';
import { HttpRequestParameters } from './http-transport-types';
import { ExportResponse } from '../export-response';
/**
 * Sends data using http
 * @param params
 * @param agent
 * @param data
 * @param onDone
 * @param timeoutMillis
 */
export declare function sendWithHttp(params: HttpRequestParameters, agent: http.Agent | https.Agent, data: Uint8Array, onDone: (response: ExportResponse) => void, timeoutMillis: number): void;
export declare function compressAndSend(req: http.ClientRequest, compression: 'gzip' | 'none', data: Uint8Array, onError: (error: Error) => void): void;
export declare function createHttpAgent(rawUrl: string, agentOptions: http.AgentOptions | https.AgentOptions): http.Agent;
//# sourceMappingURL=http-transport-utils.d.ts.map