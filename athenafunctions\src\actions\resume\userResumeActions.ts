"use server";

import {
  GetAllResumeRawTextData,
} from "@/data/user/resume";
// import { v4 as uuidv4 } from "uuid";
import { ensureServerActionProtection } from "@/lib/arcjet/protection";

export const GetAllResumeRawText = async () => {

    // Arcjet protection
    await ensureServerActionProtection();

  try {
    // Fetch all resumes
    const resumes = await GetAllResumeRawTextData();

    if (!resumes) {
      return {
        error: "Error! Failed to fetch resume data.",
        data: null,
      };
    }

    return {
      success: "Success! Resume data retrieved.",
      data: resumes,
    };
  } catch (error) {
    console.error("Error fetching resume raw text:", error);
    return {
      error: `Error fetching resume data: ${error}`,
      data: null,
    };
  }
};