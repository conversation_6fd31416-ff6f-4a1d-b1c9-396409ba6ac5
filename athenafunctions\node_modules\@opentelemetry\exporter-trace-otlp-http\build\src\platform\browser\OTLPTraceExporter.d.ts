import { ReadableSpan, SpanExporter } from '@opentelemetry/sdk-trace-base';
import { OTLPExporterConfigBase, OTLPExporterBase } from '@opentelemetry/otlp-exporter-base';
/**
 * Collector Trace Exporter for Web
 */
export declare class OTLPTraceExporter extends OTLPExporterBase<ReadableSpan[]> implements SpanExporter {
    constructor(config?: OTLPExporterConfigBase);
}
//# sourceMappingURL=OTLPTraceExporter.d.ts.map