{"version": 3, "file": "logger.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/logger.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;GAWG;AACH,MAAM,MAAM,MAAM,GAAG,OAAO,CAAC;AAE7B;;;;GAIG;AACH,MAAM,WAAW,MAAM;IACrB,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAC9B,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAC9B,KAAK,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAC/B,KAAK,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;CAChC;AAED,qBAAa,aAAc,YAAW,MAAM;IAC1C,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;IAItB,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;IAItB,KAAK,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;IAIvB,KAAK,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;CAGxB;AAED;;;;;;;;GAQG;AACH,qBAAa,WAAY,YAAW,MAAM;IACxC,OAAO,CAAC,QAAQ,CAAC,MAAM,CAAS;IAChC,OAAO,CAAC,OAAO,CAAS;gBAEZ,MAAM,EAAE,MAAM;IAoB1B,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;IAKtB,IAAI,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;IAKtB,KAAK,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;IAKvB,KAAK,CAAC,GAAG,IAAI,EAAE,MAAM,EAAE;IAMvB,MAAM;IAIN,OAAO;IAID,KAAK;CAgBZ"}