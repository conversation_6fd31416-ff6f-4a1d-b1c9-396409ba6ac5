{"version": 3, "file": "WithTraceExemplarFilter.js", "sourceRoot": "", "sources": ["../../../src/exemplar/WithTraceExemplarFilter.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAGL,kBAAkB,EAClB,KAAK,EACL,UAAU,GAEX,MAAM,oBAAoB,CAAC;AAG5B;IAAA;IAWA,CAAC;IAVC,8CAAY,GAAZ,UACE,KAAa,EACb,SAAiB,EACjB,UAAsB,EACtB,GAAY;QAEZ,IAAM,WAAW,GAAG,KAAK,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC;QAC9C,IAAI,CAAC,WAAW,IAAI,CAAC,kBAAkB,CAAC,WAAW,CAAC;YAAE,OAAO,KAAK,CAAC;QACnE,OAAO,WAAW,CAAC,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IACpE,CAAC;IACH,8BAAC;AAAD,CAAC,AAXD,IAWC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  Context,\n  HrTime,\n  isSpanContextValid,\n  trace,\n  TraceFlags,\n  Attributes,\n} from '@opentelemetry/api';\nimport { ExemplarFilter } from './ExemplarFilter';\n\nexport class WithTraceExemplarFilter implements ExemplarFilter {\n  shouldSample(\n    value: number,\n    timestamp: HrTime,\n    attributes: Attributes,\n    ctx: Context\n  ): boolean {\n    const spanContext = trace.getSpanContext(ctx);\n    if (!spanContext || !isSpanContextValid(spanContext)) return false;\n    return spanContext.traceFlags & TraceFlags.SAMPLED ? true : false;\n  }\n}\n"]}