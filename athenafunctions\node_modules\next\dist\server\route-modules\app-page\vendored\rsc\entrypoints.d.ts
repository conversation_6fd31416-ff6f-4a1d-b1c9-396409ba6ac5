import * as React from 'react';
import * as <PERSON><PERSON><PERSON><PERSON> from 'react-dom';
import * as ReactJsxDevRuntime from 'react/jsx-dev-runtime';
import * as ReactJsxRuntime from 'react/jsx-runtime';
import * as ReactCompilerRuntime from 'react/compiler-runtime';
declare let ReactServerDOMTurbopackServerEdge: any, ReactServerDOMWebpackServerEdge: any;
declare let ReactServerDOMTurbopackServerNode: any, ReactServerDOMWebpackServerNode: any;
declare let ReactServerDOMTurbopackStaticEdge: any, ReactServerDOMWebpackStaticEdge: any;
export { React, ReactJsxDevRuntime, ReactJsxRuntime, ReactCompilerRuntime, ReactDOM, ReactServerDOMWebpackServerEdge, ReactServerDOMTurbopackServerEdge, ReactServerDOMWebpackServerNode, ReactServerDOMTurbopackServerNode, ReactServerD<PERSON>WebpackStaticEdge, ReactServerDOMTurbopackStaticEdge, };
