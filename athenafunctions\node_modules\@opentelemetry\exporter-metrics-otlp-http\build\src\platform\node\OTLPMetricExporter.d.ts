import { OTLPMetricExporterOptions } from '../../OTLPMetricExporterOptions';
import { OTLPMetricExporterBase } from '../../OTLPMetricExporterBase';
import { OTLPExporterNodeConfigBase } from '@opentelemetry/otlp-exporter-base';
/**
 * OTLP Metric Exporter for Node.js
 */
export declare class OTLPMetricExporter extends OTLPMetricExporterBase {
    constructor(config?: OTLPExporterNodeConfigBase & OTLPMetricExporterOptions);
}
//# sourceMappingURL=OTLPMetricExporter.d.ts.map