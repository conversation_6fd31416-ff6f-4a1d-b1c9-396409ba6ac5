{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../../src/detectors/platform/node/index.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAC9C,OAAO,EAAE,gBAAgB,EAAE,MAAM,oBAAoB,CAAC;AACtD,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC1C,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAClD,OAAO,EAAE,eAAe,EAAE,MAAM,mBAAmB,CAAC;AACpD,OAAO,EAAE,mBAAmB,EAAE,MAAM,uBAAuB,CAAC;AAC5D,OAAO,EAAE,6BAA6B,EAAE,MAAM,iCAAiC,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { hostDetector } from './HostDetector';\nexport { hostDetectorSync } from './HostDetectorSync';\nexport { osDetector } from './OSDetector';\nexport { osDetectorSync } from './OSDetectorSync';\nexport { processDetector } from './ProcessDetector';\nexport { processDetectorSync } from './ProcessDetectorSync';\nexport { serviceInstanceIdDetectorSync } from './ServiceInstanceIdDetectorSync';\n"]}