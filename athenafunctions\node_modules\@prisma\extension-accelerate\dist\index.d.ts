import * as PrismaDefault from '@prisma/client/scripts/default-index.js';
import PrismaDefault__default, { Prisma } from '@prisma/client/scripts/default-index.js';
import { Types } from '@prisma/client/runtime/library.js';
import * as _prisma_client_runtime_library from '@prisma/client/runtime/library';

declare const FETCH_FAILURE_MESSAGE = "Unable to connect to the Accelerate API. This may be due to a network or DNS issue. Please check your connection and the Accelerate connection string. For details, visit https://www.prisma.io/docs/accelerate/troubleshoot.";
interface PrismaCacheStrategy {
    /**
     * Specifies the caching parameters for Accelerate.
     *
     * `cacheStrategy` only applies when used with an Accelerate connection string.
     */
    readonly cacheStrategy?: {
        /**
         * `swr` is short for Stale-While-Revalidate.
         *
         * `swr` defines the number of seconds that Accelerate may serve _stale_ cache data.
         * _Stale_ data is a cache hit, but the cache will be refreshed in the background by Accelerate.
         * The Prisma operation will not be blocked while data is refreshed.
         *
         * Use `swr` to reduce the latency of accessing the data while still maintaining
         * a more up-to-date value in the Accelerate cache.
         * `swr` without `ttl` will not reduce database load since Accelerate will
         * run the query in the background.
         *
         * `swr` can be combined with `ttl`.
         * `swr` applies **after** `ttl` has expired.
         * The total number of seconds data will be cached is `ttl + swr`.
         *
         * `swr` only applies when used with an Accelerate connection string.
         */
        readonly swr?: number;
        /**
         * `ttl` is short for Time-to-Live.
         *
         * `ttl` defines the number of seconds that Accelerate may serve _fresh_ cache data.
         * _Fresh_ data is a cache hit and will not execute the query against the database.
         *
         * Use `ttl` to reduce database load and latency for data that does not
         * require frequent updates.
         * `ttl` without `swr` will incur a blocking database query for the first
         * request after `ttl` expires.
         *
         * It's recommended to combine `ttl` and `swr` to maintain low latency while
         * Accelerate refreshes cached data in the background.
         * `swr` applies **after** `ttl` has expired.
         * The total number of seconds data will be cached is `ttl + swr`.
         *
         * `ttl` only applies when used with an Accelerate connection string.
         */
        readonly ttl?: number;
        /**
         * `tags` allow you to attach string values to the query's cache entry
         * that can later be used to invalidate the cache.
         *
         * A tag may only contain alphanumeric characters and underscores.
         * Each tag may contain a maximum of 64 characters.
         * A maximum of 5 tags are allowed per query.
         */
        readonly tags?: ReadonlyArray<string>;
    };
}
type PrismaFetch = (url: string, options: {
    body?: string;
    method?: string;
    headers: Record<string, string>;
}) => Promise<Response>;
interface AccelerateInfo {
    /**
     * The cache status of the response.
     * * `ttl` indicates a cache hit within the `ttl` duration and no database query was executed
     * * `swr` indicates a cache hit within the `swr` duration and the data is being refreshed by Accelerate in the background
     * * `miss` indicates that both `ttl` and `swr` have expired and the database query was executed by the request
     * * `none` indicates that no cache strategy was specified and the database query was executed by the request
     */
    cacheStatus: "ttl" | "swr" | "miss" | "none";
    /**
     * The date the response was last refreshed.
     */
    lastModified: Date;
    /**
     * The datacenter region that received the request.
     */
    region: string;
    /**
     * Unique identifier of the request. Useful for troubleshooting.
     */
    requestId: string;
    /**
     * The unique signature of the Prisma operation.
     */
    signature: string;
}
type AccelerateInvalidateInput = {
    /** Invalidate cache by tags set in the query `cacheStrategy`. */
    tags: ReadonlyArray<string>;
};
interface AcceleratePromise<T> extends Prisma.PrismaPromise<T> {
    withAccelerateInfo(): Prisma.PrismaPromise<{
        data: T;
        info: AccelerateInfo | null;
    }>;
}
type ArgsWithCacheStrategy<Model, Operation extends Types.Public.Operation> = Prisma.Args<Model, Operation> & PrismaCacheStrategy;
type Subset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
};
type SelectSubset<T, U> = {
    [key in keyof T]: key extends keyof U ? T[key] : never;
} & (T extends {
    select: unknown;
    include: unknown;
} ? "Please either choose `select` or `include`." : T extends {
    select: unknown;
    omit: unknown;
} ? "Please either choose `select` or `omit`." : unknown);
declare function makeAccelerateExtension(fetcher: PrismaFetch): (client: any) => PrismaDefault__default.PrismaClientExtends<Types.Extensions.InternalArgs<{}, {
    $allModels: {
        aggregate<This, FormalArgs extends ArgsWithCacheStrategy<This, "aggregate">, ActualArgs extends FormalArgs>(this: This, args: Subset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "aggregate">>;
        count<This, FormalArgs extends ArgsWithCacheStrategy<This, "count">, ActualArgs extends FormalArgs>(this: This, args?: Subset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "count">>;
        findFirst<This, FormalArgs extends ArgsWithCacheStrategy<This, "findFirst">, ActualArgs extends FormalArgs>(this: This, args?: SelectSubset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "findFirst"> | null>;
        findFirstOrThrow<This, FormalArgs extends ArgsWithCacheStrategy<This, "findFirstOrThrow">, ActualArgs extends FormalArgs>(this: This, args?: SelectSubset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "findFirstOrThrow">>;
        findMany<This, FormalArgs extends ArgsWithCacheStrategy<This, "findMany">, ActualArgs extends FormalArgs>(this: This, args?: SelectSubset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "findMany">>;
        findUnique<This, FormalArgs extends ArgsWithCacheStrategy<This, "findUnique">, ActualArgs extends FormalArgs>(this: This, args: SelectSubset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "findUnique"> | null>;
        findUniqueOrThrow<This, FormalArgs extends ArgsWithCacheStrategy<This, "findUniqueOrThrow">, ActualArgs extends FormalArgs>(this: This, args: SelectSubset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "findUniqueOrThrow">>;
        groupBy<This, FormalArgs extends ArgsWithCacheStrategy<This, "groupBy">, ActualArgs extends FormalArgs>(this: This, args: Subset<ActualArgs, FormalArgs>): AcceleratePromise<Prisma.Result<This, ActualArgs, "groupBy">>;
    };
}, {}, {
    $accelerate: {
        /**
         * Initiates an invalidation request for the specified cache tag values.
         *
         * A tag may only contain alphanumeric characters and underscores.
         * Each tag may contain a maximum of 64 characters.
         * A maximum of 5 tags may be invalidated per call.
         */
        invalidate: (input: AccelerateInvalidateInput) => Promise<{
            requestId: string;
        }>;
        /**
         * Initiates an invalidation request of all cache entries for this
         * environment.
         */
        invalidateAll: () => Promise<{
            requestId: string;
        }>;
    };
}> & Types.Extensions.InternalArgs<{}, {}, {}, {}> & Types.Extensions.DefaultArgs>;

interface WithAccelerateOptions {
    fetch?(this: void, input: RequestInfo | URL, init?: RequestInit): Promise<Response>;
}
declare function withAccelerate(options?: WithAccelerateOptions): (client: any) => PrismaDefault.PrismaClientExtends<_prisma_client_runtime_library.InternalArgs<{}, {
    $allModels: {
        aggregate<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "aggregate"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; }): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "aggregate">>;
        count<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "count"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args?: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; }): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "count">>;
        findFirst<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "findFirst"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args?: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; } & (ActualArgs extends {
            select: unknown;
            include: unknown;
        } ? "Please either choose `select` or `include`." : ActualArgs extends {
            select: unknown;
            omit: unknown;
        } ? "Please either choose `select` or `omit`." : unknown)): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "findFirst"> | null>;
        findFirstOrThrow<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "findFirstOrThrow"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args?: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; } & (ActualArgs extends {
            select: unknown;
            include: unknown;
        } ? "Please either choose `select` or `include`." : ActualArgs extends {
            select: unknown;
            omit: unknown;
        } ? "Please either choose `select` or `omit`." : unknown)): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "findFirstOrThrow">>;
        findMany<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "findMany"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args?: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; } & (ActualArgs extends {
            select: unknown;
            include: unknown;
        } ? "Please either choose `select` or `include`." : ActualArgs extends {
            select: unknown;
            omit: unknown;
        } ? "Please either choose `select` or `omit`." : unknown)): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "findMany">>;
        findUnique<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "findUnique"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; } & (ActualArgs extends {
            select: unknown;
            include: unknown;
        } ? "Please either choose `select` or `include`." : ActualArgs extends {
            select: unknown;
            omit: unknown;
        } ? "Please either choose `select` or `omit`." : unknown)): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "findUnique"> | null>;
        findUniqueOrThrow<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "findUniqueOrThrow"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; } & (ActualArgs extends {
            select: unknown;
            include: unknown;
        } ? "Please either choose `select` or `include`." : ActualArgs extends {
            select: unknown;
            omit: unknown;
        } ? "Please either choose `select` or `omit`." : unknown)): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "findUniqueOrThrow">>;
        groupBy<This, FormalArgs extends _prisma_client_runtime_library.Args<This, "groupBy"> & PrismaCacheStrategy, ActualArgs extends FormalArgs>(this: This, args: { [key in keyof ActualArgs]: key extends keyof FormalArgs ? ActualArgs[key] : never; }): AcceleratePromise<_prisma_client_runtime_library.Result<This, ActualArgs, "groupBy">>;
    };
}, {}, {
    $accelerate: {
        invalidate: (input: AccelerateInvalidateInput) => Promise<{
            requestId: string;
        }>;
        invalidateAll: () => Promise<{
            requestId: string;
        }>;
    };
}> & _prisma_client_runtime_library.InternalArgs<{}, {}, {}, {}> & _prisma_client_runtime_library.DefaultArgs>;

export { type AccelerateInfo, type AccelerateInvalidateInput, type AcceleratePromise, FETCH_FAILURE_MESSAGE, type PrismaCacheStrategy, type WithAccelerateOptions, makeAccelerateExtension, withAccelerate };
