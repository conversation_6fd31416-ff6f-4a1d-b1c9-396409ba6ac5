{"name": "serialize-error-cjs", "version": "0.1.4", "description": "", "main": "dist/index.js", "scripts": {"test": "npx -y ts-node test/main.ts", "build": "node build.js", "check": "npm run check:eslint", "check:eslint": "eslint src --ext .js,.jsx,.ts,.tsx --max-warnings 0", "postpublish": "PACKAGE_VERSION=$(node -p \"require('./package.json').version\") PACKAGE_NAME=$(node -p \"require('./package.json').name\") && npm deprecate \"${PACKAGE_NAME}@<${PACKAGE_VERSION}\" \"Rolling release, please update to ${PACKAGE_VERSION}\""}, "files": ["dist/"], "keywords": ["error", "serialize", "stringify", "convert", "deserialize"], "repository": {"type": "git", "url": "git+https://github.com/finwo/serialize-error-cjs"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://finwo.nl"}, "funding": "https://github.com/sponsors/finwo", "license": "MIT-0", "devDependencies": {"@types/node": "^22.13.1", "@types/tape": "^5.8.1", "@typescript-eslint/eslint-plugin": "^5.35.1", "@typescript-eslint/parser": "^5.35.1", "esbuild": "^0.25.0", "esbuild-plugin-d.ts": "^1.3.1", "eslint": "^8.22.0", "tape": "^5.9.0"}}