#!/bin/bash

# Function to handle shutdown gracefully
cleanup() {
    echo "🛑 Shutting down services..."
    kill $NEXTJS_PID $INNGEST_PID 2>/dev/null
    exit 0
}

# Set up signal handlers
trap cleanup SIGTERM SIGINT

# Start Next.js development server in the background
echo "🚀 Starting Next.js development server on port 3000..."
cd /app
npm run dev &
NEXTJS_PID=$!

# Wait for Next.js to be ready
echo "⏳ Waiting for Next.js to be ready..."
until curl -sf http://localhost:3000 > /dev/null; do
  sleep 2
done

echo "✅ Next.js is ready on port 3000."

# Start Inngest OSS Runtime in the background with auto-sync
echo "🚀 Starting Inngest Runtime with auto-sync on port 8288..."
# Use --sdk-url flag to automatically sync the function server
inngest start --event-key "$INNGEST_EVENT_KEY" --signing-key "$INNGEST_SIGNING_KEY" --sdk-url "http://localhost:3000/api/inngest" &
INNGEST_PID=$!

# Wait until the Inngest runtime is healthy
echo "⏳ Waiting for <PERSON>ges<PERSON> to be ready..."
until curl -sf http://localhost:8288/healthz > /dev/null; do
  sleep 1
done

echo "✅ Inngest is ready on port 8288."

# Function server should auto-sync via --sdk-url flag
echo "📡 Function server configured for auto-sync"
echo "🔗 Function server URL: http://localhost:3000/api/inngest"
echo "🌐 Next.js app available at: http://localhost:3000"
echo "🌐 Inngest UI available at: http://localhost:8288"
echo "📱 Apps page: http://localhost:8288/apps"
echo ""
echo "✅ Both services are running in the same container!"
echo "✅ If auto-sync doesn't work, you can manually sync via the UI"

# Keep container alive and wait for both processes
wait $NEXTJS_PID $INNGEST_PID
