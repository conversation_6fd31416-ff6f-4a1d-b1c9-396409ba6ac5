#!/bin/bash

# Start Inngest OSS Runtime in the background with auto-sync
echo "🚀 Starting Inngest Runtime with auto-sync..."
# Use --sdk-url flag to automatically sync the function server
inngest start --event-key "$INNGEST_EVENT_KEY" --signing-key "$INNGEST_SIGNING_KEY" --sdk-url "$INNGEST_FUNCTION_SERVER_URL" &

# Wait until the Inngest runtime is healthy
echo "⏳ Waiting for Inngest to be ready..."
until curl -sf http://localhost:8288/healthz > /dev/null; do
  sleep 1
done

echo "✅ Inngest is ready."

# Function server should auto-sync via --sdk-url flag
echo "📡 Function server configured for auto-sync"
echo "🔗 Function server URL: $INNGEST_FUNCTION_SERVER_URL"
echo "🌐 Inngest UI available at: http://localhost:8288"
echo "📱 Apps page: http://localhost:8288/apps"
echo ""
echo "✅ If auto-sync doesn't work, you can manually sync via the UI"

echo "✅ Successfully registered function server."

# Keep container alive
wait
