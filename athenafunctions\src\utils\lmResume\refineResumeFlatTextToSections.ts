export type ResumeSections = {
  Summary?: string;
  WorkExperience?: ExperienceEntry[];
  Education?: EducationEntry[];
  Skills?: string[];
  Certifications?: string[];
  Projects?: string[];
  Awards?: string[];
  Languages?: string[];
  Hobbies?: string[];
  VolunteerExperience?: string[];
  References?: string[];
  General?: string;
};

export type ExperienceEntry = {
  title?: string;
  organization?: string;
  startDate?: string;
  endDate?: string;
  roleSummary?: string;
  responsibilities?: string[];
};

export type EducationEntry = {
  institution?: string;
  degree?: string;
  startDate?: string;
  endDate?: string;
};

function extractYear(text?: string): string | undefined {
  const match = text?.match(/(\d{4})/);
  return match?.[1];
}

function extractEndYear(text?: string): string | undefined {
  const match = text?.match(/(?:–|-|to)\s*(Present|\d{4})/i);
  return match?.[1];
}

function extractDateRange(text: string): [string?, string?] {
  const matchRange = text.match(/(\d{4})\s?(?:–|-|to)\s?(Present|\d{4})/i);
  if (matchRange) return [matchRange[1], matchRange[2]];

  const years = [...text.matchAll(/\b(19|20)\d{2}\b/g)].map(m => m[0]);
  if (years.length === 1) return [undefined, years[0]];
  if (years.length >= 2) return [years[0], years[1]];
  return [];
}


function normalize(s: string): string {
  return s.toLowerCase().replace(/[^a-z\s]/g, '').trim();
}

function parseList(text: string): string[] {
  return text.split(/[\n,•●\-]+/).map(s => s.trim()).filter(Boolean);
}

export function refineResumeFlatTextToSections(text: string): ResumeSections {
  const sectionMappings: Record<string, keyof ResumeSections> = {
    summary: 'Summary',
    professionalsummary: 'Summary',
    executivesummary: 'Summary',
    profile: 'Summary',
    objective: 'Summary',
    careerobjective: 'Summary',
    personalstatement: 'Summary',
    workexperience: 'WorkExperience',
    professionalexperience: 'WorkExperience',
    employmenthistory: 'WorkExperience',
    careerexperience: 'WorkExperience',
    experience: 'WorkExperience',
    careerhistory: 'WorkExperience',
    history: 'WorkExperience',
    education: 'Education',
    academicbackground: 'Education',
    academicqualifications: 'Education',
    educationalbackground: 'Education',
    qualifications: 'Education',
    trainingcertifications: 'Certifications',
    certifications: 'Certifications',
    licenses: 'Certifications',
    certificates: 'Certifications',
    skills: 'Skills',
    keyskills: 'Skills',
    technicalskills: 'Skills',
    corecompetencies: 'Skills',
    areasofexpertise: 'Skills',
    projects: 'Projects',
    notableprojects: 'Projects',
    awards: 'Awards',
    achievements: 'Awards',
    honors: 'Awards',
    recognition: 'Awards',
    languages: 'Languages',
    languageproficiency: 'Languages',
    interests: 'Hobbies',
    hobbies: 'Hobbies',
    personalinterests: 'Hobbies',
    publications: 'Projects',
    research: 'Projects',
    articles: 'Projects',
    volunteerexperience: 'VolunteerExperience',
    communityinvolvement: 'VolunteerExperience',
    volunteering: 'VolunteerExperience',
    references: 'References',
    referees: 'References',
    recommendations: 'References'
  };

  const lines = text.split('\n');
  const sections: ResumeSections = {};
  let currentKey: keyof ResumeSections = 'General';
  let buffer: string[] = [];

  const flushBuffer = () => {
  const raw = buffer.join('\n').trim();
  if (!raw) return;

  try {
    switch (currentKey) {
      case 'WorkExperience':
        sections.WorkExperience = parseExperience(raw);
        break;
      case 'Education':
        sections.Education = parseEducation(raw);
        break;
      case 'Skills':
      case 'Awards':
      case 'Hobbies':
      case 'Languages':
      case 'Certifications':
      case 'VolunteerExperience':
      case 'Projects':
      case 'References':
        sections[currentKey] = parseList(raw);
        break;
      case 'Summary':
        sections.Summary = raw;
        break;
      default:
        sections.General = (sections.General || '') + '\n' + raw;
        break;
    }

    // ✅ Fallback injection in case the parser silently failed
    // Graceful fallback if parsers return empty or undefined entries
    if (currentKey === 'WorkExperience' && (!sections.WorkExperience || sections.WorkExperience.length === 0)) {
    sections.WorkExperience = [{
        title: 'Untitled Role',
        organization: 'Unknown',
        roleSummary: raw,
        responsibilities: []
    }];
    }

    if (currentKey === 'Education' && (!sections.Education || sections.Education.length === 0)) {
    sections.Education = [{
        institution: 'Unknown Institution',
        degree: raw,
        startDate: undefined,
        endDate: undefined
    }];
    }


  } catch {
    sections.General = (sections.General || '') + '\n' + raw;
  }
  

  buffer = [];
};

  for (const line of lines) {
    const norm = normalize(line);
    const maybeNewKey = sectionMappings[norm];
    if (maybeNewKey && maybeNewKey !== currentKey) {
      flushBuffer();
      currentKey = maybeNewKey;
      continue; // skip adding heading line to buffer
    }

    buffer.push(line);
  }

  flushBuffer();
  return sections;
}

function parseExperience(text: string): ExperienceEntry[] {
  return text.split(/\n{2,}/).map(block => {
    const lines = block.split('\n').map(l => l.trim()).filter(Boolean);
    const joined = lines.join(' ');

    const [startDate, endDate] = extractDateRange(joined);

    const bullets = lines.filter(l => /^[•●\-]/.test(l));
    const contentLines = bullets.length > 0 ? bullets : lines.slice(2);

    const isPIILine = (line: string) =>
      /(?:email|phone|address|@\w+)|\d{3}[-.\s]?\d{3}[-.\s]?\d{4}|\d{5}/i.test(line);

    const filteredLines = contentLines.filter(l => !isPIILine(l));

    const roleSummary = filteredLines.slice(0, 2).join(' ').replace(/\s+/g, ' ').trim();

    return {
      title: lines[0] || 'Untitled Role',
      organization: lines[1] || 'Unknown Organization',
      startDate,
      endDate,
      roleSummary,
      responsibilities: filteredLines.map(line => line.replace(/^[•●\-]\s*/, '').trim())
    };
  });
}

function parseEducation(text: string): EducationEntry[] {
  return text.split(/\n{2,}/).map(block => {
    const lines = block.split('\n').map(l => l.trim()).filter(Boolean);
    const [institution = '', degree = ''] = lines;
    const [startDate, endDate] = extractDateRange(lines.join(' '));
    return { institution, degree, startDate, endDate };
  });
}
