import { format, formatDistance, formatRelative } from "date-fns";

export function formatDate(date: Date | string) {
  const dateObject = typeof date === 'string' ? new Date(date) : date;
  return format(dateObject, 'MMMM dd, yyyy');
}

export function formatDateTime(date: Date | string) {
  const dateObject = typeof date === 'string' ? new Date(date) : date;
  return format(dateObject, 'MMMM dd, yyyy HH:mm');
}

export function formatRelativeToNow(date: Date | string) {
  const dateObject = typeof date === 'string' ? new Date(date) : date;
  return formatDistance(dateObject, new Date(), { addSuffix: true });
}

export function formatRelativeDate(date: Date | string, baseDate: Date = new Date()) {
  const dateObject = typeof date === 'string' ? new Date(date) : date;
  return formatRelative(dateObject, baseDate);
}