{"version": 3, "file": "transform.js", "sourceRoot": "", "sources": ["../../src/transform.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0CAA0C;AAE1C,8CAA2D;AAC3D,uCAAuC;AAEvC,MAAM,wBAAwB,GAAG;IAC/B,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM;IAClD,CAAC,GAAG,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,MAAM;IAClD,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,QAAQ;IACtD,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,QAAQ,CAAC,QAAQ;IACtD,kCAAkC;IAClC,CAAC,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,SAAS;CACnC,CAAC;AAEW,QAAA,wBAAwB,GAAG,kBAAkB,CAAC;AAC9C,QAAA,yBAAyB,GAAG,OAAO,CAAC;AAEjD;;;GAGG;AACH,SAAgB,YAAY,CAC1B,IAAkB,EAClB,WAAmB,EACnB,iBAAyB,EACzB,kBAA0B;IAE1B,MAAM,UAAU,GAAqB;QACnC,OAAO,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,OAAO;QACnC,QAAQ,EAAE,IAAI,CAAC,YAAY;QAC3B,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC,MAAM;QAC7B,IAAI,EAAE,wBAAwB,CAAC,IAAI,CAAC,IAAI,CAAC;QACzC,SAAS,EAAE,IAAA,2BAAoB,EAAC,IAAI,CAAC,SAAS,CAAC;QAC/C,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,IAAA,2BAAoB,EAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QACzD,aAAa,EAAE,EAAE,WAAW,EAAE;QAC9B,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,iBAAiB,EAAE,kBAAkB,CAAC;QAChE,WAAW,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;YAC7B,CAAC,CAAC,oBAAoB,CAAC,IAAI,CAAC,MAAM,CAAC;YACnC,CAAC,CAAC,SAAS;KACd,CAAC;IAEF,OAAO,UAAU,CAAC;AACpB,CAAC;AAtBD,oCAsBC;AAED,oEAAoE;AACpE,SAAgB,aAAa,CAC3B,EACE,UAAU,EACV,QAAQ,EACR,MAAM,EACN,sBAAsB,EACtB,kBAAkB,EAClB,iBAAiB,GACJ,EACf,iBAAyB,EACzB,kBAA0B;IAE1B,MAAM,IAAI,GAA8B,EAAE,CAAC;IAC3C,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,EAAE;QACzC,IAAI,CAAC,GAAG,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,CAAC;KACrC;IACD,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,cAAc,CAAC,KAAK,EAAE;QAC5C,IAAI,CAAC,iBAAiB,CAAC,GAAG,MAAM,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC;KACnE;IACD,IAAI,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,cAAc,CAAC,KAAK,IAAI,MAAM,CAAC,OAAO,EAAE;QAC9D,IAAI,CAAC,kBAAkB,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC;KAC3C;IACD,yCAAyC;IACzC,IAAI,sBAAsB,EAAE;QAC1B,IAAI,CAAC,+BAA+B,CAAC,GAAG,MAAM,CAAC,sBAAsB,CAAC,CAAC;KACxE;IAED,qCAAqC;IACrC,IAAI,kBAAkB,EAAE;QACtB,IAAI,CAAC,2BAA2B,CAAC,GAAG,MAAM,CAAC,kBAAkB,CAAC,CAAC;KAChE;IAED,oCAAoC;IACpC,IAAI,iBAAiB,EAAE;QACrB,IAAI,CAAC,0BAA0B,CAAC,GAAG,MAAM,CAAC,iBAAiB,CAAC,CAAC;KAC9D;IAED,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,OAAO,CACtC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CACzD,CAAC;IAEF,OAAO,IAAI,CAAC;AACd,CAAC;AA1CD,sCA0CC;AAED;;GAEG;AACH,SAAgB,oBAAoB,CAClC,MAAoB;IAEpB,OAAO,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;QAC1B,SAAS,EAAE,IAAI,CAAC,KAAK,CAAC,IAAA,2BAAoB,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QACvD,KAAK,EAAE,KAAK,CAAC,IAAI;KAClB,CAAC,CAAC,CAAC;AACN,CAAC;AAPD,oDAOC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport * as api from '@opentelemetry/api';\nimport { ReadableSpan, TimedEvent } from '@opentelemetry/sdk-trace-base';\nimport { hrTimeToMicroseconds } from '@opentelemetry/core';\nimport * as zipkinTypes from './types';\n\nconst ZIPKIN_SPAN_KIND_MAPPING = {\n  [api.SpanKind.CLIENT]: zipkinTypes.SpanKind.CLIENT,\n  [api.SpanKind.SERVER]: zipkinTypes.SpanKind.SERVER,\n  [api.SpanKind.CONSUMER]: zipkinTypes.SpanKind.CONSUMER,\n  [api.SpanKind.PRODUCER]: zipkinTypes.SpanKind.PRODUCER,\n  // When absent, the span is local.\n  [api.SpanKind.INTERNAL]: undefined,\n};\n\nexport const defaultStatusCodeTagName = 'otel.status_code';\nexport const defaultStatusErrorTagName = 'error';\n\n/**\n * Translate OpenTelemetry ReadableSpan to ZipkinSpan format\n * @param span Span to be translated\n */\nexport function toZipkinSpan(\n  span: ReadableSpan,\n  serviceName: string,\n  statusCodeTagName: string,\n  statusErrorTagName: string\n): zipkinTypes.Span {\n  const zipkinSpan: zipkinTypes.Span = {\n    traceId: span.spanContext().traceId,\n    parentId: span.parentSpanId,\n    name: span.name,\n    id: span.spanContext().spanId,\n    kind: ZIPKIN_SPAN_KIND_MAPPING[span.kind],\n    timestamp: hrTimeToMicroseconds(span.startTime),\n    duration: Math.round(hrTimeToMicroseconds(span.duration)),\n    localEndpoint: { serviceName },\n    tags: _toZipkinTags(span, statusCodeTagName, statusErrorTagName),\n    annotations: span.events.length\n      ? _toZipkinAnnotations(span.events)\n      : undefined,\n  };\n\n  return zipkinSpan;\n}\n\n/** Converts OpenTelemetry Span properties to Zipkin Tags format. */\nexport function _toZipkinTags(\n  {\n    attributes,\n    resource,\n    status,\n    droppedAttributesCount,\n    droppedEventsCount,\n    droppedLinksCount,\n  }: ReadableSpan,\n  statusCodeTagName: string,\n  statusErrorTagName: string\n): zipkinTypes.Tags {\n  const tags: { [key: string]: string } = {};\n  for (const key of Object.keys(attributes)) {\n    tags[key] = String(attributes[key]);\n  }\n  if (status.code !== api.SpanStatusCode.UNSET) {\n    tags[statusCodeTagName] = String(api.SpanStatusCode[status.code]);\n  }\n  if (status.code === api.SpanStatusCode.ERROR && status.message) {\n    tags[statusErrorTagName] = status.message;\n  }\n  /* Add droppedAttributesCount as a tag */\n  if (droppedAttributesCount) {\n    tags['otel.dropped_attributes_count'] = String(droppedAttributesCount);\n  }\n\n  /* Add droppedEventsCount as a tag */\n  if (droppedEventsCount) {\n    tags['otel.dropped_events_count'] = String(droppedEventsCount);\n  }\n\n  /* Add droppedLinksCount as a tag */\n  if (droppedLinksCount) {\n    tags['otel.dropped_links_count'] = String(droppedLinksCount);\n  }\n\n  Object.keys(resource.attributes).forEach(\n    name => (tags[name] = String(resource.attributes[name]))\n  );\n\n  return tags;\n}\n\n/**\n * Converts OpenTelemetry Events to Zipkin Annotations format.\n */\nexport function _toZipkinAnnotations(\n  events: TimedEvent[]\n): zipkinTypes.Annotation[] {\n  return events.map(event => ({\n    timestamp: Math.round(hrTimeToMicroseconds(event.time)),\n    value: event.name,\n  }));\n}\n"]}