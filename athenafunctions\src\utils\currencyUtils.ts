"use server";

import { GetUserLocation } from "@/actions/user/getUser";
import { prisma } from "@/lib/prisma/prismaClient"; // Import Prisma client

export interface CurrencyData {
  exchangeRate: number; // Exchange rate against a base currency (e.g., USD)
  currencySymbol: string; // e.g., $, €, ₱
  currencyCode: string; // ISO 4217 currency code, e.g., USD, EUR, PHP
  locale: string; // BCP 47 language tag, e.g., en-US, de-DE, en-PH
}

/**
 * Fetches local currency data primarily from the cached exchange rates in the database.
 * Assumes a separate process updates the CachedExchangeRate table periodically (e.g., daily).
 * @param targetCurrencyCode The ISO 4217 code for the target currency (e.g., "PHP").
 * @param baseCurrencyCode The ISO 4217 code for the base currency (default "USD").
 * @returns Promise<CurrencyData>
 */
export async function getLocalCurrencyData(
  targetCurrencyCode: string,
  baseCurrencyCode: string = "USD"
): Promise<CurrencyData> {
  let currentTargetCurrencyCode = targetCurrencyCode; // Initialize here, will be used throughout

  try {
    console.log(`UTILS/getLocalCurrencyData: Entry. targetCurrencyCode: '${targetCurrencyCode}', baseCurrencyCode: '${baseCurrencyCode}'`);
    if (targetCurrencyCode === baseCurrencyCode) { // Check against the original targetCurrencyCode
        console.log(`UTILS/getLocalCurrencyData: Target (${currentTargetCurrencyCode}) is same as base (${baseCurrencyCode}). Attempting to derive a more specific currency.`);
        let derivedCountryCode: string | null = null;

        // 1. Attempt GeoIP lookup via our API
        try {
            // Construct the full URL for the fetch call if running on the server
            // This assumes your app is accessible at process.env.NEXT_PUBLIC_APP_URL
            // or you might need a different way to determine the base URL for server-side fetch.
            // For simplicity, if NEXT_PUBLIC_APP_URL is not set, it will try a relative path,
            // which might only work if this server action is somehow proxied by the same host.
            // A more robust solution for server-to-server fetch might involve an internal service call
            // or ensuring NEXT_PUBLIC_APP_URL is correctly set.
            const appUrl = process.env.NEXT_PUBLIC_APP_URL || '';
            const geoApiUrl = `${appUrl}/api/geo`;
            console.log(`UTILS/getLocalCurrencyData: Attempting GeoIP via ${geoApiUrl}.`);
            const geoApiResponse = await fetch(geoApiUrl); 
            if (geoApiResponse.ok) {
                const geoData = await geoApiResponse.json();
                if (geoData.country) {
                    derivedCountryCode = geoData.country.toUpperCase();
                    console.log(`UTILS/getLocalCurrencyData: Derived country from /api/geo (service: ${geoData.service_used || 'unknown'}): ${derivedCountryCode}.`);
                } else {
                    console.log(`UTILS/getLocalCurrencyData: /api/geo did not return a country. Response: ${JSON.stringify(geoData).substring(0,200)}`);
                }
            } else {
                console.warn(`UTILS/getLocalCurrencyData: /api/geo call failed with status: ${geoApiResponse.status}`);
            }
        } catch (geoIpError) {
            console.error("UTILS/getLocalCurrencyData: Error during GeoIP lookup:", geoIpError);
        }

        // 2. If no country from GeoIP, try registered user location (for logged-in users)
        if (!derivedCountryCode) {
            console.log(`UTILS/getLocalCurrencyData: No country from GeoIP, attempting registered user location.`);
            try {
                const userLocation = await GetUserLocation(); // This returns a two-letter country code, null, or an error object
                if (typeof userLocation === 'string' && userLocation.length === 2) {
                    derivedCountryCode = userLocation.toUpperCase();
                    console.log(`UTILS/getLocalCurrencyData: Using registered location: ${derivedCountryCode}`);
                } else {
                    console.log(`UTILS/getLocalCurrencyData: Registered user location not available or not a valid string ('${JSON.stringify(userLocation)}').`);
                }
            } catch (locationError) {
                // This catch is specifically for errors from GetUserLocation(), e.g., if user is not authenticated.
                console.log(`UTILS/getLocalCurrencyData: Could not retrieve user location (user might be public or error occurred): ${locationError instanceof Error ? locationError.message : String(locationError)}.`);
            }
        }
        // 3. If a country code was derived (from user or GeoIP), try to find a currency for it
        if (derivedCountryCode) {
            const countryCodeSuffix = `-${derivedCountryCode}`;
            const potentialRateByLocation = await prisma.cachedExchangeRate.findFirst({
                where: { locale: { endsWith: countryCodeSuffix }, baseCurrencyCode: baseCurrencyCode },
                select: { targetCurrencyCode: true }
            });

            if (potentialRateByLocation && potentialRateByLocation.targetCurrencyCode) {
                console.log(`UTILS/getLocalCurrencyData: Found potential target currency ${potentialRateByLocation.targetCurrencyCode} based on derived country ${derivedCountryCode}.`);
                currentTargetCurrencyCode = potentialRateByLocation.targetCurrencyCode;
            } else {
                console.log(`UTILS/getLocalCurrencyData: No specific currency found for derived country ${derivedCountryCode}. Proceeding with ${currentTargetCurrencyCode}.`);
            }
        }
    }

    // 1. Check database for cached rate
    // No CACHE_DURATION_HOURS check here, as we assume the table is updated by a separate daily job.
    // We simply return what's in the table.
    const cachedRate = await prisma.cachedExchangeRate.findUnique({
      where: {
        unique_currency_pair: {
          baseCurrencyCode: baseCurrencyCode,
          targetCurrencyCode: currentTargetCurrencyCode, // Use the potentially updated target
        },
      },
    });
    if (cachedRate) {
      console.log(`UTILS/getLocalCurrencyData: Using cached rate for ${currentTargetCurrencyCode} from DB. Rate: ${cachedRate.exchangeRate}, Symbol: ${cachedRate.currencySymbol}, Locale: ${cachedRate.locale}`);
      return {
        exchangeRate: cachedRate.exchangeRate,
        currencySymbol: cachedRate.currencySymbol,
        currencyCode: cachedRate.targetCurrencyCode,
        locale: cachedRate.locale,
      };
    }

    // If not found in DB cache after all checks
    console.warn(`Rate for ${currentTargetCurrencyCode} (base: ${baseCurrencyCode}) not found in DB cache. Falling back to default.`);
    return await getMockData(currentTargetCurrencyCode, baseCurrencyCode);

  } catch (error) {
    console.error("Error during currency data retrieval:", error);
    // If any error occurs (DB read, GetUserLocation, etc.), fall back.
    // currentTargetCurrencyCode will be its value before the error, or the initial targetCurrencyCode.
    console.warn(`Error occurred. Falling back to default for initially intended or derived target: ${currentTargetCurrencyCode}.`);
    return await getMockData(currentTargetCurrencyCode, baseCurrencyCode); // Ultimate fallback
  }
}

// Helper function for mock data to keep the main function cleaner
async function getMockData(targetCurrencyCode: string, baseCurrencyCode: string = "USD"): Promise<CurrencyData> {
  console.warn(`Attempting to use fallback data for ${targetCurrencyCode} from cache or hardcoded default.`);
  try {
    // In this new model, getMockData will not try to read from the DB again,
    // as getLocalCurrencyData already did. It directly provides a hardcoded default.
    // The previous attempt to read from DB in getMockData is removed.
  } catch (dbError) {
    // This catch block is unlikely to be hit now, but kept for safety.
    console.error("Unexpected error in getMockData (should not query DB anymore):", dbError);
  }
  console.warn(`Providing hardcoded USD default as ultimate fallback for ${targetCurrencyCode}.`);
  return {
    exchangeRate: 1,
    currencySymbol: "$",
    currencyCode: "USD",
    locale: "en-US",
  };
}
