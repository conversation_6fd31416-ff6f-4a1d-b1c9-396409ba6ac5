import { ResumeDataSchema, ResumeDataSchemaType } from "@/data/zod/resumeZod";
import { safeDecrypt } from "./security/safeDecrypt";
import { transformParsedDataToResumeSchema } from "@/data/zod/resumeTransformer";

export function safeParseDecryptedResume(
    encrypted: string | Buffer | null
): ResumeDataSchemaType | null {
    
    const decrypted = safeDecrypt(encrypted);

    if (!decrypted) return null;
  
    try {
        const parsed = JSON.parse(decrypted);

        // If parsed data is null, or an empty object/array after JSON.parse,
        // treat it as if there's no resume data to process.
        if (parsed === null || (typeof parsed === 'object' && Object.keys(parsed).length === 0) || (Array.isArray(parsed) && parsed.length === 0)) {
            return null;
        }

        const transformedData = transformParsedDataToResumeSchema(parsed);
        
        const result = ResumeDataSchema.safeParse(transformedData);
        
        if (!result.success) {
            console.error('Schema validation failed:', result.error);
        }
        
        return result.success ? result.data : null;
    } catch (err) {
        console.error("Failed to parse decrypted resume data:", err);
        return null;
    }
  }
