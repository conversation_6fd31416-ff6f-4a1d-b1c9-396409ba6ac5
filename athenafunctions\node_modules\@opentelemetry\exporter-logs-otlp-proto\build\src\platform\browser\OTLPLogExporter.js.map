{"version": 3, "file": "OTLPLogExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPLogExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAEH,0EAG2C;AAC3C,sEAAyE;AAGzE,iFAAuG;AAEvG;;GAEG;AACH,MAAa,eACX,SAAQ,qCAAqC;IAG7C,YAAY,SAAiC,EAAE;QAC7C,KAAK,CACH,IAAA,oDAAqC,EACnC,MAAM,EACN,yCAAsB,EACtB,SAAS,EACT,EAAE,cAAc,EAAE,wBAAwB,EAAE,CAC7C,CACF,CAAC;IACJ,CAAC;CACF;AAdD,0CAcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport {\n  OTLPExporterConfigBase,\n  OTLPExporterBase,\n} from '@opentelemetry/otlp-exporter-base';\nimport { ProtobufLogsSerializer } from '@opentelemetry/otlp-transformer';\n\nimport { ReadableLogRecord, LogRecordExporter } from '@opentelemetry/sdk-logs';\nimport { createLegacyOtlpBrowserExportDelegate } from '@opentelemetry/otlp-exporter-base/browser-http';\n\n/**\n * Collector Trace Exporter for Web\n */\nexport class OTLPLogExporter\n  extends OTLPExporterBase<ReadableLogRecord[]>\n  implements LogRecordExporter\n{\n  constructor(config: OTLPExporterConfigBase = {}) {\n    super(\n      createLegacyOtlpBrowserExportDelegate(\n        config,\n        ProtobufLogsSerializer,\n        'v1/logs',\n        { 'Content-Type': 'application/x-protobuf' }\n      )\n    );\n  }\n}\n"]}