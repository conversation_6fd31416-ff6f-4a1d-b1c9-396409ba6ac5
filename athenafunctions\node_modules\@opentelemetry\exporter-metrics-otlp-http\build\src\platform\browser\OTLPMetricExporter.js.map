{"version": 3, "file": "OTLPMetricExporter.js", "sourceRoot": "", "sources": ["../../../../src/platform/browser/OTLPMetricExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,yEAAsE;AAEtE,sEAAwE;AACxE,iFAAuG;AAEvG;;GAEG;AACH,MAAa,kBAAmB,SAAQ,+CAAsB;IAC5D,YAAY,MAA2D;QACrE,KAAK,CACH,IAAA,oDAAqC,EACnC,MAAM,aAAN,MAAM,cAAN,MAAM,GAAI,EAAE,EACZ,wCAAqB,EACrB,YAAY,EACZ,EAAE,cAAc,EAAE,kBAAkB,EAAE,CACvC,EACD,MAAM,CACP,CAAC;IACJ,CAAC;CACF;AAZD,gDAYC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { OTLPMetricExporterOptions } from '../../OTLPMetricExporterOptions';\nimport { OTLPMetricExporterBase } from '../../OTLPMetricExporterBase';\nimport { OTLPExporterConfigBase } from '@opentelemetry/otlp-exporter-base';\nimport { JsonMetricsSerializer } from '@opentelemetry/otlp-transformer';\nimport { createLegacyOtlpBrowserExportDelegate } from '@opentelemetry/otlp-exporter-base/browser-http';\n\n/**\n * Collector Metric Exporter for Web\n */\nexport class OTLPMetricExporter extends OTLPMetricExporterBase {\n  constructor(config?: OTLPExporterConfigBase & OTLPMetricExporterOptions) {\n    super(\n      createLegacyOtlpBrowserExportDelegate(\n        config ?? {},\n        JsonMetricsSerializer,\n        'v1/metrics',\n        { 'Content-Type': 'application/json' }\n      ),\n      config\n    );\n  }\n}\n"]}