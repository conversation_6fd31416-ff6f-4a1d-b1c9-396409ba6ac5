import { ResumeData } from "@/data/zod/resumeZod";
import { SVGProps } from "react";

export type IconSvgProps = SVGProps<SVGSVGElement> & {
    size?: number;
};

export type Company = {
    id: string;
    userId: string;
    name: string;
    code?: number;
    tin?: string | null;
    website?: string | null;
    createdDate?: string | null;
    addedById?: string | null;
};

export type UserFile = {
    id: string;
    userId: string;
    companyId?: string;
    fileUse: string; // resume, requirement, etc
    fileType: string; // pdf, docx, etc
    fileName: string;
    description: string;
    key: string;
    url: string;
    fileSize: number;
    postType?: number; // for job file type, whether set status to active or draft
};

export type UserPlan = {
    id: string;
    userId: string;
    companyId?: string;
    processor?: string;
    subscriptionId?: string;
    planId: string;
    amount: number;
    paymentFrequency: string;
    exchangeRate: number;
    exchangeCurrencyCode: string;
    exchangeCurrencySymbol: string;
    userLocale: string;
    baseCurrencyCode: string;
    baseCurrencySymbol: string;
};

export type CandidateResume = {
    id: string;
    userId?: string;
    companyId?: string;
    fileId: string;
    username?: string; 
    name?: string;
    title?: string;
    about?: string;
    email?: string;
    phone?: string;
    website?: string;
    xaccount?: string;
    linkedin?: string;
    github?: string;
    address?: string;
    picture?: string;
    fileContent?: string;
    resumeData?: ResumeData;
    promptMarkdown?: string;
    structured?: Record<string, string | string[] | object[]>;
    metadata?: Record<string, string>;
    status?: string;
    createdAt?: Date;
    updatedAt?: Date;
};

export type ResumeSectionValue = string | string[] | object[];
export type ResumeSections = Record<string, ResumeSectionValue>;


export type JobDescPost = {
    id: string;
    userId: string;
    companyId?: string;
    fileId: string;
    jobTitle: string; 
    employmentType: string;
    experienceLevel?: string;
    country: string;
    location?: string;
    department?: string;
    salaryCurrency?: string;
    salaryFrom?: number;
    salaryTo?: number;
    jobDescription: string;
    listingDuration: number;
    interviewType: string;
    localRemoteWork: boolean;
    overseasRemoteWork: boolean;
    skills: string[];
    languageRequirements: string[];
    tags: string[];
    status: string;
    rawText?: string;
    promptMarkdown?: string;
    structured?: Record<string, string | string[] | object[]>;
    metadata?: Record<string, string>;
    expiresAt?: Date;
    createdAt?: Date;
    updatedAt?: Date;
};